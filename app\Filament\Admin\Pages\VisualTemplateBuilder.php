<?php

namespace App\Filament\Admin\Pages;

use App\Models\TemplateLayout;
use App\Models\LayoutSection;
use App\Models\ContentBlock;
use App\Models\BlockTemplate;
use App\Models\Company;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Log;

class VisualTemplateBuilder extends Page implements HasForms
{
    use InteractsWithForms;
    protected static ?string $navigationIcon = 'heroicon-o-paint-brush';
    protected static ?string $navigationLabel = 'Visual Builder v2.0';
    protected static ?string $navigationGroup = 'Template Management';
    protected static string $view = 'filament.admin.pages.visual-template-builder';
    protected static ?int $navigationSort = 2;

    // Page properties
    public ?TemplateLayout $template = null;
    public array $templateData = [];
    public array $availableBlocks = [];
    public array $companies = [];
    public string $selectedCompanyId = '';
    public string $previewMode = 'desktop';
    public string $pageSize = 'legal';

    // Section settings
    public array $sectionSettings = [
        'section_name' => '',
        'column_count' => 1,
        'column_layout' => 'equal',
        'column_widths' => [],
        'background_color' => '',
        'min_height' => 100,
    ];

    // Block settings
    public array $blockSettings = [
        'block_name' => '',
        'alignment' => 'left',
        'font_size' => 12,
        'font_weight' => 'normal',
        'font_color' => '#000000',
    ];

    // Selected element for properties modal
    public ?int $selectedElementId = null;
    public string $selectedElementType = ''; // 'section' or 'block'

    // Modal states
    public bool $showSectionModal = false;
    public bool $showBlockModal = false;
    public bool $showImageModal = false;

    public function mount(): void
    {

        // Get template ID from URL parameter
        $templateId = Request::get('template');

        if ($templateId) {
            $this->template = TemplateLayout::with(['sections.blocks'])->find($templateId);
            if ($this->template) {
                $this->templateData = $this->template->layout_data ?? [];
                $this->pageSize = $this->template->page_size ?? 'legal';
                $this->selectedCompanyId = (string) $this->template->company_id;
            }
        }

        $this->loadAvailableBlocks();
        $this->loadCompanies();
    }

    protected function loadAvailableBlocks(): void
    {
        $this->availableBlocks = BlockTemplate::active()
            ->orderBy('category')
            ->orderBy('usage_count', 'desc')
            ->get()
            ->groupBy('category')
            ->map(function ($blocks, $category) {
                return [
                    'category' => $category,
                    'label' => ucfirst($category),
                    'blocks' => $blocks->map(function ($block) {
                        return [
                            'id' => $block->id,
                            'name' => $block->name,
                            'description' => $block->description,
                            'block_type' => $block->block_type,
                            'preview_html' => $block->getPreviewHtml(),
                            'tags' => $block->tags ?? [],
                        ];
                    })->toArray()
                ];
            })
            ->values()
            ->toArray();
    }

    protected function loadCompanies(): void
    {
        $this->companies = Company::select('id', 'name')
            ->orderBy('name')
            ->get()
            ->map(function ($company) {
                return [
                    'id' => $company->id,
                    'name' => $company->name,
                ];
            })
            ->toArray();
    }

    public function getTitle(): string
    {
        if ($this->template) {
            return '🎨 Visual Builder: ' . $this->template->name;
        }
        return '🎨 Visual Template Builder v2.0';
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('new_template')
                ->label('New Template')
                ->icon('heroicon-o-plus')
                ->color('success')
                ->action('createNewTemplate')
                ->visible(fn () => $this->template === null),

            Action::make('save')
                ->label('Save Template')
                ->icon('heroicon-o-check')
                ->color('success')
                ->action('saveTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('preview')
                ->label('Preview')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->action('previewTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('duplicate')
                ->label('Duplicate')
                ->icon('heroicon-o-document-duplicate')
                ->color('gray')
                ->action('duplicateTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('settings')
                ->label('Template Settings')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('gray')
                ->action('openTemplateSettings')
                ->visible(fn () => $this->template !== null),
        ];
    }

    public function createNewTemplate(): void
    {
        $this->redirect(route('filament.admin.resources.template-layouts.create'));
    }

    public function saveTemplate(): void
    {
        if (!$this->template) {
            return;
        }

        try {
            // Template will be saved via Livewire component interactions
            // Success - no notification needed

        } catch (\Exception $e) {
            // Error - log but don't show notification
            Log::error('Failed to save template: ' . $e->getMessage());
        }
    }

    public function previewTemplate(): void
    {
        if (!$this->template) {
            return;
        }

        // Generate preview URL for v2.0
        $previewUrl = route('visual-template.preview', [
            'template' => $this->template->id,
            'mode' => $this->previewMode,
            'size' => $this->pageSize
        ]);

        // Open preview in new tab
        $this->js("window.open('$previewUrl', '_blank')");
    }

    public function duplicateTemplate(): void
    {
        if (!$this->template) {
            return;
        }

        try {
            $newTemplate = $this->template->duplicate();

            // Redirect to edit the new template
            $this->redirect(route('filament.admin.pages.visual-template-builder', ['template' => $newTemplate->id]));

        } catch (\Exception $e) {
            Log::error('Failed to duplicate template: ' . $e->getMessage());
        }
    }

    public function openTemplateSettings(): void
    {
        if ($this->template) {
            $this->redirect(route('filament.admin.resources.template-layouts.edit', $this->template));
        }
    }

    public function changePreviewMode(string $mode): void
    {
        $this->previewMode = $mode;
    }

    public function changePageSize(string $size): void
    {
        $this->pageSize = $size;

        if ($this->template) {
            $this->template->update(['page_size' => $size]);
        }
    }

    // Section management methods
    public function addSection(string $sectionType, ?int $order = null): void
    {
        if (!$this->template) {
            return;
        }

        try {
            LayoutSection::create([
                'template_layout_id' => $this->template->id,
                'section_type' => $sectionType,
                'order' => $order ?? $this->getNextSectionOrder($sectionType),
            ]);

            // Refresh the page to show new section
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to add section: ' . $e->getMessage());
        }
    }

    private function getNextSectionOrder(string $sectionType): int
    {
        if (!$this->template) {
            return 0;
        }

        return $this->template->sections()
            ->where('section_type', $sectionType)
            ->max('order') + 1;
    }

    // Add block to section
    public function addBlockToSection(int $blockTemplateId, int $sectionId, int $columnPosition = 1): void
    {
        try {
            $blockTemplate = BlockTemplate::find($blockTemplateId);
            $section = LayoutSection::find($sectionId);

            if (!$blockTemplate || !$section) {
                return;
            }

            // Create content block from template
            $blockTemplate->createContentBlock($section->id, [
                'column_position' => $columnPosition,
                'order' => $section->getNextBlockOrder($columnPosition),
            ]);

            // Refresh the page to show new block
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to add block: ' . $e->getMessage());
        }
    }

    // Delete section
    public function deleteSection(int $sectionId): void
    {
        try {
            $section = LayoutSection::find($sectionId);

            if (!$section) {
                return;
            }

            $section->delete();

            // Refresh the page
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to delete section: ' . $e->getMessage());
        }
    }

    // Delete block
    public function deleteBlock(int $blockId): void
    {
        try {
            $block = ContentBlock::find($blockId);

            if (!$block) {
                return;
            }

            $block->delete();

            // Refresh the page
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to remove block: ' . $e->getMessage());
        }
    }

    // Auto-save section settings when changed
    public function updatedSectionSettings(): void
    {
        // Auto-save when section settings change
        if ($this->selectedElementId && $this->sectionSettings) {
            $this->autoSaveSectionSettings();
        }
    }

    // Auto-save image settings when changed
    public function updatedBlockSettings(): void
    {
        // Auto-save when block settings change (including image settings)
        if ($this->selectedElementId && $this->blockSettings) {
            $this->autoSaveImageSettings();
        }
    }

    // Auto-save section settings
    private function autoSaveSectionSettings(): void
    {
        try {
            Log::info('Auto-saving section settings', [
                'selectedElementId' => $this->selectedElementId,
                'sectionSettings' => $this->sectionSettings
            ]);

            // Validate selectedElementId
            if (!$this->selectedElementId) {
                return;
            }

            $section = LayoutSection::find($this->selectedElementId);

            if (!$section) {
                return;
            }

            Log::info('Section found', [
                'section_id' => $section->id,
                'current_column_count' => $section->column_count,
                'current_layout' => $section->column_layout
            ]);

            // Validate and prepare update data
            $columnCount = (int) ($this->sectionSettings['column_count'] ?? $section->column_count);
            $columnLayout = $this->sectionSettings['column_layout'] ?? $section->column_layout;
            $columnWidths = $this->sectionSettings['column_widths'] ?? $section->column_widths;

            // Ensure column_widths is array or null
            if (is_string($columnWidths)) {
                $columnWidths = json_decode($columnWidths, true);
            }

            $updateData = [
                'section_name' => $this->sectionSettings['section_name'] ?? $section->section_name,
                'column_count' => $columnCount,
                'column_layout' => $columnLayout,
                'column_widths' => $columnWidths,
                'background_color' => $this->sectionSettings['background_color'] ?? $section->background_color,
                'min_height' => (int) ($this->sectionSettings['min_height'] ?? $section->min_height),
            ];

            Log::info('Prepared update data', $updateData);

            // Perform update
            $updateResult = $section->update($updateData);

            Log::info('Update result', [
                'success' => $updateResult,
                'section_id' => $section->id
            ]);

            if (!$updateResult) {
                throw new \Exception('Database update failed');
            }

            // Verify update
            $section->refresh();
            Log::info('Section after update', [
                'column_count' => $section->column_count,
                'column_layout' => $section->column_layout,
                'column_widths' => $section->column_widths
            ]);

            // Section updated successfully - no notification needed

            // Emit success event
            $this->dispatch('section-updated', [
                'section_id' => $section->id,
                'column_count' => $section->column_count,
                'column_layout' => $section->column_layout,
                'auto_saved' => true
            ]);

            // Reload template data to reflect changes
            $this->reloadTemplate();

        } catch (\Exception $e) {
            Log::error('Failed to auto-save section', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'selectedElementId' => $this->selectedElementId,
                'sectionSettings' => $this->sectionSettings
            ]);

            // Don't show notification for auto-save errors (too intrusive)
            // Just emit event for debugging
            $this->dispatch('section-update-failed', [
                'error' => $e->getMessage(),
                'selectedElementId' => $this->selectedElementId,
                'auto_save' => true
            ]);
        }
    }

    // Manual update method (backup/legacy)
    public function updateSectionSettings(): void
    {
        $this->autoSaveSectionSettings();
    }

    // Load section settings for editing
    public function loadSectionSettings(int $sectionId): void
    {
        $section = LayoutSection::find($sectionId);

        if ($section) {
            $this->sectionSettings = [
                'section_name' => $section->section_name ?? '',
                'column_count' => $section->column_count,
                'column_layout' => $section->column_layout ?? 'equal',
                'column_widths' => $section->column_widths ?? [],
                'background_color' => $section->background_color ?? '',
                'min_height' => $section->min_height ?? 100,
            ];
        }
    }

    // Reload template data
    private function reloadTemplate(): void
    {
        if ($this->template) {
            $this->template = TemplateLayout::with(['sections.blocks'])->find($this->template->id);
        }
    }

    // Update block settings
    public function updateBlockSettings(): void
    {
        try {
            $block = ContentBlock::find($this->selectedElementId);

            if (!$block) {
                return;
            }

            // Update block settings
            $block->update([
                'block_name' => $this->blockSettings['block_name'] ?? $block->block_name,
                'configuration' => array_merge($block->configuration ?? [], [
                    'alignment' => $this->blockSettings['alignment'] ?? 'left',
                    'font_size' => (int) ($this->blockSettings['font_size'] ?? 12),
                    'font_weight' => $this->blockSettings['font_weight'] ?? 'normal',
                    'font_color' => $this->blockSettings['font_color'] ?? '#000000',
                ])
            ]);

            $this->reloadTemplate();

        } catch (\Exception $e) {
            Log::error('Failed to update block: ' . $e->getMessage());
        }
    }

    // Modal actions
    protected function getActions(): array
    {
        return [
            // Section Settings Modal
            Action::make('sectionSettings')
                ->label('Section Settings')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('gray')
                ->form([
                    \Filament\Forms\Components\Section::make('Section Configuration')
                        ->schema([
                            TextInput::make('section_name')
                                ->label('Section Name')
                                ->placeholder('Enter section name'),

                            Grid::make(2)
                                ->schema([
                                    Select::make('column_count')
                                        ->label('Column Count')
                                        ->options([
                                            1 => '1 Column',
                                            2 => '2 Columns',
                                            3 => '3 Columns',
                                            4 => '4 Columns',
                                        ])
                                        ->default(1)
                                        ->reactive(),

                                    Select::make('column_layout')
                                        ->label('Column Layout')
                                        ->options([
                                            'equal' => 'Equal Width',
                                            'custom' => 'Custom Width',
                                            'flex' => 'Flexible',
                                            'grid' => 'CSS Grid',
                                        ])
                                        ->default('equal'),
                                ]),

                            TextInput::make('min_height')
                                ->label('Minimum Height (px)')
                                ->numeric()
                                ->default(100),

                            ColorPicker::make('background_color')
                                ->label('Background Color'),
                        ]),
                ])
                ->action(function (array $data) {
                    $this->sectionSettings = $data;
                    $this->autoSaveSectionSettings();
                    $this->showSectionModal = false;
                })
                ->visible(fn () => $this->showSectionModal),

            // Block Settings Modal
            Action::make('blockSettings')
                ->label('Block Settings')
                ->icon('heroicon-o-cube')
                ->color('gray')
                ->form([
                    \Filament\Forms\Components\Section::make('Block Configuration')
                        ->schema([
                            TextInput::make('block_name')
                                ->label('Block Name')
                                ->placeholder('Enter block name'),

                            Grid::make(2)
                                ->schema([
                                    Select::make('alignment')
                                        ->label('Text Alignment')
                                        ->options([
                                            'left' => 'Left',
                                            'center' => 'Center',
                                            'right' => 'Right',
                                            'justify' => 'Justify',
                                        ])
                                        ->default('left'),

                                    TextInput::make('font_size')
                                        ->label('Font Size (pt)')
                                        ->numeric()
                                        ->default(12),
                                ]),

                            Grid::make(2)
                                ->schema([
                                    Select::make('font_weight')
                                        ->label('Font Weight')
                                        ->options([
                                            'normal' => 'Normal',
                                            'bold' => 'Bold',
                                            'lighter' => 'Lighter',
                                        ])
                                        ->default('normal'),

                                    ColorPicker::make('font_color')
                                        ->label('Font Color')
                                        ->default('#000000'),
                                ]),
                        ]),
                ])
                ->action(function (array $data) {
                    $this->blockSettings = $data;
                    $this->updateBlockSettings();
                    $this->showBlockModal = false;
                })
                ->visible(fn () => $this->showBlockModal),

            // Image Settings Modal
            Action::make('imageSettings')
                ->label('Image Settings')
                ->icon('heroicon-o-photo')
                ->color('gray')
                ->form([
                    \Filament\Forms\Components\Section::make('Image Configuration')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    TextInput::make('width')
                                        ->label('Width')
                                        ->placeholder('100px or auto')
                                        ->default('100px'),

                                    TextInput::make('height')
                                        ->label('Height')
                                        ->placeholder('auto or 100px')
                                        ->default('auto'),
                                ]),

                            Select::make('object_fit')
                                ->label('Image Fit')
                                ->options([
                                    'contain' => 'Contain (fit inside)',
                                    'cover' => 'Cover (fill area)',
                                    'fill' => 'Fill (stretch)',
                                    'scale-down' => 'Scale Down',
                                    'none' => 'None (original)',
                                ])
                                ->default('contain'),

                            TextInput::make('alt_text')
                                ->label('Alt Text')
                                ->placeholder('Image description'),
                        ]),
                ])
                ->action(function (array $data) {
                    $this->updateImageSettings($data);
                    $this->showImageModal = false;
                })
                ->visible(fn () => $this->showImageModal),
        ];
    }

    // Open modals
    public function openSectionSettings(int $sectionId): void
    {
        $this->selectedElementId = $sectionId;
        $this->selectedElementType = 'section';
        $this->loadSectionSettings($sectionId);
        $this->showSectionModal = true;
    }

    public function openBlockSettings(int $blockId): void
    {
        $this->selectedElementId = $blockId;
        $this->selectedElementType = 'block';
        $this->loadBlockSettings($blockId);
        $this->showBlockModal = true;
    }

    public function openImageSettings(int $blockId): void
    {
        $this->selectedElementId = $blockId;
        $this->selectedElementType = 'block';
        $this->loadImageSettings($blockId);
        $this->showImageModal = true;
    }

    // Load block settings
    public function loadBlockSettings(int $blockId): void
    {
        $block = ContentBlock::find($blockId);

        if ($block) {
            $config = $block->configuration ?? [];
            $this->blockSettings = [
                'block_name' => $block->block_name ?? '',
                'alignment' => $config['alignment'] ?? 'left',
                'font_size' => $config['font_size'] ?? 12,
                'font_weight' => $config['font_weight'] ?? 'normal',
                'font_color' => $config['font_color'] ?? '#000000',
            ];
        }
    }

    // Load image settings
    public function loadImageSettings(int $blockId): void
    {
        $block = ContentBlock::find($blockId);

        if ($block) {
            $contentData = $block->content_data ?? [];
            $this->blockSettings = [
                'width' => $contentData['width'] ?? '100px',
                'height' => $contentData['height'] ?? 'auto',
                'object_fit' => $contentData['object_fit'] ?? 'contain',
                'alt_text' => $contentData['alt_text'] ?? '',
            ];
        }
    }

    // Auto-save image settings
    private function autoSaveImageSettings(): void
    {
        try {
            $block = ContentBlock::find($this->selectedElementId);

            if (!$block) {
                return;
            }

            // Update content_data with image settings
            $contentData = $block->content_data ?? [];
            $contentData = array_merge($contentData, [
                'width' => $this->blockSettings['width'] ?? '100px',
                'height' => $this->blockSettings['height'] ?? 'auto',
                'object_fit' => $this->blockSettings['object_fit'] ?? 'contain',
                'alt_text' => $this->blockSettings['alt_text'] ?? '',
            ]);

            $block->update(['content_data' => $contentData]);

            $this->reloadTemplate();

        } catch (\Exception $e) {
            Log::error('Failed to auto-save image settings: ' . $e->getMessage());
        }
    }

    // Update image settings (manual save - kept for compatibility)
    public function updateImageSettings(): void
    {
        $this->autoSaveImageSettings();
    }

    // Move block up in order
    public function moveBlockUp(int $blockId): void
    {
        try {
            $block = ContentBlock::find($blockId);

            if (!$block) {
                Notification::make()
                    ->title('Error')
                    ->body('Block not found.')
                    ->danger()
                    ->send();
                return;
            }

            // Find previous block in same column
            $previousBlock = ContentBlock::where('layout_section_id', $block->layout_section_id)
                ->where('column_position', $block->column_position)
                ->where('order', '<', $block->order)
                ->orderBy('order', 'desc')
                ->first();

            if ($previousBlock) {
                // Swap orders
                $tempOrder = $block->order;
                $block->update(['order' => $previousBlock->order]);
                $previousBlock->update(['order' => $tempOrder]);

                $this->reloadTemplate();
            }

        } catch (\Exception $e) {
            Log::error('Failed to move block: ' . $e->getMessage());
        }
    }

    // Move block down in order
    public function moveBlockDown(int $blockId): void
    {
        try {
            $block = ContentBlock::find($blockId);

            if (!$block) {
                return;
            }

            // Find next block in same column
            $nextBlock = ContentBlock::where('layout_section_id', $block->layout_section_id)
                ->where('column_position', $block->column_position)
                ->where('order', '>', $block->order)
                ->orderBy('order', 'asc')
                ->first();

            if ($nextBlock) {
                // Swap orders
                $tempOrder = $block->order;
                $block->update(['order' => $nextBlock->order]);
                $nextBlock->update(['order' => $tempOrder]);

                $this->reloadTemplate();
            }

        } catch (\Exception $e) {
            Log::error('Failed to move block: ' . $e->getMessage());
        }
    }

    // Move block to different column via drag & drop
    public function moveBlockToColumn(int $blockId, int $sectionId, int $columnId): void
    {
        try {
            $block = ContentBlock::find($blockId);

            if (!$block) {
                return;
            }

            // Get the highest order in target column
            $maxOrder = ContentBlock::where('layout_section_id', $sectionId)
                ->where('column_position', $columnId)
                ->max('order') ?? 0;

            // Update block position
            $block->update([
                'layout_section_id' => $sectionId,
                'column_position' => $columnId,
                'order' => $maxOrder + 1
            ]);

            $this->reloadTemplate();

        } catch (\Exception $e) {
            Log::error('Failed to move block: ' . $e->getMessage());
        }
    }

    // Get data for frontend Vue.js component
    public function getBuilderData(): array
    {
        return [
            'template' => $this->template?->toArray(),
            'templateData' => $this->templateData,
            'availableBlocks' => $this->availableBlocks,
            'companies' => $this->companies,
            'selectedCompanyId' => $this->selectedCompanyId,
            'previewMode' => $this->previewMode,
            'pageSize' => $this->pageSize,
            'sections' => $this->template?->sections()->with('blocks')->get()->toArray() ?? [],
        ];
    }
}
