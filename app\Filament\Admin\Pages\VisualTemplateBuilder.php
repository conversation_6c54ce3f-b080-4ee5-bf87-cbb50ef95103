<?php

namespace App\Filament\Admin\Pages;

use App\Models\TemplateLayout;
use App\Models\LayoutSection;
use App\Models\ContentBlock;
use App\Models\BlockTemplate;
use App\Models\Company;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Log;

class VisualTemplateBuilder extends Page implements HasForms
{
    use InteractsWithForms;
    protected static ?string $navigationIcon = 'heroicon-o-paint-brush';
    protected static ?string $navigationLabel = 'Visual Builder v2.0';
    protected static ?string $navigationGroup = 'Template Management';
    protected static string $view = 'filament.admin.pages.visual-template-builder';
    protected static ?int $navigationSort = 2;

    // Page properties
    public ?TemplateLayout $template = null;
    public array $templateData = [];
    public array $availableBlocks = [];
    public array $companies = [];
    public string $selectedCompanyId = '';
    public string $previewMode = 'desktop';
    public string $pageSize = 'legal';

    // Section settings
    public array $sectionSettings = [
        'section_name' => '',
        'column_count' => 1,
        'column_layout' => 'equal',
        'column_widths' => [],
        'layout_type' => 'table',
        'table_rows' => 1,
        'background_color' => '',
        'min_height' => 100,
    ];

    // Block settings
    public array $blockSettings = [
        'block_name' => '',
        'alignment' => 'left',
        'font_size' => 12,
        'font_weight' => 'normal',
        'font_color' => '#000000',
    ];

    // Selected element for properties modal
    public ?int $selectedElementId = null;
    public string $selectedElementType = ''; // 'section' or 'block'

    // Modal states
    public bool $showSectionModal = false;
    public bool $showBlockModal = false;
    public bool $showImageModal = false;

    public function mount(): void
    {

        // Get template ID from URL parameter
        $templateId = Request::get('template');

        if ($templateId) {
            $this->template = TemplateLayout::with(['sections.blocks'])->find($templateId);
            if ($this->template) {
                $this->templateData = $this->template->layout_data ?? [];
                $this->pageSize = $this->template->page_size ?? 'legal';
                $this->selectedCompanyId = (string) $this->template->company_id;
            }
        }

        $this->loadAvailableBlocks();
        $this->loadCompanies();
    }

    protected function loadAvailableBlocks(): void
    {
        $this->availableBlocks = BlockTemplate::active()
            ->orderBy('category')
            ->orderBy('usage_count', 'desc')
            ->get()
            ->groupBy('category')
            ->map(function ($blocks, $category) {
                return [
                    'category' => $category,
                    'label' => ucfirst($category),
                    'blocks' => $blocks->map(function ($block) {
                        return [
                            'id' => $block->id,
                            'name' => $block->name,
                            'description' => $block->description,
                            'block_type' => $block->block_type,
                            'preview_html' => $block->getPreviewHtml(),
                            'tags' => $block->tags ?? [],
                        ];
                    })->toArray()
                ];
            })
            ->values()
            ->toArray();
    }

    protected function loadCompanies(): void
    {
        $this->companies = Company::select('id', 'name')
            ->orderBy('name')
            ->get()
            ->map(function ($company) {
                return [
                    'id' => $company->id,
                    'name' => $company->name,
                ];
            })
            ->toArray();
    }

    public function getTitle(): string
    {
        if ($this->template) {
            return '🎨 Visual Builder: ' . $this->template->name;
        }
        return '🎨 Visual Template Builder v2.0';
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('new_template')
                ->label('New Template')
                ->icon('heroicon-o-plus')
                ->color('success')
                ->action('createNewTemplate')
                ->visible(fn () => $this->template === null),

            Action::make('save')
                ->label('Save Template')
                ->icon('heroicon-o-check')
                ->color('success')
                ->action('saveTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('preview')
                ->label('Preview')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->action('previewTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('duplicate')
                ->label('Duplicate')
                ->icon('heroicon-o-document-duplicate')
                ->color('gray')
                ->action('duplicateTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('settings')
                ->label('Template Settings')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('gray')
                ->action('openTemplateSettings')
                ->visible(fn () => $this->template !== null),
        ];
    }

    public function createNewTemplate(): void
    {
        $this->redirect(route('filament.admin.resources.template-layouts.create'));
    }

    public function saveTemplate(): void
    {
        if (!$this->template) {
            return;
        }

        try {
            // Template will be saved via Livewire component interactions
            // Success - no notification needed

        } catch (\Exception $e) {
            // Error - log but don't show notification
            Log::error('Failed to save template: ' . $e->getMessage());
        }
    }

    public function previewTemplate(): void
    {
        if (!$this->template) {
            return;
        }

        // Force reload template to get latest data
        $this->reloadTemplate();

        // Generate preview URL for v2.0 with cache buster
        $previewUrl = route('visual-template.preview', [
            'template' => $this->template->id,
            'mode' => $this->previewMode,
            'size' => $this->pageSize,
            '_t' => time() // Cache buster
        ]);

        // Open preview in new tab
        $this->js("window.open('$previewUrl', '_blank')");
    }

    public function duplicateTemplate(): void
    {
        if (!$this->template) {
            return;
        }

        try {
            $newTemplate = $this->template->duplicate();

            // Redirect to edit the new template
            $this->redirect(route('filament.admin.pages.visual-template-builder', ['template' => $newTemplate->id]));

        } catch (\Exception $e) {
            Log::error('Failed to duplicate template: ' . $e->getMessage());
        }
    }

    public function openTemplateSettings(): void
    {
        if ($this->template) {
            $this->redirect(route('filament.admin.resources.template-layouts.edit', $this->template));
        }
    }

    public function changePreviewMode(string $mode): void
    {
        $this->previewMode = $mode;
    }

    public function changePageSize(string $size): void
    {
        $this->pageSize = $size;

        if ($this->template) {
            $this->template->update(['page_size' => $size]);
        }
    }

    // Section management methods
    public function addSection(string $sectionType, ?int $order = null): void
    {
        if (!$this->template) {
            Log::error('No template found for addSection');
            Notification::make()
                ->title('Error')
                ->body('No template selected')
                ->danger()
                ->send();
            return;
        }

        try {
            Log::info('Adding section', [
                'template_id' => $this->template->id,
                'section_type' => $sectionType,
                'order' => $order
            ]);

            // Create new section with table layout
            $section = LayoutSection::create([
                'template_layout_id' => $this->template->id,
                'section_type' => $sectionType,
                'section_name' => ucfirst($sectionType) . ' Section',
                'column_count' => 1,
                'table_rows' => 1,
                'layout_type' => 'table',
                'column_layout' => 'equal',
                'order' => $order ?? $this->getNextSectionOrder($sectionType),
                'is_active' => true,
                'min_height' => 80,
            ]);

            Log::info('Section created', ['section_id' => $section->id]);

            // Create initial empty cell for the table
            $cell = ContentBlock::create([
                'layout_section_id' => $section->id,
                'block_type' => 'empty',
                'block_name' => 'Cell 1,1',
                'column_position' => 1,
                'row_position' => 1,
                'order' => 1,
                'rowspan' => 1,
                'colspan' => 1,
                'vertical_align' => 'top',
                'content_data' => [],
                'field_mapping' => [],
                'styling' => [],
            ]);

            Log::info('Cell created', ['cell_id' => $cell->id]);

            Notification::make()
                ->title('Success')
                ->body(ucfirst($sectionType) . ' section added successfully')
                ->success()
                ->send();

            // Refresh the page to show new section
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to add section: ' . $e->getMessage());
            Log::error('Error details: ' . $e->getTraceAsString());

            Notification::make()
                ->title('Error')
                ->body('Failed to add section: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    private function getNextSectionOrder(string $sectionType): int
    {
        if (!$this->template) {
            return 0;
        }

        return $this->template->sections()
            ->where('section_type', $sectionType)
            ->max('order') + 1;
    }

    // Add block to section
    public function addBlockToSection(int $blockTemplateId, int $sectionId, int $columnPosition = 1): void
    {
        try {
            $blockTemplate = BlockTemplate::find($blockTemplateId);
            $section = LayoutSection::find($sectionId);

            if (!$blockTemplate || !$section) {
                return;
            }

            // Create content block from template
            $blockTemplate->createContentBlock($section->id, [
                'column_position' => $columnPosition,
                'order' => $section->getNextBlockOrder($columnPosition),
            ]);

            // Refresh the page to show new block
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to add block: ' . $e->getMessage());
        }
    }

    // Add field to table cell (modern drag & drop)
    public function addFieldToCell(int $blockTemplateId, int $sectionId, int $row, int $col): void
    {
        try {
            $blockTemplate = BlockTemplate::find($blockTemplateId);
            $section = LayoutSection::find($sectionId);

            if (!$blockTemplate || !$section) {
                return;
            }

            // Find or create the cell at the specified position
            $cell = $section->getCellAt($row, $col);

            if (!$cell) {
                // Create new cell if it doesn't exist
                $cell = ContentBlock::create([
                    'layout_section_id' => $section->id,
                    'block_type' => 'empty',
                    'block_name' => "Cell {$row},{$col}",
                    'column_position' => $col,
                    'row_position' => $row,
                    'order' => ($row - 1) * ($section->column_count ?? 1) + $col,
                    'rowspan' => 1,
                    'colspan' => 1,
                    'vertical_align' => 'top',
                    'content_data' => [],
                    'field_mapping' => [],
                    'styling' => [],
                ]);
            }

            // Update the cell with field content
            $cell->update([
                'block_type' => $blockTemplate->block_type,
                'block_name' => $blockTemplate->name,
                'content_data' => $blockTemplate->template_data ?? [],
                'field_mapping' => $blockTemplate->field_mappings ?? [],
                'styling' => $blockTemplate->default_styling ?? [],
            ]);

            // Update block template usage count
            $blockTemplate->increment('usage_count');

            // Refresh the page to show updated cell
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to add field to cell: ' . $e->getMessage());
        }
    }

    // Add block to table cell (for table layout)
    public function addBlockToCell(int $blockTemplateId, int $cellId): void
    {
        try {
            $blockTemplate = BlockTemplate::find($blockTemplateId);
            $cell = ContentBlock::find($cellId);

            if (!$blockTemplate || !$cell) {
                return;
            }

            // Update the empty cell with block content
            $cell->update([
                'block_type' => $blockTemplate->block_type,
                'block_name' => $blockTemplate->name,
                'content_data' => $blockTemplate->default_content,
                'field_mapping' => $blockTemplate->field_mapping,
                'styling' => $blockTemplate->default_styling,
            ]);

            // Update block template usage count
            $blockTemplate->increment('usage_count');

            // Refresh the page to show updated cell
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to add block to cell: ' . $e->getMessage());
        }
    }

    // Add table row
    public function addTableRow(int $sectionId): void
    {
        try {
            $section = LayoutSection::find($sectionId);
            if (!$section) return;

            $newRowNumber = ($section->table_rows ?? 1) + 1;
            $columnCount = $section->column_count ?? 1;

            // Update section row count
            $section->update(['table_rows' => $newRowNumber]);

            // Create new cells for the new row
            for ($col = 1; $col <= $columnCount; $col++) {
                ContentBlock::create([
                    'layout_section_id' => $section->id,
                    'block_type' => 'empty',
                    'block_name' => "Cell {$newRowNumber},{$col}",
                    'column_position' => $col,
                    'row_position' => $newRowNumber,
                    'order' => ($newRowNumber - 1) * $columnCount + $col,
                    'rowspan' => 1,
                    'colspan' => 1,
                    'vertical_align' => 'top',
                    'content_data' => [],
                    'field_mapping' => [],
                    'styling' => [],
                ]);
            }

            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to add table row: ' . $e->getMessage());
        }
    }

    // Add table column
    public function addTableColumn(int $sectionId): void
    {
        try {
            $section = LayoutSection::find($sectionId);
            if (!$section) return;

            $newColumnNumber = ($section->column_count ?? 1) + 1;
            $rowCount = $section->table_rows ?? 1;

            // Update section column count
            $section->update(['column_count' => $newColumnNumber]);

            // Create new cells for the new column
            for ($row = 1; $row <= $rowCount; $row++) {
                ContentBlock::create([
                    'layout_section_id' => $section->id,
                    'block_type' => 'empty',
                    'block_name' => "Cell {$row},{$newColumnNumber}",
                    'column_position' => $newColumnNumber,
                    'row_position' => $row,
                    'order' => ($row - 1) * $newColumnNumber + $newColumnNumber,
                    'rowspan' => 1,
                    'colspan' => 1,
                    'vertical_align' => 'top',
                    'content_data' => [],
                    'field_mapping' => [],
                    'styling' => [],
                ]);
            }

            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to add table column: ' . $e->getMessage());
        }
    }

    // Split cell into rows
    public function splitCellRow(int $cellId): void
    {
        try {
            $cell = ContentBlock::find($cellId);
            if (!$cell) return;

            $section = $cell->layoutSection;

            // Create new cell below the current one
            $newCell = ContentBlock::create([
                'layout_section_id' => $section->id,
                'block_type' => 'empty',
                'block_name' => "Cell " . ($cell->row_position + 1) . ",{$cell->column_position}",
                'column_position' => $cell->column_position,
                'row_position' => $cell->row_position + 1,
                'order' => $cell->order + $section->column_count,
                'rowspan' => 1,
                'colspan' => $cell->colspan,
                'vertical_align' => 'top',
                'content_data' => [],
                'field_mapping' => [],
                'styling' => [],
            ]);

            // Update section row count if needed
            if ($cell->row_position + 1 > $section->table_rows) {
                $section->update(['table_rows' => $cell->row_position + 1]);
            }

            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to split cell row: ' . $e->getMessage());
        }
    }

    // Split cell into columns
    public function splitCellColumn(int $cellId): void
    {
        try {
            $cell = ContentBlock::find($cellId);
            if (!$cell) return;

            $section = $cell->layoutSection;

            // Create new cell to the right of the current one
            $newCell = ContentBlock::create([
                'layout_section_id' => $section->id,
                'block_type' => 'empty',
                'block_name' => "Cell {$cell->row_position}," . ($cell->column_position + 1),
                'column_position' => $cell->column_position + 1,
                'row_position' => $cell->row_position,
                'order' => $cell->order + 1,
                'rowspan' => $cell->rowspan,
                'colspan' => 1,
                'vertical_align' => 'top',
                'content_data' => [],
                'field_mapping' => [],
                'styling' => [],
            ]);

            // Update section column count if needed
            if ($cell->column_position + 1 > $section->column_count) {
                $section->update(['column_count' => $cell->column_position + 1]);
            }

            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to split cell column: ' . $e->getMessage());
        }
    }

    // Delete section
    public function deleteSection(int $sectionId): void
    {
        try {
            $section = LayoutSection::find($sectionId);

            if (!$section) {
                return;
            }

            $section->delete();

            // Refresh the page
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to delete section: ' . $e->getMessage());
        }
    }

    // Delete block
    public function deleteBlock(int $blockId): void
    {
        try {
            $block = ContentBlock::find($blockId);

            if (!$block) {
                return;
            }

            $block->delete();

            // Refresh the page
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to remove block: ' . $e->getMessage());
        }
    }

    // Auto-save section settings when changed
    public function updatedSectionSettings(): void
    {
        // Auto-save when section settings change
        if ($this->selectedElementId && $this->sectionSettings) {
            $this->autoSaveSectionSettings();
        }
    }

    // Auto-save block settings when changed
    public function updatedBlockSettings(): void
    {
        // Auto-save when block settings change
        if ($this->selectedElementId && $this->blockSettings) {
            // Check if this is image settings or block settings
            if (isset($this->blockSettings['width']) || isset($this->blockSettings['height']) ||
                isset($this->blockSettings['object_fit']) || isset($this->blockSettings['alt_text'])) {
                $this->autoSaveImageSettings();
            } else {
                $this->autoSaveBlockSettings();
            }
        }
    }

    // Auto-save section settings
    private function autoSaveSectionSettings(): void
    {
        try {
            Log::info('Auto-saving section settings', [
                'selectedElementId' => $this->selectedElementId,
                'sectionSettings' => $this->sectionSettings
            ]);

            // Validate selectedElementId
            if (!$this->selectedElementId) {
                return;
            }

            $section = LayoutSection::find($this->selectedElementId);

            if (!$section) {
                return;
            }

            Log::info('Section found', [
                'section_id' => $section->id,
                'current_column_count' => $section->column_count,
                'current_layout' => $section->column_layout
            ]);

            // Validate and prepare update data
            $columnCount = (int) ($this->sectionSettings['column_count'] ?? $section->column_count);
            $columnLayout = $this->sectionSettings['column_layout'] ?? $section->column_layout;
            $columnWidths = $this->sectionSettings['column_widths'] ?? $section->column_widths;

            // Ensure column_widths is array or null
            if (is_string($columnWidths)) {
                $columnWidths = json_decode($columnWidths, true);
            }

            $updateData = [
                'section_name' => $this->sectionSettings['section_name'] ?? $section->section_name,
                'column_count' => $columnCount,
                'column_layout' => $columnLayout,
                'column_widths' => $columnWidths,
                'layout_type' => $this->sectionSettings['layout_type'] ?? $section->layout_type ?? 'table',
                'table_rows' => (int) ($this->sectionSettings['table_rows'] ?? $section->table_rows ?? 1),
                'background_color' => $this->sectionSettings['background_color'] ?? $section->background_color,
                'min_height' => (int) ($this->sectionSettings['min_height'] ?? $section->min_height),
            ];

            Log::info('Prepared update data', $updateData);

            // Handle layout type change
            $oldLayoutType = $section->layout_type;
            $newLayoutType = $updateData['layout_type'];

            // Perform update
            $updateResult = $section->update($updateData);

            Log::info('Update result', [
                'success' => $updateResult,
                'section_id' => $section->id
            ]);

            if (!$updateResult) {
                throw new \Exception('Database update failed');
            }

            // If layout type changed to table, create table cells
            if ($oldLayoutType !== 'table' && $newLayoutType === 'table') {
                $this->createTableCells($section);
            }

            // Verify update
            $section->refresh();
            Log::info('Section after update', [
                'column_count' => $section->column_count,
                'column_layout' => $section->column_layout,
                'column_widths' => $section->column_widths
            ]);

            // Section updated successfully - no notification needed

            // Emit success event
            $this->dispatch('section-updated', [
                'section_id' => $section->id,
                'column_count' => $section->column_count,
                'column_layout' => $section->column_layout,
                'auto_saved' => true
            ]);

            // Reload template data to reflect changes
            $this->reloadTemplate();

        } catch (\Exception $e) {
            Log::error('Failed to auto-save section', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'selectedElementId' => $this->selectedElementId,
                'sectionSettings' => $this->sectionSettings
            ]);

            // Don't show notification for auto-save errors (too intrusive)
            // Just emit event for debugging
            $this->dispatch('section-update-failed', [
                'error' => $e->getMessage(),
                'selectedElementId' => $this->selectedElementId,
                'auto_save' => true
            ]);
        }
    }

    // Manual update method (backup/legacy)
    public function updateSectionSettings(): void
    {
        $this->autoSaveSectionSettings();
    }

    // Load section settings for editing
    public function loadSectionSettings(int $sectionId): void
    {
        $section = LayoutSection::find($sectionId);

        if ($section) {
            $this->sectionSettings = [
                'section_name' => $section->section_name ?? '',
                'column_count' => $section->column_count,
                'column_layout' => $section->column_layout ?? 'equal',
                'column_widths' => $section->column_widths ?? [],
                'layout_type' => $section->layout_type ?? 'table',
                'table_rows' => $section->table_rows ?? 1,
                'background_color' => $section->background_color ?? '',
                'min_height' => $section->min_height ?? 100,
            ];
        }
    }

    // Load cell settings for editing
    public function loadCellSettings(int $cellId): void
    {
        $cell = ContentBlock::find($cellId);

        if ($cell) {
            $cellStyling = $cell->cell_styling ?? [];

            $this->blockSettings = [
                'block_name' => $cell->block_name ?? '',
                'rowspan' => $cell->rowspan ?? 1,
                'colspan' => $cell->colspan ?? 1,
                'vertical_align' => $cell->vertical_align ?? 'top',
                'cell_background' => $cellStyling['background_color'] ?? '#ffffff',
                'cell_padding_top' => $cellStyling['padding_top'] ?? 12,
                'cell_padding_right' => $cellStyling['padding_right'] ?? 12,
                'cell_padding_bottom' => $cellStyling['padding_bottom'] ?? 12,
                'cell_padding_left' => $cellStyling['padding_left'] ?? 12,
                'cell_border_width' => $cellStyling['border_width'] ?? 1,
                'cell_border_style' => $cellStyling['border_style'] ?? 'solid',
                'cell_border_color' => $cellStyling['border_color'] ?? '#e5e7eb',
            ];
        }
    }

    // Create table cells for table layout
    private function createTableCells(LayoutSection $section): void
    {
        try {
            // Clear existing blocks for this section
            $section->blocks()->delete();

            // Create empty cells for table layout
            $rows = $section->table_rows ?? 1;
            $cols = $section->column_count ?? 1;

            for ($row = 1; $row <= $rows; $row++) {
                for ($col = 1; $col <= $cols; $col++) {
                    ContentBlock::create([
                        'layout_section_id' => $section->id,
                        'block_type' => 'empty',
                        'block_name' => "Cell {$row},{$col}",
                        'column_position' => $col,
                        'row_position' => $row,
                        'order' => ($row - 1) * $cols + $col,
                        'rowspan' => 1,
                        'colspan' => 1,
                        'vertical_align' => 'top',
                        'content_data' => [],
                        'field_mapping' => [],
                        'styling' => [],
                    ]);
                }
            }

            Log::info('Created table cells', [
                'section_id' => $section->id,
                'rows' => $rows,
                'cols' => $cols,
                'total_cells' => $rows * $cols
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create table cells: ' . $e->getMessage());
        }
    }

    // Cell merge operations (Divi/Elementor style)
    public function mergeCellRight(int $cellId): void
    {
        try {
            $cell = ContentBlock::find($cellId);
            if (!$cell) return;

            $section = $cell->layoutSection;
            $rightCell = $section->getCellAt($cell->row_position, $cell->column_position + 1);

            if ($rightCell && $cell->column_position + $cell->colspan <= $section->column_count) {
                // Merge content if right cell has content
                if ($rightCell->block_type !== 'empty') {
                    // Combine content or ask user preference
                    $this->combineBlockContent($cell, $rightCell);
                }

                // Update colspan and delete right cell
                $cell->update(['colspan' => $cell->colspan + 1]);
                $rightCell->delete();

                $this->redirect(request()->header('Referer'));
            }
        } catch (\Exception $e) {
            Log::error('Failed to merge cell right: ' . $e->getMessage());
        }
    }

    public function mergeCellDown(int $cellId): void
    {
        try {
            $cell = ContentBlock::find($cellId);
            if (!$cell) return;

            $section = $cell->layoutSection;
            $downCell = $section->getCellAt($cell->row_position + 1, $cell->column_position);

            if ($downCell && $cell->row_position + $cell->rowspan <= $section->table_rows) {
                // Merge content if down cell has content
                if ($downCell->block_type !== 'empty') {
                    $this->combineBlockContent($cell, $downCell);
                }

                // Update rowspan and delete down cell
                $cell->update(['rowspan' => $cell->rowspan + 1]);
                $downCell->delete();

                $this->redirect(request()->header('Referer'));
            }
        } catch (\Exception $e) {
            Log::error('Failed to merge cell down: ' . $e->getMessage());
        }
    }

    public function splitCell(int $cellId): void
    {
        try {
            $cell = ContentBlock::find($cellId);
            if (!$cell) return;

            $section = $cell->layoutSection;

            // Create new cells for split areas
            if ($cell->colspan > 1) {
                for ($i = 1; $i < $cell->colspan; $i++) {
                    ContentBlock::create([
                        'layout_section_id' => $section->id,
                        'block_type' => 'empty',
                        'block_name' => "Cell {$cell->row_position}," . ($cell->column_position + $i),
                        'column_position' => $cell->column_position + $i,
                        'row_position' => $cell->row_position,
                        'order' => $cell->order + $i,
                        'rowspan' => 1,
                        'colspan' => 1,
                        'vertical_align' => 'top',
                        'content_data' => [],
                        'field_mapping' => [],
                        'styling' => [],
                    ]);
                }
            }

            if ($cell->rowspan > 1) {
                for ($i = 1; $i < $cell->rowspan; $i++) {
                    ContentBlock::create([
                        'layout_section_id' => $section->id,
                        'block_type' => 'empty',
                        'block_name' => "Cell " . ($cell->row_position + $i) . ",{$cell->column_position}",
                        'column_position' => $cell->column_position,
                        'row_position' => $cell->row_position + $i,
                        'order' => $cell->order + ($i * $section->column_count),
                        'rowspan' => 1,
                        'colspan' => 1,
                        'vertical_align' => 'top',
                        'content_data' => [],
                        'field_mapping' => [],
                        'styling' => [],
                    ]);
                }
            }

            // Reset original cell to 1x1
            $cell->update(['rowspan' => 1, 'colspan' => 1]);

            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to split cell: ' . $e->getMessage());
        }
    }

    public function duplicateCell(int $cellId): void
    {
        try {
            $cell = ContentBlock::find($cellId);
            if (!$cell) return;

            // Find next empty cell in the same section
            $section = $cell->layoutSection;
            $emptyCell = $section->blocks()->where('block_type', 'empty')->first();

            if ($emptyCell) {
                $emptyCell->update([
                    'block_type' => $cell->block_type,
                    'block_name' => $cell->block_name . ' (Copy)',
                    'content_data' => $cell->content_data,
                    'field_mapping' => $cell->field_mapping,
                    'styling' => $cell->styling,
                ]);

                $this->redirect(request()->header('Referer'));
            }
        } catch (\Exception $e) {
            Log::error('Failed to duplicate cell: ' . $e->getMessage());
        }
    }

    public function clearCell(int $cellId): void
    {
        try {
            $cell = ContentBlock::find($cellId);
            if (!$cell) return;

            $cell->update([
                'block_type' => 'empty',
                'block_name' => "Cell {$cell->row_position},{$cell->column_position}",
                'content_data' => [],
                'field_mapping' => [],
                'styling' => [],
            ]);

            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to clear cell: ' . $e->getMessage());
        }
    }

    // Move block to specific cell (drag & drop)
    public function moveBlockToCell(int $blockId, int $targetCellId): void
    {
        try {
            $sourceBlock = ContentBlock::find($blockId);
            $targetCell = ContentBlock::find($targetCellId);

            if (!$sourceBlock || !$targetCell) return;

            // If target cell is empty, move block content
            if ($targetCell->block_type === 'empty') {
                $targetCell->update([
                    'block_type' => $sourceBlock->block_type,
                    'block_name' => $sourceBlock->block_name,
                    'content_data' => $sourceBlock->content_data,
                    'field_mapping' => $sourceBlock->field_mapping,
                    'styling' => $sourceBlock->styling,
                ]);

                // Clear source block
                $sourceBlock->update([
                    'block_type' => 'empty',
                    'block_name' => "Cell {$sourceBlock->row_position},{$sourceBlock->column_position}",
                    'content_data' => [],
                    'field_mapping' => [],
                    'styling' => [],
                ]);
            } else {
                // Swap blocks
                $tempData = [
                    'block_type' => $sourceBlock->block_type,
                    'block_name' => $sourceBlock->block_name,
                    'content_data' => $sourceBlock->content_data,
                    'field_mapping' => $sourceBlock->field_mapping,
                    'styling' => $sourceBlock->styling,
                ];

                $sourceBlock->update([
                    'block_type' => $targetCell->block_type,
                    'block_name' => $targetCell->block_name,
                    'content_data' => $targetCell->content_data,
                    'field_mapping' => $targetCell->field_mapping,
                    'styling' => $targetCell->styling,
                ]);

                $targetCell->update($tempData);
            }

            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to move block to cell: ' . $e->getMessage());
        }
    }

    // Copy & Paste functionality
    public function pasteBlock(int $sourceBlockId, int $targetCellId): void
    {
        try {
            $sourceBlock = ContentBlock::find($sourceBlockId);
            $targetCell = ContentBlock::find($targetCellId);

            if (!$sourceBlock || !$targetCell) return;

            $targetCell->update([
                'block_type' => $sourceBlock->block_type,
                'block_name' => $sourceBlock->block_name . ' (Copy)',
                'content_data' => $sourceBlock->content_data,
                'field_mapping' => $sourceBlock->field_mapping,
                'styling' => $sourceBlock->styling,
            ]);

            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Log::error('Failed to paste block: ' . $e->getMessage());
        }
    }

    // Helper method to combine block content when merging
    private function combineBlockContent(ContentBlock $primaryCell, ContentBlock $secondaryCell): void
    {
        if ($secondaryCell->block_type === 'empty') return;

        // Simple content combination - can be enhanced based on block types
        $primaryContent = $primaryCell->content_data ?? [];
        $secondaryContent = $secondaryCell->content_data ?? [];

        if (isset($primaryContent['text']) && isset($secondaryContent['text'])) {
            $primaryContent['text'] .= ' ' . $secondaryContent['text'];
        }

        $primaryCell->update(['content_data' => $primaryContent]);
    }

    // Reload template data
    private function reloadTemplate(): void
    {
        if ($this->template) {
            $this->template = TemplateLayout::with(['sections.blocks'])->find($this->template->id);
        }
    }

    // Auto-save block settings
    private function autoSaveBlockSettings(): void
    {
        try {
            $block = ContentBlock::find($this->selectedElementId);

            if (!$block) {
                return;
            }

            $newConfiguration = array_merge($block->configuration ?? [], [
                'alignment' => $this->blockSettings['alignment'] ?? 'left',
                'font_size' => (int) ($this->blockSettings['font_size'] ?? 12),
                'font_weight' => $this->blockSettings['font_weight'] ?? 'normal',
                'font_color' => $this->blockSettings['font_color'] ?? '#000000',
            ]);

            // Update block settings
            $block->update([
                'block_name' => $this->blockSettings['block_name'] ?? $block->block_name,
                'configuration' => $newConfiguration
            ]);

            $this->reloadTemplate();

        } catch (\Exception $e) {
            Log::error('Failed to auto-save block settings: ' . $e->getMessage());
        }
    }

    // Update block settings (manual save - kept for compatibility)
    public function updateBlockSettings(): void
    {
        $this->autoSaveBlockSettings();
    }

    // Modal actions
    protected function getActions(): array
    {
        return [
            // Section Settings Modal
            Action::make('sectionSettings')
                ->label('Section Settings')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('gray')
                ->form([
                    \Filament\Forms\Components\Section::make('Section Configuration')
                        ->schema([
                            TextInput::make('section_name')
                                ->label('Section Name')
                                ->placeholder('Enter section name'),

                            Grid::make(2)
                                ->schema([
                                    Select::make('column_count')
                                        ->label('Column Count')
                                        ->options([
                                            1 => '1 Column',
                                            2 => '2 Columns',
                                            3 => '3 Columns',
                                            4 => '4 Columns',
                                        ])
                                        ->default(1)
                                        ->reactive(),

                                    Select::make('column_layout')
                                        ->label('Column Layout')
                                        ->options([
                                            'equal' => 'Equal Width',
                                            'custom' => 'Custom Width',
                                            'flex' => 'Flexible',
                                            'grid' => 'CSS Grid',
                                        ])
                                        ->default('equal'),
                                ]),

                            TextInput::make('min_height')
                                ->label('Minimum Height (px)')
                                ->numeric()
                                ->default(100),

                            ColorPicker::make('background_color')
                                ->label('Background Color'),
                        ]),
                ])
                ->action(function (array $data) {
                    $this->sectionSettings = $data;
                    $this->autoSaveSectionSettings();
                    $this->showSectionModal = false;
                })
                ->visible(fn () => $this->showSectionModal),

            // Block Settings Modal
            Action::make('blockSettings')
                ->label('Block Settings')
                ->icon('heroicon-o-cube')
                ->color('gray')
                ->form([
                    \Filament\Forms\Components\Section::make('Block Configuration')
                        ->schema([
                            TextInput::make('block_name')
                                ->label('Block Name')
                                ->placeholder('Enter block name'),

                            Grid::make(2)
                                ->schema([
                                    Select::make('alignment')
                                        ->label('Text Alignment')
                                        ->options([
                                            'left' => 'Left',
                                            'center' => 'Center',
                                            'right' => 'Right',
                                            'justify' => 'Justify',
                                        ])
                                        ->default('left'),

                                    TextInput::make('font_size')
                                        ->label('Font Size (pt)')
                                        ->numeric()
                                        ->default(12),
                                ]),

                            Grid::make(2)
                                ->schema([
                                    Select::make('font_weight')
                                        ->label('Font Weight')
                                        ->options([
                                            'normal' => 'Normal',
                                            'bold' => 'Bold',
                                            'lighter' => 'Lighter',
                                        ])
                                        ->default('normal'),

                                    ColorPicker::make('font_color')
                                        ->label('Font Color')
                                        ->default('#000000'),
                                ]),
                        ]),
                ])
                ->action(function (array $data) {
                    $this->blockSettings = $data;
                    $this->updateBlockSettings();
                    $this->showBlockModal = false;
                })
                ->visible(fn () => $this->showBlockModal),

            // Image Settings Modal
            Action::make('imageSettings')
                ->label('Image Settings')
                ->icon('heroicon-o-photo')
                ->color('gray')
                ->form([
                    \Filament\Forms\Components\Section::make('Image Configuration')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    TextInput::make('width')
                                        ->label('Width')
                                        ->placeholder('100px or auto')
                                        ->default('100px'),

                                    TextInput::make('height')
                                        ->label('Height')
                                        ->placeholder('auto or 100px')
                                        ->default('auto'),
                                ]),

                            Select::make('object_fit')
                                ->label('Image Fit')
                                ->options([
                                    'contain' => 'Contain (fit inside)',
                                    'cover' => 'Cover (fill area)',
                                    'fill' => 'Fill (stretch)',
                                    'scale-down' => 'Scale Down',
                                    'none' => 'None (original)',
                                ])
                                ->default('contain'),

                            TextInput::make('alt_text')
                                ->label('Alt Text')
                                ->placeholder('Image description'),
                        ]),
                ])
                ->action(function (array $data) {
                    $this->updateImageSettings($data);
                    $this->showImageModal = false;
                })
                ->visible(fn () => $this->showImageModal),
        ];
    }

    // Open modals
    public function openSectionSettings(int $sectionId): void
    {
        $this->selectedElementId = $sectionId;
        $this->selectedElementType = 'section';
        $this->loadSectionSettings($sectionId);
        $this->showSectionModal = true;
    }

    public function openBlockSettings(int $blockId): void
    {
        $this->selectedElementId = $blockId;
        $this->selectedElementType = 'block';
        $this->loadBlockSettings($blockId);
        $this->showBlockModal = true;
    }

    public function openImageSettings(int $blockId): void
    {
        $this->selectedElementId = $blockId;
        $this->selectedElementType = 'block';
        $this->loadImageSettings($blockId);
        $this->showImageModal = true;
    }

    // Load block settings
    public function loadBlockSettings(int $blockId): void
    {
        $block = ContentBlock::find($blockId);

        if ($block) {
            $config = $block->configuration ?? [];
            $this->blockSettings = [
                'block_name' => $block->block_name ?? '',
                'alignment' => $config['alignment'] ?? 'left',
                'font_size' => $config['font_size'] ?? 12,
                'font_weight' => $config['font_weight'] ?? 'normal',
                'font_color' => $config['font_color'] ?? '#000000',
            ];
        }
    }

    // Load image settings
    public function loadImageSettings(int $blockId): void
    {
        $block = ContentBlock::find($blockId);

        if ($block) {
            $contentData = $block->content_data ?? [];
            $this->blockSettings = [
                'width' => $contentData['width'] ?? '100px',
                'height' => $contentData['height'] ?? 'auto',
                'object_fit' => $contentData['object_fit'] ?? 'contain',
                'alt_text' => $contentData['alt_text'] ?? '',
            ];
        }
    }

    // Auto-save image settings
    private function autoSaveImageSettings(): void
    {
        try {
            $block = ContentBlock::find($this->selectedElementId);

            if (!$block) {
                return;
            }

            // Update content_data with image settings
            $oldContentData = $block->content_data ?? [];
            $newContentData = array_merge($oldContentData, [
                'width' => $this->blockSettings['width'] ?? '100px',
                'height' => $this->blockSettings['height'] ?? 'auto',
                'object_fit' => $this->blockSettings['object_fit'] ?? 'contain',
                'alt_text' => $this->blockSettings['alt_text'] ?? '',
            ]);

            $block->update(['content_data' => $newContentData]);

            $this->reloadTemplate();

        } catch (\Exception $e) {
            Log::error('Failed to auto-save image settings: ' . $e->getMessage());
        }
    }

    // Update image settings (manual save - kept for compatibility)
    public function updateImageSettings(): void
    {
        $this->autoSaveImageSettings();
    }

    // Move block up in order
    public function moveBlockUp(int $blockId): void
    {
        try {
            $block = ContentBlock::find($blockId);

            if (!$block) {
                Notification::make()
                    ->title('Error')
                    ->body('Block not found.')
                    ->danger()
                    ->send();
                return;
            }

            // Find previous block in same column
            $previousBlock = ContentBlock::where('layout_section_id', $block->layout_section_id)
                ->where('column_position', $block->column_position)
                ->where('order', '<', $block->order)
                ->orderBy('order', 'desc')
                ->first();

            if ($previousBlock) {
                // Swap orders
                $tempOrder = $block->order;
                $block->update(['order' => $previousBlock->order]);
                $previousBlock->update(['order' => $tempOrder]);

                $this->reloadTemplate();
            }

        } catch (\Exception $e) {
            Log::error('Failed to move block: ' . $e->getMessage());
        }
    }

    // Move block down in order
    public function moveBlockDown(int $blockId): void
    {
        try {
            $block = ContentBlock::find($blockId);

            if (!$block) {
                return;
            }

            // Find next block in same column
            $nextBlock = ContentBlock::where('layout_section_id', $block->layout_section_id)
                ->where('column_position', $block->column_position)
                ->where('order', '>', $block->order)
                ->orderBy('order', 'asc')
                ->first();

            if ($nextBlock) {
                // Swap orders
                $tempOrder = $block->order;
                $block->update(['order' => $nextBlock->order]);
                $nextBlock->update(['order' => $tempOrder]);

                $this->reloadTemplate();
            }

        } catch (\Exception $e) {
            Log::error('Failed to move block: ' . $e->getMessage());
        }
    }

    // Move block to different column via drag & drop
    public function moveBlockToColumn(int $blockId, int $sectionId, int $columnId): void
    {
        try {
            $block = ContentBlock::find($blockId);

            if (!$block) {
                return;
            }

            // Get the highest order in target column
            $maxOrder = ContentBlock::where('layout_section_id', $sectionId)
                ->where('column_position', $columnId)
                ->max('order') ?? 0;

            // Update block position
            $block->update([
                'layout_section_id' => $sectionId,
                'column_position' => $columnId,
                'order' => $maxOrder + 1
            ]);

            $this->reloadTemplate();

        } catch (\Exception $e) {
            Log::error('Failed to move block: ' . $e->getMessage());
        }
    }

    // Get data for frontend Vue.js component
    public function getBuilderData(): array
    {
        return [
            'template' => $this->template?->toArray(),
            'templateData' => $this->templateData,
            'availableBlocks' => $this->availableBlocks,
            'companies' => $this->companies,
            'selectedCompanyId' => $this->selectedCompanyId,
            'previewMode' => $this->previewMode,
            'pageSize' => $this->pageSize,
            'sections' => $this->template?->sections()->with('blocks')->get()->toArray() ?? [],
        ];
    }
}
