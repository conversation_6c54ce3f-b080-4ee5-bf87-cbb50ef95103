<?php

namespace App\Filament\Admin\Pages;

use App\Models\TemplateLayout;
use App\Models\LayoutSection;
use App\Models\ContentBlock;
use App\Models\BlockTemplate;
use App\Models\Company;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Log;

class VisualTemplateBuilder extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-paint-brush';
    protected static ?string $navigationLabel = 'Visual Builder v2.0';
    protected static ?string $navigationGroup = 'Template Management';
    protected static string $view = 'filament.admin.pages.visual-template-builder';
    protected static ?int $navigationSort = 2;

    // Page properties
    public ?TemplateLayout $template = null;
    public array $templateData = [];
    public array $availableBlocks = [];
    public array $companies = [];
    public string $selectedCompanyId = '';
    public string $previewMode = 'desktop';
    public string $pageSize = 'legal';

    // Section settings
    public array $sectionSettings = [
        'section_name' => '',
        'column_count' => 1,
        'column_layout' => 'equal',
        'column_widths' => [],
        'background_color' => '',
        'min_height' => 100,
    ];

    // Block settings
    public array $blockSettings = [
        'block_name' => '',
        'alignment' => 'left',
        'font_size' => 12,
        'font_weight' => 'normal',
        'font_color' => '#000000',
    ];

    // Selected element for properties modal
    public ?int $selectedElementId = null;

    public function mount(): void
    {
        // Show new system notification
        Notification::make()
            ->title('🚀 Visual Builder v2.0')
            ->body('Welcome to the new DIVI-style visual template builder! Create dynamic layouts with drag & drop sections.')
            ->success()
            ->persistent()
            ->send();

        // Get template ID from URL parameter
        $templateId = Request::get('template');

        if ($templateId) {
            $this->template = TemplateLayout::with(['sections.blocks'])->find($templateId);
            if ($this->template) {
                $this->templateData = $this->template->layout_data ?? [];
                $this->pageSize = $this->template->page_size ?? 'legal';
                $this->selectedCompanyId = (string) $this->template->company_id;
            }
        }

        $this->loadAvailableBlocks();
        $this->loadCompanies();
    }

    protected function loadAvailableBlocks(): void
    {
        $this->availableBlocks = BlockTemplate::active()
            ->orderBy('category')
            ->orderBy('usage_count', 'desc')
            ->get()
            ->groupBy('category')
            ->map(function ($blocks, $category) {
                return [
                    'category' => $category,
                    'label' => ucfirst($category),
                    'blocks' => $blocks->map(function ($block) {
                        return [
                            'id' => $block->id,
                            'name' => $block->name,
                            'description' => $block->description,
                            'block_type' => $block->block_type,
                            'preview_html' => $block->getPreviewHtml(),
                            'tags' => $block->tags ?? [],
                        ];
                    })->toArray()
                ];
            })
            ->values()
            ->toArray();
    }

    protected function loadCompanies(): void
    {
        $this->companies = Company::select('id', 'name')
            ->orderBy('name')
            ->get()
            ->map(function ($company) {
                return [
                    'id' => $company->id,
                    'name' => $company->name,
                ];
            })
            ->toArray();
    }

    public function getTitle(): string
    {
        if ($this->template) {
            return '🎨 Visual Builder: ' . $this->template->name;
        }
        return '🎨 Visual Template Builder v2.0';
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('new_template')
                ->label('New Template')
                ->icon('heroicon-o-plus')
                ->color('success')
                ->action('createNewTemplate')
                ->visible(fn () => $this->template === null),

            Action::make('save')
                ->label('Save Template')
                ->icon('heroicon-o-check')
                ->color('success')
                ->action('saveTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('preview')
                ->label('Preview')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->action('previewTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('duplicate')
                ->label('Duplicate')
                ->icon('heroicon-o-document-duplicate')
                ->color('gray')
                ->action('duplicateTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('settings')
                ->label('Template Settings')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('gray')
                ->action('openTemplateSettings')
                ->visible(fn () => $this->template !== null),
        ];
    }

    public function createNewTemplate(): void
    {
        $this->redirect(route('filament.admin.resources.template-layouts.create'));
    }

    public function saveTemplate(): void
    {
        if (!$this->template) {
            Notification::make()
                ->title('Error')
                ->body('No template selected to save.')
                ->danger()
                ->send();
            return;
        }

        try {
            // Template will be saved via Livewire component interactions
            Notification::make()
                ->title('Success')
                ->body('Template saved successfully.')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to save template: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function previewTemplate(): void
    {
        if (!$this->template) {
            Notification::make()
                ->title('Error')
                ->body('No template selected for preview.')
                ->warning()
                ->send();
            return;
        }

        // Generate preview URL for v2.0
        $previewUrl = route('visual-template.preview', [
            'template' => $this->template->id,
            'mode' => $this->previewMode,
            'size' => $this->pageSize
        ]);

        // Open preview in new tab
        $this->js("window.open('$previewUrl', '_blank')");
    }

    public function duplicateTemplate(): void
    {
        if (!$this->template) {
            return;
        }

        try {
            $newTemplate = $this->template->duplicate();

            Notification::make()
                ->title('Template Duplicated')
                ->body("Template '{$this->template->name}' has been duplicated as '{$newTemplate->name}'.")
                ->success()
                ->send();

            // Redirect to edit the new template
            $this->redirect(route('filament.admin.pages.visual-template-builder', ['template' => $newTemplate->id]));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Duplication Failed')
                ->body('Failed to duplicate template: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function openTemplateSettings(): void
    {
        if ($this->template) {
            $this->redirect(route('filament.admin.resources.template-layouts.edit', $this->template));
        }
    }

    public function changePreviewMode(string $mode): void
    {
        $this->previewMode = $mode;

        Notification::make()
            ->title('Preview Mode Changed')
            ->body("Preview mode changed to {$mode}.")
            ->success()
            ->send();
    }

    public function changePageSize(string $size): void
    {
        $this->pageSize = $size;

        if ($this->template) {
            $this->template->update(['page_size' => $size]);
        }

        Notification::make()
            ->title('Page Size Changed')
            ->body("Page size changed to {$size}.")
            ->success()
            ->send();
    }

    // Section management methods
    public function addSection(string $sectionType, ?int $order = null): void
    {
        if (!$this->template) {
            Notification::make()
                ->title('Error')
                ->body('Please create or select a template first.')
                ->warning()
                ->send();
            return;
        }

        try {
            $section = LayoutSection::create([
                'template_layout_id' => $this->template->id,
                'section_type' => $sectionType,
                'order' => $order ?? $this->getNextSectionOrder($sectionType),
            ]);

            Notification::make()
                ->title('Section Added')
                ->body("New {$sectionType} section added to template.")
                ->success()
                ->send();

            // Refresh the page to show new section
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to add section: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    private function getNextSectionOrder(string $sectionType): int
    {
        if (!$this->template) {
            return 0;
        }

        return $this->template->sections()
            ->where('section_type', $sectionType)
            ->max('order') + 1;
    }

    // Add block to section
    public function addBlockToSection(int $blockTemplateId, int $sectionId, int $columnPosition = 1): void
    {
        try {
            $blockTemplate = BlockTemplate::find($blockTemplateId);
            $section = LayoutSection::find($sectionId);

            if (!$blockTemplate || !$section) {
                Notification::make()
                    ->title('Error')
                    ->body('Block template or section not found.')
                    ->danger()
                    ->send();
                return;
            }

            // Create content block from template
            $block = $blockTemplate->createContentBlock($section->id, [
                'column_position' => $columnPosition,
                'order' => $section->getNextBlockOrder($columnPosition),
            ]);

            Notification::make()
                ->title('Block Added')
                ->body("'{$blockTemplate->name}' added to section.")
                ->success()
                ->send();

            // Refresh the page to show new block
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to add block: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    // Delete section
    public function deleteSection(int $sectionId): void
    {
        try {
            $section = LayoutSection::find($sectionId);

            if (!$section) {
                Notification::make()
                    ->title('Error')
                    ->body('Section not found.')
                    ->danger()
                    ->send();
                return;
            }

            $sectionName = $section->section_name ?: ucfirst($section->section_type) . ' Section';
            $section->delete();

            Notification::make()
                ->title('Section Deleted')
                ->body("'{$sectionName}' has been deleted.")
                ->success()
                ->send();

            // Refresh the page
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to delete section: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    // Delete block
    public function deleteBlock(int $blockId): void
    {
        try {
            $block = ContentBlock::find($blockId);

            if (!$block) {
                Notification::make()
                    ->title('Error')
                    ->body('Block not found.')
                    ->danger()
                    ->send();
                return;
            }

            $blockName = $block->block_name ?: ucfirst($block->block_type);
            $block->delete();

            Notification::make()
                ->title('Block Removed')
                ->body("'{$blockName}' has been removed.")
                ->success()
                ->send();

            // Refresh the page
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to remove block: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    // Auto-save section settings when changed
    public function updatedSectionSettings(): void
    {
        // Auto-save when section settings change
        if ($this->selectedElementId && $this->sectionSettings) {
            $this->autoSaveSectionSettings();
        }
    }

    // Auto-save section settings
    private function autoSaveSectionSettings(): void
    {
        try {
            Log::info('Auto-saving section settings', [
                'selectedElementId' => $this->selectedElementId,
                'sectionSettings' => $this->sectionSettings
            ]);

            // Validate selectedElementId
            if (!$this->selectedElementId) {
                Notification::make()
                    ->title('Error')
                    ->body('No section selected for update.')
                    ->danger()
                    ->send();
                return;
            }

            $section = LayoutSection::find($this->selectedElementId);

            if (!$section) {
                Notification::make()
                    ->title('Error')
                    ->body('Section not found. ID: ' . $this->selectedElementId)
                    ->danger()
                    ->send();
                return;
            }

            Log::info('Section found', [
                'section_id' => $section->id,
                'current_column_count' => $section->column_count,
                'current_layout' => $section->column_layout
            ]);

            // Validate and prepare update data
            $columnCount = (int) ($this->sectionSettings['column_count'] ?? $section->column_count);
            $columnLayout = $this->sectionSettings['column_layout'] ?? $section->column_layout;
            $columnWidths = $this->sectionSettings['column_widths'] ?? $section->column_widths;

            // Ensure column_widths is array or null
            if (is_string($columnWidths)) {
                $columnWidths = json_decode($columnWidths, true);
            }

            $updateData = [
                'section_name' => $this->sectionSettings['section_name'] ?? $section->section_name,
                'column_count' => $columnCount,
                'column_layout' => $columnLayout,
                'column_widths' => $columnWidths,
                'background_color' => $this->sectionSettings['background_color'] ?? $section->background_color,
                'min_height' => (int) ($this->sectionSettings['min_height'] ?? $section->min_height),
            ];

            Log::info('Prepared update data', $updateData);

            // Perform update
            $updateResult = $section->update($updateData);

            Log::info('Update result', [
                'success' => $updateResult,
                'section_id' => $section->id
            ]);

            if (!$updateResult) {
                throw new \Exception('Database update failed');
            }

            // Verify update
            $section->refresh();
            Log::info('Section after update', [
                'column_count' => $section->column_count,
                'column_layout' => $section->column_layout,
                'column_widths' => $section->column_widths
            ]);

            Notification::make()
                ->title('Section Updated Successfully')
                ->body("Column count changed to {$section->column_count}. Layout: {$section->column_layout}")
                ->success()
                ->send();

            // Emit success event
            $this->dispatch('section-updated', [
                'section_id' => $section->id,
                'column_count' => $section->column_count,
                'column_layout' => $section->column_layout,
                'auto_saved' => true
            ]);

            // Reload template data to reflect changes
            $this->reloadTemplate();

        } catch (\Exception $e) {
            Log::error('Failed to auto-save section', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'selectedElementId' => $this->selectedElementId,
                'sectionSettings' => $this->sectionSettings
            ]);

            // Don't show notification for auto-save errors (too intrusive)
            // Just emit event for debugging
            $this->dispatch('section-update-failed', [
                'error' => $e->getMessage(),
                'selectedElementId' => $this->selectedElementId,
                'auto_save' => true
            ]);
        }
    }

    // Manual update method (backup/legacy)
    public function updateSectionSettings(): void
    {
        $this->autoSaveSectionSettings();

        // Show success notification for manual saves
        Notification::make()
            ->title('Section Settings Saved')
            ->body('Changes have been applied successfully.')
            ->success()
            ->send();
    }

    // Load section settings for editing
    public function loadSectionSettings(int $sectionId): void
    {
        $section = LayoutSection::find($sectionId);

        if ($section) {
            $this->sectionSettings = [
                'section_name' => $section->section_name ?? '',
                'column_count' => $section->column_count,
                'column_layout' => $section->column_layout ?? 'equal',
                'column_widths' => $section->column_widths ?? [],
                'background_color' => $section->background_color ?? '',
                'min_height' => $section->min_height ?? 100,
            ];
        }
    }

    // Reload template data
    private function reloadTemplate(): void
    {
        if ($this->template) {
            $this->template = TemplateLayout::with(['sections.blocks'])->find($this->template->id);
        }
    }

    // Update block settings
    public function updateBlockSettings(): void
    {
        try {
            $block = ContentBlock::find($this->selectedElementId);

            if (!$block) {
                Notification::make()
                    ->title('Error')
                    ->body('Block not found.')
                    ->danger()
                    ->send();
                return;
            }

            // Update block settings
            $block->update([
                'block_name' => $this->blockSettings['block_name'] ?? $block->block_name,
                'configuration' => array_merge($block->configuration ?? [], [
                    'alignment' => $this->blockSettings['alignment'] ?? 'left',
                    'font_size' => (int) ($this->blockSettings['font_size'] ?? 12),
                    'font_weight' => $this->blockSettings['font_weight'] ?? 'normal',
                    'font_color' => $this->blockSettings['font_color'] ?? '#000000',
                ])
            ]);

            Notification::make()
                ->title('Block Updated')
                ->body('Block settings have been saved.')
                ->success()
                ->send();

            $this->reloadTemplate();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to update block: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    // Get data for frontend Vue.js component
    public function getBuilderData(): array
    {
        return [
            'template' => $this->template?->toArray(),
            'templateData' => $this->templateData,
            'availableBlocks' => $this->availableBlocks,
            'companies' => $this->companies,
            'selectedCompanyId' => $this->selectedCompanyId,
            'previewMode' => $this->previewMode,
            'pageSize' => $this->pageSize,
            'sections' => $this->template?->sections()->with('blocks')->get()->toArray() ?? [],
        ];
    }
}
