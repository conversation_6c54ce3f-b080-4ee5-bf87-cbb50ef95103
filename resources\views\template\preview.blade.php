<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Preview</title>
    
    <!-- Load Google Fonts if available -->
    {!! $fonts['html_links'] ?? '' !!}
    
    <!-- Preload fonts for better performance -->
    {!! $fonts['preload_links'] ?? '' !!}
    
    <style>
        /* CSS Imports for fonts */
        {!! $fonts['css_imports'] ?? '' !!}
        
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {{ $templateData['config']['font_family'] ?? 'DejaVu Sans' }}, Arial, sans-serif;
            font-size: {{ $templateData['config']['font_size'] ?? 12 }}pt;
            line-height: 1.4;
            color: #333;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .preview-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .page-content {
            @if($pageSize === 'legal')
                width: 8.5in;
                min-height: 14in;
                max-height: 14in;
            @else
                width: 210mm;
                min-height: 330mm;
                max-height: 330mm;
            @endif
            margin: 0 auto;
            padding: 0.75in;
            background: white;
            position: relative;
            overflow: hidden;
        }
        
        /* Section styles */
        .template-section {
            margin-bottom: 20px;
        }
        
        .template-section.header {
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }
        
        .template-section.footer {
            border-top: 2px solid #eee;
            padding-top: 20px;
            margin-top: 20px;
        }
        
        /* Component styles */
        .component {
            margin-bottom: 15px;
        }
        
        .component.kop {
            text-align: center;
        }
        
        .component.kop.left {
            text-align: left;
        }
        
        .component.kop.right {
            text-align: right;
        }
        
        .component.billto {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .component.table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .component.table th,
        .component.table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .component.table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .component.inwords {
            font-style: italic;
            margin: 15px 0;
        }
        
        .component.bankinfo {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        
        /* Utility classes */
        .text-left { text-align: left; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .text-justify { text-align: justify; }
        
        .font-bold { font-weight: bold; }
        .font-semibold { font-weight: 600; }
        .font-normal { font-weight: normal; }
        
        .text-sm { font-size: 0.875em; }
        .text-lg { font-size: 1.125em; }
        .text-xl { font-size: 1.25em; }
        
        .mb-2 { margin-bottom: 8px; }
        .mb-4 { margin-bottom: 16px; }
        .mt-4 { margin-top: 16px; }
        
        /* Print styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .preview-container {
                box-shadow: none;
                border-radius: 0;
            }
            
            .page-content {
                box-shadow: none;
                margin: 0;
                padding: 0.5in;
            }
        }
        
        /* Page size indicator */
        .page-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
        
        /* Error styles */
        .error {
            background: #fee;
            color: #c33;
            padding: 10px;
            border: 1px solid #fcc;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        /* Content overflow warning */
        .overflow-warning {
            position: fixed;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: #f59e0b;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            display: none;
        }
    </style>
</head>
<body>
    <!-- Page size indicator -->
    <div class="page-info">
        {{ $pageSize === 'legal' ? 'Legal (8.5" × 14")' : 'F4/Folio (210mm × 330mm)' }}
    </div>
    
    <!-- Content overflow warning -->
    <div class="overflow-warning" id="overflowWarning">
        ⚠️ Content may exceed single page limit
    </div>
    
    <div class="preview-container">
        <div class="page-content" id="pageContent">
            
            @if(isset($templateData['sections']))
                
                <!-- Header Section -->
                @if(isset($templateData['sections']['header']) && count($templateData['sections']['header']) > 0)
                    <div class="template-section header">
                        @foreach($templateData['sections']['header'] as $component)
                            <div class="component {{ $component['type'] ?? '' }}">
                                {!! app(App\Http\Controllers\TemplatePreviewController::class)->renderComponent($component, $sampleData) !!}
                            </div>
                        @endforeach
                    </div>
                @endif
                
                <!-- Body Section -->
                @if(isset($templateData['sections']['body']) && count($templateData['sections']['body']) > 0)
                    <div class="template-section body">
                        @foreach($templateData['sections']['body'] as $component)
                            <div class="component {{ $component['type'] ?? '' }}">
                                {!! app(App\Http\Controllers\TemplatePreviewController::class)->renderComponent($component, $sampleData) !!}
                            </div>
                        @endforeach
                    </div>
                @endif
                
                <!-- Footer Section -->
                @if(isset($templateData['sections']['footer']) && count($templateData['sections']['footer']) > 0)
                    <div class="template-section footer">
                        @foreach($templateData['sections']['footer'] as $component)
                            <div class="component {{ $component['type'] ?? '' }}">
                                {!! app(App\Http\Controllers\TemplatePreviewController::class)->renderComponent($component, $sampleData) !!}
                            </div>
                        @endforeach
                    </div>
                @endif
                
            @else
                <!-- Empty template message -->
                <div class="text-center" style="padding: 100px 20px; color: #666;">
                    <h2 style="margin-bottom: 10px;">Empty Template</h2>
                    <p>Add components to your template to see the preview.</p>
                </div>
            @endif
            
        </div>
    </div>
    
    <script>
        // Check for content overflow
        function checkOverflow() {
            const pageContent = document.getElementById('pageContent');
            const overflowWarning = document.getElementById('overflowWarning');
            
            if (pageContent.scrollHeight > pageContent.clientHeight) {
                overflowWarning.style.display = 'block';
            } else {
                overflowWarning.style.display = 'none';
            }
        }
        
        // Check overflow on load and resize
        window.addEventListener('load', checkOverflow);
        window.addEventListener('resize', checkOverflow);
        
        // Auto-refresh preview when parent window updates (for builder integration)
        window.addEventListener('message', function(event) {
            if (event.data.type === 'template-update') {
                location.reload();
            }
        });
        
        // Print functionality
        function printTemplate() {
            window.print();
        }
        
        // Add print button if needed
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printTemplate();
            }
        });
    </script>
</body>
</html>
