<?php

/**
 * Update Visual Builder to Use Existing Data Structure
 * Run via: php artisan tinker
 */

echo "=== Updating Visual Builder Data Structure ===\n";

try {
    // 1. Clear existing system blocks
    echo "1. Clearing existing system blocks...\n";
    
    App\Models\BlockTemplate::where('is_system', true)->delete();
    echo "   ✅ Existing system blocks cleared\n";
    
    // 2. Create block templates using EXISTING data structure
    echo "\n2. Creating block templates with existing data structure...\n";
    
    $blockTemplates = [
        // COMPANY BLOCKS (using $payload['company'])
        [
            'name' => 'Company Logo',
            'description' => 'Company logo image',
            'block_type' => 'company_logo',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.logo',
                'alt_text' => 'Company Logo',
                'width' => '100px',
                'height' => 'auto',
                'storage_prefix' => 'storage/'
            ],
            'field_mappings' => [
                'field' => 'company.logo',
                'format' => 'image'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'margin' => ['top' => 0, 'right' => 10, 'bottom' => 0, 'left' => 0]
            ],
            'preview_html' => '<div class="w-20 h-16 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500 rounded">[LOGO]</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['logo', 'company', 'header'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Name',
            'description' => 'Company name with customizable heading size',
            'block_type' => 'company_name',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.name',
                'heading_size_path' => 'company.heading_size',
                'text_color_path' => 'company.text_color',
                'default_heading' => 'h3',
                'default_color' => '#000000'
            ],
            'field_mappings' => [
                'field' => 'company.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 18,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="text-lg font-bold text-gray-800">PT. Sample Company</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'name', 'header'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Address',
            'description' => 'Company address (strips HTML tags)',
            'block_type' => 'company_address',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.address',
                'strip_tags' => true
            ],
            'field_mappings' => [
                'field' => 'company.address',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Sample Street No. 123, Jakarta 12345</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'address', 'header'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Phone',
            'description' => 'Company phone number',
            'block_type' => 'company_phone',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.phone',
                'prefix' => '',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.phone',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">+62 21 1234 5678</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'phone', 'header'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Email',
            'description' => 'Company email address',
            'block_type' => 'company_email',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.email',
                'prefix' => 'Email: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.email',
                'format' => 'email'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Email: <span class="text-blue-600"><EMAIL></span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'email', 'header'],
            'usage_count' => 0
        ],
        
        // INVOICE BLOCKS (using $payload['invoice'])
        [
            'name' => 'Invoice Number',
            'description' => 'Invoice number field',
            'block_type' => 'invoice_number',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'invoice.invoice_no',
                'prefix' => 'Invoice No: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_no',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 14,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="text-right font-bold">Invoice No: INV-2024-001</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'number', 'header'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Date',
            'description' => 'Invoice date field',
            'block_type' => 'invoice_date',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'invoice.invoice_date',
                'prefix' => 'Invoice Date: ',
                'suffix' => '',
                'date_format' => 'd/m/Y'
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_date',
                'format' => 'date'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-right text-sm text-gray-600">Invoice Date: 15/01/2024</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'date', 'header'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Due Date',
            'description' => 'Invoice due date field',
            'block_type' => 'invoice_due_date',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'invoice.invoice_due',
                'prefix' => 'Due: ',
                'suffix' => '',
                'date_format' => 'd/m/Y'
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_due',
                'format' => 'date'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-right text-sm text-gray-600">Due: 15/02/2024</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'due_date', 'header'],
            'usage_count' => 0
        ],
        
        // CUSTOMER BLOCKS (using $payload['customer'])
        [
            'name' => 'Customer Name',
            'description' => 'Customer/client name',
            'block_type' => 'customer_name',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'customer.name',
                'prefix' => 'Bill to: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'customer.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 14,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="font-bold">Bill to: Sample Client Corp</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'name', 'header', 'billto'],
            'usage_count' => 0
        ],
        [
            'name' => 'Customer Address',
            'description' => 'Customer/client address',
            'block_type' => 'customer_address',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'customer.address',
                'allow_html' => true
            ],
            'field_mappings' => [
                'field' => 'customer.address',
                'format' => 'html'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Client Avenue No. 456, Surabaya 60123</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'address', 'header', 'billto'],
            'usage_count' => 0
        ],
        
        // COMBINED BLOCKS
        [
            'name' => 'Company Info Combined',
            'description' => 'Combined company name, address, phone, email (like kop templates)',
            'block_type' => 'company_info_combined',
            'category' => 'header',
            'template_data' => [
                'fields' => [
                    [
                        'path' => 'company.name',
                        'format' => 'text',
                        'heading_size_path' => 'company.heading_size',
                        'text_color_path' => 'company.text_color',
                        'style_class' => 'fw-bold',
                        'default_heading' => 'h3'
                    ],
                    [
                        'path' => 'company.address',
                        'format' => 'html',
                        'strip_tags' => true,
                        'style' => 'font-size: 11pt; margin-bottom: 0;'
                    ],
                    [
                        'path' => 'company.phone',
                        'format' => 'text',
                        'style' => 'font-size: 11pt;',
                        'condition' => 'if_not_empty'
                    ],
                    [
                        'path' => 'company.email',
                        'format' => 'email',
                        'prefix' => 'Email: ',
                        'style' => 'font-size: 11pt;',
                        'condition' => 'if_not_empty'
                    ]
                ],
                'layout' => 'vertical',
                'template_style' => 'kop'
            ],
            'field_mappings' => [
                'fields' => ['company.name', 'company.address', 'company.phone', 'company.email'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'line_height' => 1.2
            ],
            'preview_html' => '<div><div class="h3 fw-bold">PT. Sample Company</div><ul class="list-unstyled mt-0" style="font-size: 11pt"><li class="mb-0">Jl. Sample Street No. 123, Jakarta</li><li>+62 21 1234 5678</li><li>Email: <EMAIL></li></ul></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'info', 'header', 'combined', 'kop'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Details Combined',
            'description' => 'Combined invoice number, date, due date (like billto templates)',
            'block_type' => 'invoice_details_combined',
            'category' => 'header',
            'template_data' => [
                'fields' => [
                    [
                        'path' => 'invoice.invoice_no',
                        'format' => 'text',
                        'prefix' => 'Invoice No: ',
                        'style_class' => 'fw-bold'
                    ],
                    [
                        'path' => 'invoice.invoice_date',
                        'format' => 'date',
                        'prefix' => 'Invoice Date: ',
                        'date_format' => 'd/m/Y'
                    ],
                    [
                        'path' => 'invoice.invoice_due',
                        'format' => 'date',
                        'prefix' => 'Due: ',
                        'date_format' => 'd/m/Y'
                    ]
                ],
                'layout' => 'vertical',
                'template_style' => 'billto'
            ],
            'field_mappings' => [
                'fields' => ['invoice.invoice_no', 'invoice.invoice_date', 'invoice.invoice_due'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-right"><div class="fw-bold">Invoice No: INV-2024-001</div><div>Invoice Date: 15/01/2024</div><div>Due: 15/02/2024</div></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'details', 'header', 'combined', 'billto'],
            'usage_count' => 0
        ],
        [
            'name' => 'Customer Info Combined',
            'description' => 'Combined customer name and address (like billto templates)',
            'block_type' => 'customer_info_combined',
            'category' => 'header',
            'template_data' => [
                'fields' => [
                    [
                        'path' => 'customer.name',
                        'format' => 'text',
                        'prefix' => 'Bill to: ',
                        'style_class' => 'fw-bold'
                    ],
                    [
                        'path' => 'customer.address',
                        'format' => 'html',
                        'allow_html' => true
                    ]
                ],
                'layout' => 'vertical',
                'template_style' => 'billto'
            ],
            'field_mappings' => [
                'fields' => ['customer.name', 'customer.address'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div><div class="fw-bold">Bill to: Sample Client Corp</div><div>Jl. Client Avenue No. 456, Surabaya 60123</div></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'info', 'header', 'combined', 'billto'],
            'usage_count' => 0
        ],
        
        // SIGNATURE BLOCK
        [
            'name' => 'Company Signature',
            'description' => 'Company signature image and name',
            'block_type' => 'company_signature',
            'category' => 'footer',
            'template_data' => [
                'signature_path' => 'company.signature',
                'signature_name_path' => 'company.signature_name',
                'storage_prefix' => 'storage/',
                'max_height' => '5rem'
            ],
            'field_mappings' => [
                'fields' => ['company.signature', 'company.signature_name'],
                'format' => 'signature'
            ],
            'default_styling' => [
                'alignment' => 'center'
            ],
            'preview_html' => '<div class="text-center"><div class="w-20 h-16 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500 rounded mb-2">[SIGNATURE]</div><u>Signature Name</u></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['signature', 'company', 'footer'],
            'usage_count' => 0
        ],
        
        // UTILITY BLOCKS
        [
            'name' => 'Free Text Block',
            'description' => 'Fully customizable text content',
            'block_type' => 'free_text',
            'category' => 'general',
            'template_data' => [
                'text' => 'Enter your custom text here',
                'allow_html' => true,
                'editable' => true
            ],
            'field_mappings' => [],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div class="border-2 border-dashed border-blue-300 bg-blue-50 p-3 text-blue-700 text-sm rounded"><span class="font-medium">✏️ Free Text Block</span><br><span class="text-xs">Click to edit custom content</span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['text', 'custom', 'editable'],
            'usage_count' => 0
        ],
        [
            'name' => 'Spacer',
            'description' => 'Empty space for layout',
            'block_type' => 'spacer',
            'category' => 'general',
            'template_data' => [
                'height' => '20px',
                'adjustable' => true
            ],
            'field_mappings' => [],
            'default_styling' => [
                'min_height' => 20
            ],
            'preview_html' => '<div class="h-5 border-b border-dashed border-gray-300 flex items-center justify-center text-xs text-gray-400 bg-gray-50 rounded">--- spacer (20px) ---</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['spacer', 'layout'],
            'usage_count' => 0
        ]
    ];
    
    $createdBlocks = [];
    foreach ($blockTemplates as $blockData) {
        $block = App\Models\BlockTemplate::create($blockData);
        $createdBlocks[] = $block;
        echo "   ✅ Created: {$block->name} ({$block->block_type})\n";
    }
    
    echo "\n=== Data Structure Update Complete ===\n";
    echo "✅ Block templates now use existing payload structure\n";
    echo "✅ Compatible with current dynamic.blade.php template\n";
    echo "✅ Supports all existing template variations (kop, billto, etc.)\n";
    
    $totalBlocks = count($createdBlocks);
    echo "\n📊 Created {$totalBlocks} block templates:\n";
    echo "   - Individual fields: Logo, Name, Address, Phone, Email\n";
    echo "   - Invoice fields: Number, Date, Due Date\n";
    echo "   - Customer fields: Name, Address\n";
    echo "   - Combined blocks: Company Info, Invoice Details, Customer Info\n";
    echo "   - Signature block: Company signature\n";
    echo "   - Utility blocks: Free Text, Spacer\n";
    
    echo "\n🎯 Data Structure Mapping:\n";
    echo "   ✅ \$payload['company'] → Company blocks\n";
    echo "   ✅ \$payload['invoice'] → Invoice blocks\n";
    echo "   ✅ \$payload['customer'] → Customer blocks\n";
    echo "   ✅ Template styles → Combined blocks (kop, billto)\n";
    echo "   ✅ Existing formatting → Date, currency, HTML handling\n";
    
    echo "\n🚀 Visual Builder now fully compatible with existing templates!\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
