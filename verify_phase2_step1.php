<?php

/**
 * Verification Script for Phase 2 Step 1: Filament Resource Creation
 * 
 * Run this script via: php artisan tinker
 * Then copy-paste the content of this file
 */

echo "=== Phase 2 Step 1 Verification ===\n";

// 1. Check if InvoiceTemplateResource exists
echo "1. Checking InvoiceTemplateResource...\n";
if (class_exists('App\Filament\Admin\Resources\InvoiceTemplateResource')) {
    echo "   ✅ InvoiceTemplateResource class exists\n";
} else {
    echo "   ❌ InvoiceTemplateResource class NOT found\n";
}

// 2. Check if all pages exist
echo "\n2. Checking Resource Pages...\n";
$pages = [
    'App\Filament\Admin\Resources\InvoiceTemplateResource\Pages\ListInvoiceTemplates',
    'App\Filament\Admin\Resources\InvoiceTemplateResource\Pages\CreateInvoiceTemplate',
    'App\Filament\Admin\Resources\InvoiceTemplateResource\Pages\ViewInvoiceTemplate',
    'App\Filament\Admin\Resources\InvoiceTemplateResource\Pages\EditInvoiceTemplate',
];

foreach ($pages as $page) {
    if (class_exists($page)) {
        echo "   ✅ " . basename($page) . " exists\n";
    } else {
        echo "   ❌ " . basename($page) . " NOT found\n";
    }
}

// 3. Check if models and relationships work
echo "\n3. Testing Model Relationships...\n";
try {
    // Test creating a template
    $user = App\Models\User::first();
    if (!$user) {
        echo "   ⚠️  No users found, creating test user...\n";
        $user = App\Models\User::factory()->create();
    }
    
    $template = App\Models\InvoiceTemplate::create([
        'name' => 'Test Template - ' . now()->format('Y-m-d H:i:s'),
        'description' => 'Test template for verification',
        'type' => 'custom',
        'is_active' => true,
        'created_by' => $user->id,
        'template_data' => [
            'page_size' => 'legal',
            'orientation' => 'portrait',
            'font_family' => 'DejaVu Sans',
            'font_size' => 12,
            'margins' => [
                'top' => 15,
                'right' => 15,
                'bottom' => 15,
                'left' => 15
            ]
        ]
    ]);
    
    echo "   ✅ Template created successfully (ID: {$template->id})\n";
    
    // Test relationships
    if ($template->creator) {
        echo "   ✅ Creator relationship works\n";
    } else {
        echo "   ❌ Creator relationship failed\n";
    }
    
    // Test scopes
    $activeTemplates = App\Models\InvoiceTemplate::active()->count();
    echo "   ✅ Active templates scope works (found {$activeTemplates} active templates)\n";
    
    $customTemplates = App\Models\InvoiceTemplate::custom()->count();
    echo "   ✅ Custom templates scope works (found {$customTemplates} custom templates)\n";
    
    // Clean up test data
    $template->delete();
    echo "   ✅ Test template cleaned up\n";
    
} catch (Exception $e) {
    echo "   ❌ Model test failed: " . $e->getMessage() . "\n";
}

// 4. Check template components seeded
echo "\n4. Checking Template Components...\n";
try {
    $componentCounts = [
        'kop' => App\Models\TemplateComponent::byType('kop')->count(),
        'billto' => App\Models\TemplateComponent::byType('billto')->count(),
        'table' => App\Models\TemplateComponent::byType('table')->count(),
        'inwords' => App\Models\TemplateComponent::byType('inwords')->count(),
        'bankinfo' => App\Models\TemplateComponent::byType('bankinfo')->count(),
    ];
    
    $totalComponents = array_sum($componentCounts);
    echo "   ✅ Total components seeded: {$totalComponents}\n";
    
    foreach ($componentCounts as $type => $count) {
        echo "   - {$type}: {$count} components\n";
    }
    
    if ($totalComponents >= 17) {
        echo "   ✅ All expected components are seeded\n";
    } else {
        echo "   ⚠️  Expected 17+ components, found {$totalComponents}\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Component check failed: " . $e->getMessage() . "\n";
}

// 5. Check if resource is discoverable by Filament
echo "\n5. Checking Filament Resource Discovery...\n";
try {
    $resourcePath = app_path('Filament/Admin/Resources/InvoiceTemplateResource.php');
    if (file_exists($resourcePath)) {
        echo "   ✅ Resource file exists at correct path\n";
        
        // Check if resource has correct namespace
        $content = file_get_contents($resourcePath);
        if (strpos($content, 'namespace App\Filament\Admin\Resources;') !== false) {
            echo "   ✅ Resource has correct namespace\n";
        } else {
            echo "   ❌ Resource namespace incorrect\n";
        }
        
        if (strpos($content, 'protected static ?string $navigationGroup = \'Template Management\';') !== false) {
            echo "   ✅ Navigation group configured\n";
        } else {
            echo "   ❌ Navigation group not configured\n";
        }
        
    } else {
        echo "   ❌ Resource file not found\n";
    }
} catch (Exception $e) {
    echo "   ❌ Resource discovery check failed: " . $e->getMessage() . "\n";
}

echo "\n=== Verification Complete ===\n";
echo "If all checks show ✅, Phase 2 Step 1 is successfully implemented!\n";
echo "\nNext steps:\n";
echo "1. Access /admin in your browser\n";
echo "2. Look for 'Template Management' group in navigation\n";
echo "3. Click 'Invoice Templates' to test the resource\n";
echo "4. Try creating a new template\n";
