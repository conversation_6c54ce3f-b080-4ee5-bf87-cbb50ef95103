<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('layout_sections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('template_layout_id')->constrained()->onDelete('cascade');
            $table->enum('section_type', ['header', 'body', 'footer']);
            $table->string('section_name')->nullable(); // Custom section names
            $table->integer('column_count')->default(1); // 1-12 columns
            $table->string('column_layout')->default('equal'); // equal, custom
            $table->json('column_widths')->nullable(); // [25, 50, 25] for custom widths
            $table->json('settings')->nullable(); // Section-specific settings
            $table->json('styling')->nullable(); // CSS styling options
            $table->integer('order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->integer('min_height')->default(50); // Minimum height in pixels
            $table->string('background_color')->nullable();
            $table->string('border_style')->nullable();
            $table->json('padding')->nullable(); // {top: 10, right: 10, bottom: 10, left: 10}
            $table->json('margin')->nullable(); // {top: 10, right: 10, bottom: 10, left: 10}
            $table->timestamps();
            
            // Indexes
            $table->index(['template_layout_id', 'section_type']);
            $table->index(['template_layout_id', 'order']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('layout_sections');
    }
};
