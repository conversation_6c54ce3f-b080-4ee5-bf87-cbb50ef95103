<?php

/**
 * Debug Livewire Section Settings Step by Step
 * Run via: php artisan tinker
 */

echo "=== Debug Livewire Section Settings ===\n";

try {
    // 1. Create minimal test setup
    echo "1. Creating minimal test setup...\n";
    
    $testTemplate = App\Models\TemplateLayout::updateOrCreate(
        ['name' => 'Livewire Debug Test'],
        [
            'description' => 'Minimal template for Livewire debugging',
            'company_id' => null,
            'version' => 'v2',
            'created_by' => 1,
        ]
    );
    
    // Clear and create ONE section
    App\Models\LayoutSection::where('template_layout_id', $testTemplate->id)->delete();
    
    $section = App\Models\LayoutSection::create([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'header',
        'section_name' => 'Test Header',
        'column_count' => 1,
        'column_layout' => 'equal',
        'column_widths' => null,
        'order' => 0,
        'is_active' => true,
        'min_height' => 100,
        'background_color' => null,
    ]);
    
    echo "   ✅ Template: {$testTemplate->id}\n";
    echo "   ✅ Section: {$section->id}\n";
    echo "   📊 Initial state: {$section->column_count} columns\n";
    
    // 2. Test Livewire component instantiation
    echo "\n2. Testing Livewire component...\n";
    
    try {
        $component = new App\Filament\Admin\Pages\VisualTemplateBuilder();
        $component->template = $testTemplate;
        echo "   ✅ Livewire component created\n";
        
        // Test initial sectionSettings
        echo "   📊 Initial sectionSettings:\n";
        foreach ($component->sectionSettings as $key => $value) {
            $displayValue = is_array($value) ? json_encode($value) : ($value ?? 'null');
            echo "      - {$key}: {$displayValue}\n";
        }
        
    } catch (\Exception $e) {
        echo "   ❌ Livewire component error: {$e->getMessage()}\n";
        return;
    }
    
    // 3. Test loadSectionSettings method
    echo "\n3. Testing loadSectionSettings method...\n";
    
    try {
        $component->selectedElementId = $section->id;
        $component->loadSectionSettings($section->id);
        
        echo "   ✅ loadSectionSettings called\n";
        echo "   📊 Loaded sectionSettings:\n";
        foreach ($component->sectionSettings as $key => $value) {
            $displayValue = is_array($value) ? json_encode($value) : ($value ?? 'null');
            echo "      - {$key}: {$displayValue}\n";
        }
        
        // Verify loaded values match section
        $matches = [
            'section_name' => $component->sectionSettings['section_name'] === $section->section_name,
            'column_count' => $component->sectionSettings['column_count'] == $section->column_count,
            'column_layout' => $component->sectionSettings['column_layout'] === $section->column_layout,
            'min_height' => $component->sectionSettings['min_height'] == $section->min_height,
        ];
        
        echo "   🔍 Data matching:\n";
        foreach ($matches as $field => $match) {
            echo "      - {$field}: " . ($match ? 'MATCH' : 'MISMATCH') . "\n";
        }
        
    } catch (\Exception $e) {
        echo "   ❌ loadSectionSettings error: {$e->getMessage()}\n";
    }
    
    // 4. Test manual sectionSettings update
    echo "\n4. Testing manual sectionSettings update...\n";
    
    $component->sectionSettings['column_count'] = 3;
    $component->sectionSettings['column_layout'] = 'equal';
    $component->sectionSettings['background_color'] = '#f0f0f0';
    $component->sectionSettings['min_height'] = 150;
    
    echo "   📝 Updated sectionSettings:\n";
    foreach ($component->sectionSettings as $key => $value) {
        $displayValue = is_array($value) ? json_encode($value) : ($value ?? 'null');
        echo "      - {$key}: {$displayValue}\n";
    }
    
    // 5. Test updateSectionSettings method
    echo "\n5. Testing updateSectionSettings method...\n";
    
    try {
        echo "   🔧 Calling updateSectionSettings...\n";
        $component->updateSectionSettings();
        
        // Check if section was updated
        $section->refresh();
        echo "   📊 Section after update:\n";
        echo "      - column_count: {$section->column_count}\n";
        echo "      - column_layout: {$section->column_layout}\n";
        echo "      - background_color: " . ($section->background_color ?? 'null') . "\n";
        echo "      - min_height: {$section->min_height}\n";
        
        // Verify updates
        $success = [
            'column_count' => $section->column_count == 3,
            'column_layout' => $section->column_layout === 'equal',
            'background_color' => $section->background_color === '#f0f0f0',
            'min_height' => $section->min_height == 150,
        ];
        
        echo "   🔍 Update success:\n";
        foreach ($success as $field => $ok) {
            echo "      - {$field}: " . ($ok ? 'SUCCESS' : 'FAILED') . "\n";
        }
        
        $allSuccess = array_reduce($success, function($carry, $item) {
            return $carry && $item;
        }, true);
        
        if ($allSuccess) {
            echo "   ✅ updateSectionSettings WORKS!\n";
        } else {
            echo "   ❌ updateSectionSettings FAILED\n";
        }
        
    } catch (\Exception $e) {
        echo "   ❌ updateSectionSettings error: {$e->getMessage()}\n";
        echo "   📋 Stack trace: " . $e->getTraceAsString() . "\n";
    }
    
    // 6. Test different column counts
    echo "\n6. Testing different column counts...\n";
    
    $testCounts = [1, 2, 4];
    foreach ($testCounts as $count) {
        echo "   🔧 Testing column_count: {$count}\n";
        
        $component->sectionSettings['column_count'] = $count;
        $component->sectionSettings['column_layout'] = 'equal';
        
        try {
            $component->updateSectionSettings();
            $section->refresh();
            
            $success = $section->column_count == $count;
            echo "      📊 Result: " . ($success ? 'SUCCESS' : 'FAILED') . " (actual: {$section->column_count})\n";
            
        } catch (\Exception $e) {
            echo "      ❌ Error: {$e->getMessage()}\n";
        }
    }
    
    // 7. Test column widths
    echo "\n7. Testing column widths...\n";
    
    $testWidths = [
        [50, 50],
        [25, 75],
        [33, 33, 34]
    ];
    
    foreach ($testWidths as $widths) {
        $columnCount = count($widths);
        echo "   🔧 Testing {$columnCount} columns with widths: " . json_encode($widths) . "\n";
        
        $component->sectionSettings['column_count'] = $columnCount;
        $component->sectionSettings['column_layout'] = 'custom';
        $component->sectionSettings['column_widths'] = $widths;
        
        try {
            $component->updateSectionSettings();
            $section->refresh();
            
            $countOk = $section->column_count == $columnCount;
            $widthsOk = json_encode($section->column_widths) === json_encode($widths);
            
            echo "      📊 Column count: " . ($countOk ? 'SUCCESS' : 'FAILED') . " (actual: {$section->column_count})\n";
            echo "      📊 Column widths: " . ($widthsOk ? 'SUCCESS' : 'FAILED') . " (actual: " . json_encode($section->column_widths) . ")\n";
            
        } catch (\Exception $e) {
            echo "      ❌ Error: {$e->getMessage()}\n";
        }
    }
    
    // 8. Check Laravel logs
    echo "\n8. Checking recent Laravel logs...\n";
    
    try {
        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile)) {
            $logs = file_get_contents($logFile);
            $recentLogs = array_slice(explode("\n", $logs), -20);
            
            $relevantLogs = array_filter($recentLogs, function($line) {
                return strpos($line, 'updateSectionSettings') !== false || 
                       strpos($line, 'Section found') !== false ||
                       strpos($line, 'Prepared update data') !== false;
            });
            
            if (!empty($relevantLogs)) {
                echo "   📋 Recent relevant logs:\n";
                foreach ($relevantLogs as $log) {
                    echo "      " . trim($log) . "\n";
                }
            } else {
                echo "   📋 No relevant logs found\n";
            }
        } else {
            echo "   📋 Log file not found\n";
        }
    } catch (\Exception $e) {
        echo "   ❌ Could not read logs: {$e->getMessage()}\n";
    }
    
    // 9. Final test URL
    echo "\n9. Test URL...\n";
    
    $builderUrl = "/admin/visual-template-builder?template={$testTemplate->id}";
    echo "   🎨 Visual Builder URL: {$builderUrl}\n";
    
    // 10. Summary
    echo "\n=== Debug Summary ===\n";
    
    $finalSection = App\Models\LayoutSection::find($section->id);
    echo "📊 Final section state:\n";
    echo "   - column_count: {$finalSection->column_count}\n";
    echo "   - column_layout: {$finalSection->column_layout}\n";
    echo "   - column_widths: " . json_encode($finalSection->column_widths) . "\n";
    echo "   - background_color: " . ($finalSection->background_color ?? 'null') . "\n";
    echo "   - min_height: {$finalSection->min_height}\n";
    
    echo "\n🔍 Next steps if still not working:\n";
    echo "1. Check browser console for JavaScript errors\n";
    echo "2. Verify wire:model.live or wire:model.defer\n";
    echo "3. Check if form is inside proper Livewire component\n";
    echo "4. Test with wire:click instead of form submit\n";
    echo "5. Add wire:loading indicators\n";
    
    echo "\n📋 Manual test:\n";
    echo "1. Open: {$builderUrl}\n";
    echo "2. Click ⚙️ on section\n";
    echo "3. Change column count\n";
    echo "4. Click 'Save Section'\n";
    echo "5. Check browser network tab for Livewire requests\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
