<?php

/**
 * Create Individual Field Components for Maximum Flexibility
 * Run via: php artisan tinker
 */

echo "=== Creating Individual Field Components ===\n";

try {
    // 1. Clear existing system blocks
    echo "1. Clearing existing system blocks...\n";
    
    App\Models\BlockTemplate::where('is_system', true)->delete();
    echo "   ✅ Existing system blocks cleared\n";
    
    // 2. Create individual field components
    echo "\n2. Creating individual field components...\n";
    
    $individualComponents = [
        // COMPANY FIELDS - INDIVIDUAL
        [
            'name' => 'Company Logo',
            'description' => 'Company logo image only',
            'block_type' => 'company_logo',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.logo',
                'alt_text' => 'Company Logo',
                'width' => '100px',
                'height' => 'auto',
                'storage_prefix' => 'storage/'
            ],
            'field_mappings' => [
                'field' => 'company.logo',
                'format' => 'image'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'margin' => ['top' => 0, 'right' => 10, 'bottom' => 0, 'left' => 0]
            ],
            'preview_html' => '<div class="w-16 h-12 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500 rounded">[LOGO]</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'logo', 'image'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Name',
            'description' => 'Company name text only',
            'block_type' => 'company_name',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.name',
                'heading_size_path' => 'company.heading_size',
                'text_color_path' => 'company.text_color',
                'default_heading' => 'h3',
                'default_color' => '#000000'
            ],
            'field_mappings' => [
                'field' => 'company.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 18,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="text-lg font-bold text-gray-800">PT. Sample Company</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'name', 'text'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Address',
            'description' => 'Company address text only',
            'block_type' => 'company_address',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.address',
                'strip_tags' => true
            ],
            'field_mappings' => [
                'field' => 'company.address',
                'format' => 'html'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Sample Street No. 123<br>Jakarta Pusat 10220</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'address', 'text'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Phone',
            'description' => 'Company phone number only',
            'block_type' => 'company_phone',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.phone',
                'prefix' => 'Phone: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.phone',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Phone: +62 21 1234 5678</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'phone', 'contact'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Email',
            'description' => 'Company email address only',
            'block_type' => 'company_email',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.email',
                'prefix' => 'Email: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.email',
                'format' => 'email'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Email: <span class="text-blue-600"><EMAIL></span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'email', 'contact'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Website',
            'description' => 'Company website URL only',
            'block_type' => 'company_website',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.website',
                'prefix' => 'Website: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.website',
                'format' => 'url'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Website: <span class="text-blue-600">www.company.com</span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'website', 'contact'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Fax',
            'description' => 'Company fax number only',
            'block_type' => 'company_fax',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.fax',
                'prefix' => 'Fax: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.fax',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Fax: +62 21 1234 5679</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'fax', 'contact'],
            'usage_count' => 0
        ],
        
        // INVOICE FIELDS - INDIVIDUAL
        [
            'name' => 'Invoice Number',
            'description' => 'Invoice number only',
            'block_type' => 'invoice_number',
            'category' => 'invoice',
            'template_data' => [
                'field_path' => 'invoice.invoice_no',
                'prefix' => 'Invoice No: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_no',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 14,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="text-right font-bold">Invoice No: INV-2024-001</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'number'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Date',
            'description' => 'Invoice date only',
            'block_type' => 'invoice_date',
            'category' => 'invoice',
            'template_data' => [
                'field_path' => 'invoice.invoice_date',
                'prefix' => 'Invoice Date: ',
                'suffix' => '',
                'date_format' => 'd/m/Y'
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_date',
                'format' => 'date'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-right text-sm text-gray-600">Invoice Date: 15/01/2024</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'date'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Due Date',
            'description' => 'Invoice due date only',
            'block_type' => 'invoice_due_date',
            'category' => 'invoice',
            'template_data' => [
                'field_path' => 'invoice.invoice_due',
                'prefix' => 'Due Date: ',
                'suffix' => '',
                'date_format' => 'd/m/Y'
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_due',
                'format' => 'date'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-right text-sm text-gray-600">Due Date: 15/02/2024</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'due_date'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Amount',
            'description' => 'Invoice total amount only',
            'block_type' => 'invoice_amount',
            'category' => 'invoice',
            'template_data' => [
                'field_path' => 'invoice.invoice_amount',
                'prefix' => 'Total: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_amount',
                'format' => 'currency'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 14,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="text-right font-bold">Total: Rp 1,500,000</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'amount', 'currency'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Remarks',
            'description' => 'Invoice remarks/notes only',
            'block_type' => 'invoice_remarks',
            'category' => 'invoice',
            'template_data' => [
                'field_path' => 'invoice.remarks',
                'prefix' => '',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'invoice.remarks',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Thank you for your business</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'remarks', 'notes'],
            'usage_count' => 0
        ],
        
        // CUSTOMER FIELDS - INDIVIDUAL
        [
            'name' => 'Customer Name',
            'description' => 'Customer/client name only',
            'block_type' => 'customer_name',
            'category' => 'customer',
            'template_data' => [
                'field_path' => 'customer.name',
                'prefix' => 'Bill to: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'customer.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 14,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="font-bold">Bill to: Sample Client Corp</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'name', 'billto'],
            'usage_count' => 0
        ],
        [
            'name' => 'Customer Address',
            'description' => 'Customer/client address only',
            'block_type' => 'customer_address',
            'category' => 'customer',
            'template_data' => [
                'field_path' => 'customer.address',
                'allow_html' => true
            ],
            'field_mappings' => [
                'field' => 'customer.address',
                'format' => 'html'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Client Avenue No. 456<br>Surabaya 60123</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'address', 'billto'],
            'usage_count' => 0
        ],
        [
            'name' => 'Customer Phone',
            'description' => 'Customer phone number only',
            'block_type' => 'customer_phone',
            'category' => 'customer',
            'template_data' => [
                'field_path' => 'customer.phone',
                'prefix' => 'Phone: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'customer.phone',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Phone: +62 31 7777 5678</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'phone', 'contact'],
            'usage_count' => 0
        ],
        [
            'name' => 'Customer Email',
            'description' => 'Customer email address only',
            'block_type' => 'customer_email',
            'category' => 'customer',
            'template_data' => [
                'field_path' => 'customer.email',
                'prefix' => 'Email: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'customer.email',
                'format' => 'email'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Email: <span class="text-blue-600"><EMAIL></span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'email', 'contact'],
            'usage_count' => 0
        ],
        
        // SIGNATURE FIELDS - INDIVIDUAL
        [
            'name' => 'Company Signature Image',
            'description' => 'Company signature image only',
            'block_type' => 'company_signature_image',
            'category' => 'signature',
            'template_data' => [
                'field_path' => 'company.signature',
                'storage_prefix' => 'storage/',
                'max_height' => '5rem'
            ],
            'field_mappings' => [
                'field' => 'company.signature',
                'format' => 'image'
            ],
            'default_styling' => [
                'alignment' => 'center'
            ],
            'preview_html' => '<div class="text-center"><div class="w-20 h-16 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500 rounded">[SIGNATURE]</div></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['signature', 'image'],
            'usage_count' => 0
        ],
        [
            'name' => 'Signature Name',
            'description' => 'Signature name text only',
            'block_type' => 'signature_name',
            'category' => 'signature',
            'template_data' => [
                'field_path' => 'company.signature_name',
                'prefix' => '',
                'suffix' => '',
                'underline' => true
            ],
            'field_mappings' => [
                'field' => 'company.signature_name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'center',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-center"><u>John Doe</u></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['signature', 'name'],
            'usage_count' => 0
        ],
        
        // UTILITY BLOCKS
        [
            'name' => 'Free Text Block',
            'description' => 'Fully customizable text content',
            'block_type' => 'free_text',
            'category' => 'utility',
            'template_data' => [
                'text' => 'Enter your custom text here',
                'allow_html' => true,
                'editable' => true
            ],
            'field_mappings' => [],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div class="border-2 border-dashed border-blue-300 bg-blue-50 p-3 text-blue-700 text-sm rounded"><span class="font-medium">✏️ Free Text Block</span><br><span class="text-xs">Click to edit custom content</span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['text', 'custom', 'editable'],
            'usage_count' => 0
        ],
        [
            'name' => 'Spacer',
            'description' => 'Empty space for layout spacing',
            'block_type' => 'spacer',
            'category' => 'utility',
            'template_data' => [
                'height' => '20px',
                'adjustable' => true
            ],
            'field_mappings' => [],
            'default_styling' => [
                'min_height' => 20
            ],
            'preview_html' => '<div class="h-5 border-b border-dashed border-gray-300 flex items-center justify-center text-xs text-gray-400 bg-gray-50 rounded">--- spacer (20px) ---</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['spacer', 'layout'],
            'usage_count' => 0
        ],
        [
            'name' => 'Horizontal Line',
            'description' => 'Horizontal divider line',
            'block_type' => 'horizontal_line',
            'category' => 'utility',
            'template_data' => [
                'thickness' => '1px',
                'color' => '#cccccc',
                'style' => 'solid',
                'width' => '100%'
            ],
            'field_mappings' => [],
            'default_styling' => [
                'margin' => ['top' => 10, 'bottom' => 10]
            ],
            'preview_html' => '<hr class="border-gray-300 my-2">',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['line', 'divider', 'separator'],
            'usage_count' => 0
        ]
    ];
    
    $createdComponents = [];
    foreach ($individualComponents as $componentData) {
        $component = App\Models\BlockTemplate::create($componentData);
        $createdComponents[] = $component;
        echo "   ✅ Created: {$component->name} ({$component->category})\n";
    }
    
    echo "\n=== Individual Field Components Created ===\n";
    
    $totalComponents = count($createdComponents);
    $categories = collect($createdComponents)->groupBy('category');
    
    echo "📊 Total components: {$totalComponents}\n";
    echo "📦 Categories breakdown:\n";
    foreach ($categories as $category => $components) {
        $count = $components->count();
        echo "   - {$category}: {$count} components\n";
        foreach ($components as $component) {
            echo "     • {$component->name}\n";
        }
    }
    
    echo "\n🎯 Component Organization:\n";
    echo "✅ Company Fields: Logo, Name, Address, Phone, Email, Website, Fax\n";
    echo "✅ Invoice Fields: Number, Date, Due Date, Amount, Remarks\n";
    echo "✅ Customer Fields: Name, Address, Phone, Email\n";
    echo "✅ Signature Fields: Image, Name\n";
    echo "✅ Utility Fields: Free Text, Spacer, Horizontal Line\n";
    
    echo "\n🎨 Layout Flexibility Examples:\n";
    echo "📋 Example 1: Minimal Header\n";
    echo "   - Company Logo + Company Name\n";
    echo "   - Invoice Number + Invoice Date\n";
    
    echo "\n📋 Example 2: Detailed Header\n";
    echo "   - Company Logo + Company Name + Company Address\n";
    echo "   - Company Phone + Company Email + Company Website\n";
    echo "   - Invoice Number + Invoice Date + Invoice Due Date\n";
    
    echo "\n📋 Example 3: Custom Layout\n";
    echo "   - Free Text Block (custom header)\n";
    echo "   - Company Logo + Company Name\n";
    echo "   - Spacer\n";
    echo "   - Customer Name + Customer Address\n";
    echo "   - Horizontal Line\n";
    echo "   - Invoice details as needed\n";
    
    echo "\n📋 Example 4: Contact-Heavy Layout\n";
    echo "   - Company Name + Company Address\n";
    echo "   - Company Phone + Company Email\n";
    echo "   - Company Website + Company Fax\n";
    echo "   - Customer Phone + Customer Email\n";
    
    echo "\n🚀 Maximum Flexibility Achieved!\n";
    echo "Now users can:\n";
    echo "✅ Pick exactly the fields they need\n";
    echo "✅ Arrange fields in any order\n";
    echo "✅ Mix and match company/invoice/customer fields\n";
    echo "✅ Add custom text and spacing as needed\n";
    echo "✅ Create unique layouts for different use cases\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
