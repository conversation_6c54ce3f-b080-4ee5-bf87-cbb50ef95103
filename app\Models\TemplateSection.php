<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TemplateSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'template_id',
        'section_type',
        'section_name',
        'section_config',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'section_config' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the template that owns this section
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(InvoiceTemplate::class, 'template_id');
    }

    /**
     * Scope for active sections
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for sections by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('section_type', $type);
    }

    /**
     * Scope for ordered sections
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get section configuration value
     */
    public function getConfig($key, $default = null)
    {
        return data_get($this->section_config, $key, $default);
    }

    /**
     * Set section configuration value
     */
    public function setConfig($key, $value)
    {
        $config = $this->section_config ?? [];
        data_set($config, $key, $value);
        $this->section_config = $config;
        return $this;
    }

    /**
     * Check if section is header type
     */
    public function isHeader(): bool
    {
        return $this->section_type === 'header';
    }

    /**
     * Check if section is billto type
     */
    public function isBillTo(): bool
    {
        return $this->section_type === 'billto';
    }

    /**
     * Check if section is table type
     */
    public function isTable(): bool
    {
        return $this->section_type === 'table';
    }

    /**
     * Check if section is inwords type
     */
    public function isInWords(): bool
    {
        return $this->section_type === 'inwords';
    }

    /**
     * Check if section is footer type
     */
    public function isFooter(): bool
    {
        return $this->section_type === 'footer';
    }

    /**
     * Check if section is custom type
     */
    public function isCustom(): bool
    {
        return $this->section_type === 'custom';
    }
}
