# Font Configuration for Invoice Template Maker
# ================================================

# Enable/Disable Google Fonts Integration
# Set to true to include Google Fonts in font selection
# Set to false for production (recommended for PDF compatibility)
GOOGLE_FONTS_ENABLED=false

# Google Fonts API Key (required if GOOGLE_FONTS_ENABLED=true)
# Get your API key from: https://developers.google.com/fonts/docs/developer_api
GOOGLE_FONTS_API_KEY=

# Font Configuration Examples:
# ============================

# Production Environment (Recommended):
# GOOGLE_FONTS_ENABLED=false
# - Only PDF-compatible fonts (DejaVu, Arial, Times, etc.)
# - Fast, reliable, no external dependencies
# - Guaranteed to work on shared hosting

# Development Environment (Optional):
# GOOGLE_FONTS_ENABLED=true
# GOOGLE_FONTS_API_KEY=your_api_key_here
# - Includes curated Google Fonts (serif/sans-serif only)
# - More variety for template design
# - Preview only - PDFs still use fallback fonts

# Notes:
# ======
# 1. Google Fonts are filtered to serif/sans-serif only (no display, handwriting, monospace)
# 2. Curated list includes popular fonts: Roboto, Open Sans, Lato, Montserrat, etc.
# 3. All Google Fonts have fallback to system fonts for PDF generation
# 4. DejaVu fonts are always recommended for best PDF compatibility
