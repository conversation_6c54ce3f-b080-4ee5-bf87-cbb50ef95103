<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TableSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'template_layout_id',
        'section_type',
        'section_name',
        'rows',
        'columns',
        'table_structure',
        'styling',
        'order',
        'is_active',
    ];

    protected $casts = [
        'table_structure' => 'array',
        'styling' => 'array',
        'is_active' => 'boolean',
        'rows' => 'integer',
        'columns' => 'integer',
        'order' => 'integer',
    ];

    protected $attributes = [
        'rows' => 1,
        'columns' => 1,
        'is_active' => true,
        'order' => 0,
    ];

    // Relationships
    public function templateLayout(): BelongsTo
    {
        return $this->belongsTo(TemplateLayout::class);
    }

    public function cells(): HasMany
    {
        return $this->hasMany(TableCell::class)->orderBy('row_index')->orderBy('col_index');
    }

    // Helper methods
    public function getCellAt(int $row, int $col): ?TableCell
    {
        return $this->cells()
            ->where('row_index', $row)
            ->where('col_index', $col)
            ->first();
    }

    public function createDefaultCell(int $row = 0, int $col = 0): TableCell
    {
        return $this->cells()->create([
            'row_index' => $row,
            'col_index' => $col,
            'rowspan' => 1,
            'colspan' => 1,
            'field_type' => 'text',
            'static_content' => 'Click to edit',
        ]);
    }

    public function addRow(): void
    {
        $this->increment('rows');
        
        // Create cells for the new row
        for ($col = 0; $col < $this->columns; $col++) {
            $this->createDefaultCell($this->rows - 1, $col);
        }
        
        $this->updateTableStructure();
    }

    public function addColumn(): void
    {
        $this->increment('columns');
        
        // Create cells for the new column in all existing rows
        for ($row = 0; $row < $this->rows; $row++) {
            $this->createDefaultCell($row, $this->columns - 1);
        }
        
        $this->updateTableStructure();
    }

    public function mergeCells(int $startRow, int $startCol, int $endRow, int $endCol): void
    {
        $rowspan = $endRow - $startRow + 1;
        $colspan = $endCol - $startCol + 1;
        
        // Get the main cell (top-left)
        $mainCell = $this->getCellAt($startRow, $startCol);
        
        if ($mainCell) {
            $mainCell->update([
                'rowspan' => $rowspan,
                'colspan' => $colspan,
            ]);
            
            // Remove or mark other cells as merged
            for ($row = $startRow; $row <= $endRow; $row++) {
                for ($col = $startCol; $col <= $endCol; $col++) {
                    if ($row === $startRow && $col === $startCol) {
                        continue; // Skip main cell
                    }
                    
                    $cell = $this->getCellAt($row, $col);
                    if ($cell) {
                        $cell->delete(); // Or mark as merged
                    }
                }
            }
        }
        
        $this->updateTableStructure();
    }

    public function splitCell(int $row, int $col): void
    {
        $cell = $this->getCellAt($row, $col);
        
        if ($cell && ($cell->rowspan > 1 || $cell->colspan > 1)) {
            $originalRowspan = $cell->rowspan;
            $originalColspan = $cell->colspan;
            
            // Reset main cell to 1x1
            $cell->update([
                'rowspan' => 1,
                'colspan' => 1,
            ]);
            
            // Create new cells for the split area
            for ($r = $row; $r < $row + $originalRowspan; $r++) {
                for ($c = $col; $c < $col + $originalColspan; $c++) {
                    if ($r === $row && $c === $col) {
                        continue; // Skip main cell
                    }
                    
                    $this->createDefaultCell($r, $c);
                }
            }
        }
        
        $this->updateTableStructure();
    }

    private function updateTableStructure(): void
    {
        $structure = [];
        
        for ($row = 0; $row < $this->rows; $row++) {
            $structure[$row] = [];
            for ($col = 0; $col < $this->columns; $col++) {
                $cell = $this->getCellAt($row, $col);
                $structure[$row][$col] = $cell ? [
                    'id' => $cell->id,
                    'rowspan' => $cell->rowspan,
                    'colspan' => $cell->colspan,
                    'field_type' => $cell->field_type,
                ] : null;
            }
        }
        
        $this->update(['table_structure' => $structure]);
    }

    public function renderHtml(array $data = []): string
    {
        $html = '<table class="layout-table section-' . $this->section_type . '"';
        
        // Add table styling
        if ($this->styling) {
            $styles = [];
            foreach ($this->styling as $property => $value) {
                $styles[] = $property . ': ' . $value;
            }
            if (!empty($styles)) {
                $html .= ' style="' . implode('; ', $styles) . '"';
            }
        }
        
        $html .= '>';
        
        // Render table structure
        $renderedCells = [];
        
        for ($row = 0; $row < $this->rows; $row++) {
            $html .= '<tr>';
            
            for ($col = 0; $col < $this->columns; $col++) {
                $cellKey = $row . '-' . $col;
                
                // Skip if this cell is already rendered as part of a span
                if (isset($renderedCells[$cellKey])) {
                    continue;
                }
                
                $cell = $this->getCellAt($row, $col);
                
                if ($cell) {
                    $html .= $cell->renderHtml($data);
                    
                    // Mark spanned cells as rendered
                    for ($r = $row; $r < $row + $cell->rowspan; $r++) {
                        for ($c = $col; $c < $col + $cell->colspan; $c++) {
                            $renderedCells[$r . '-' . $c] = true;
                        }
                    }
                } else {
                    // Empty cell
                    $html .= '<td></td>';
                    $renderedCells[$cellKey] = true;
                }
            }
            
            $html .= '</tr>';
        }
        
        $html .= '</table>';
        
        return $html;
    }

    public function duplicate(): self
    {
        $newSection = $this->replicate();
        $newSection->save();
        
        // Duplicate all cells
        foreach ($this->cells as $cell) {
            $newCell = $cell->replicate();
            $newCell->table_section_id = $newSection->id;
            $newCell->save();
        }
        
        $newSection->updateTableStructure();
        
        return $newSection;
    }
}
