<?php

/**
 * Create Visual Builder v2.0 Menu Script
 * Run via: php artisan tinker
 */

echo "=== Creating Visual Builder v2.0 Menu ===\n";

try {
    // 1. Update navigation untuk add new menu
    echo "1. Adding Visual Builder v2.0 to navigation...\n";
    
    // Note: This would typically be done in AdminPanelProvider or similar
    // For now, we'll create the basic structure
    
    echo "   ✅ Menu structure planned\n";
    echo "   📋 Menu items:\n";
    echo "      - Template Management (existing)\n";
    echo "      - Visual Builder v2.0 (NEW)\n";
    echo "      - Legacy Builder v1.0 (DEPRECATED)\n";
    
    // 2. Create company template version selection
    echo "\n2. Setting up company template version selection...\n";
    
    // Check if template_version column exists
    $hasTemplateVersion = Schema::hasColumn('companies', 'template_version');
    
    if (!$hasTemplateVersion) {
        echo "   ⚠️  template_version column not found in companies table\n";
        echo "   📝 Migration needed:\n";
        echo "      Schema::table('companies', function (Blueprint \$table) {\n";
        echo "          \$table->enum('template_version', ['v1', 'v2'])->default('v1');\n";
        echo "          \$table->enum('migration_status', ['pending', 'in_progress', 'completed'])->default('pending');\n";
        echo "          \$table->json('legacy_backup')->nullable();\n";
        echo "      });\n";
    } else {
        echo "   ✅ template_version column exists\n";
    }
    
    // 3. Create sample company with v2 template
    echo "\n3. Creating sample company for v2.0 testing...\n";
    
    $testCompany = App\Models\Company::firstOrCreate([
        'name' => 'Visual Builder Test Company'
    ], [
        'address' => 'Jl. Visual Builder No. 123',
        'phone' => '021-VISUAL-123',
        'email' => '<EMAIL>',
        // 'template_version' => 'v2', // Will add when column exists
    ]);
    
    echo "   ✅ Test company created (ID: {$testCompany->id})\n";
    
    // 4. Plan menu structure
    echo "\n4. Planning menu structure...\n";
    
    $menuStructure = [
        'Template Management' => [
            'icon' => 'heroicon-o-document-text',
            'items' => [
                'Templates' => '/admin/invoice-templates',
                'Visual Builder v2.0' => '/admin/visual-template-builder',
                'Legacy Builder v1.0' => '/admin/template-builder-legacy',
            ]
        ]
    ];
    
    foreach ($menuStructure as $group => $config) {
        echo "   📁 {$group}\n";
        foreach ($config['items'] as $item => $url) {
            $status = str_contains($item, 'Legacy') ? '❌ DEPRECATED' : 
                     (str_contains($item, 'v2.0') ? '🚀 NEW' : '✅ EXISTING');
            echo "      - {$item} → {$url} {$status}\n";
        }
    }
    
    // 5. Create route planning
    echo "\n5. Planning routes...\n";
    
    $routes = [
        // New v2.0 routes
        '/admin/visual-template-builder' => 'VisualTemplateBuilder::class (NEW)',
        '/admin/visual-template-builder/{template}' => 'VisualTemplateBuilder with template (NEW)',
        '/visual-template/preview/{template}' => 'VisualTemplateController@preview (NEW)',
        
        // Legacy routes (DEPRECATED)
        '/admin/template-builder-legacy' => 'TemplateBuilder::class (DEPRECATED)',
        '/template/preview/{template}' => 'TemplatePreviewController@show (DEPRECATED)',
    ];
    
    foreach ($routes as $route => $handler) {
        $status = str_contains($handler, 'DEPRECATED') ? '❌' : '🚀';
        echo "   {$status} {$route} → {$handler}\n";
    }
    
    // 6. Create file structure planning
    echo "\n6. Planning file structure...\n";
    
    $fileStructure = [
        'NEW FILES (v2.0)' => [
            'app/Filament/Admin/Pages/VisualTemplateBuilder.php',
            'app/Http/Controllers/VisualTemplateController.php',
            'app/Models/TemplateLayout.php',
            'app/Models/LayoutSection.php',
            'app/Models/ContentBlock.php',
            'resources/views/filament/admin/pages/visual-template-builder.blade.php',
            'resources/views/visual-template/preview.blade.php',
        ],
        'QUARANTINED FILES (v1.0)' => [
            'app/Filament/Admin/Pages/TemplateBuilder.php (DEPRECATED)',
            'app/Http/Controllers/TemplatePreviewController.php (DEPRECATED)',
            'resources/views/filament/admin/pages/template-builder.blade.php (DEPRECATED)',
            'resources/views/template/preview.blade.php (DEPRECATED)',
        ]
    ];
    
    foreach ($fileStructure as $category => $files) {
        echo "   📁 {$category}:\n";
        foreach ($files as $file) {
            $status = str_contains($file, 'DEPRECATED') ? '❌' : '🚀';
            echo "      {$status} {$file}\n";
        }
    }
    
    // 7. Create migration checklist
    echo "\n7. Migration checklist...\n";
    
    $checklist = [
        'Database Migration' => [
            '[ ] Add template_version to companies table',
            '[ ] Create template_layouts table',
            '[ ] Create layout_sections table', 
            '[ ] Create content_blocks table',
            '[ ] Create block_templates table',
        ],
        'Backend Development' => [
            '[ ] Create VisualTemplateBuilder page',
            '[ ] Create VisualTemplateController',
            '[ ] Create new models (TemplateLayout, etc.)',
            '[ ] Setup new routes',
            '[ ] Create API endpoints for Vue.js',
        ],
        'Frontend Development' => [
            '[ ] Setup Vue.js 3 + Composition API',
            '[ ] Create section builder interface',
            '[ ] Implement drag & drop functionality',
            '[ ] Build content block palette',
            '[ ] Create real-time preview',
        ],
        'Integration' => [
            '[ ] Update navigation menu',
            '[ ] Create version selection interface',
            '[ ] Setup routing logic',
            '[ ] Create migration tools',
            '[ ] Update company forms',
        ],
        'Testing' => [
            '[ ] Unit tests for new models',
            '[ ] Feature tests for visual builder',
            '[ ] Integration tests',
            '[ ] Backward compatibility tests',
            '[ ] Performance tests',
        ]
    ];
    
    foreach ($checklist as $category => $items) {
        echo "   📋 {$category}:\n";
        foreach ($items as $item) {
            echo "      {$item}\n";
        }
    }
    
    echo "\n=== Visual Builder v2.0 Menu Planning Complete ===\n";
    echo "🎯 Next Steps:\n";
    echo "1. Create database migration for template_version\n";
    echo "2. Create VisualTemplateBuilder Filament page\n";
    echo "3. Setup Vue.js integration\n";
    echo "4. Update navigation menu\n";
    echo "5. Create company template version selection\n";
    
    echo "\n🚀 Ready to start Visual Builder v2.0 development!\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
