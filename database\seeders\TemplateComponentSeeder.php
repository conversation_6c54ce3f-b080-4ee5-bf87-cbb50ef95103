<?php

namespace Database\Seeders;

use App\Models\TemplateComponent;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class TemplateComponentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed Kop Components
        $this->seedKopComponents();
        
        // Seed BillTo Components
        $this->seedBillToComponents();
        
        // Seed Table Components
        $this->seedTableComponents();
        
        // Seed InWords Components
        $this->seedInWordsComponents();
        
        // Seed BankInfo Components
        $this->seedBankInfoComponents();
    }

    /**
     * Seed Kop (Header) Components
     */
    private function seedKopComponents(): void
    {
        $kopComponents = [
            [
                'name' => 'Left Aligned Kop',
                'file' => 'left.blade.php',
                'description' => 'Logo and company info aligned to the left'
            ],
            [
                'name' => 'Right Aligned Kop',
                'file' => 'right.blade.php',
                'description' => 'Logo and company info aligned to the right'
            ],
            [
                'name' => 'Center Aligned Kop',
                'file' => 'center.blade.php',
                'description' => 'Logo and company info centered'
            ],
            [
                'name' => 'Center Thara Kop',
                'file' => 'centerthara.blade.php',
                'description' => 'Special center layout for Thara template'
            ],
            [
                'name' => 'Justified Kop',
                'file' => 'justified.blade.php',
                'description' => 'Justified layout for header'
            ]
        ];

        foreach ($kopComponents as $component) {
            $filePath = resource_path("views/template/kop/{$component['file']}");
            $code = File::exists($filePath) ? File::get($filePath) : '';

            TemplateComponent::create([
                'component_type' => 'kop',
                'component_name' => $component['name'],
                'component_code' => $code,
                'is_system' => true,
                'description' => $component['description'],
                'component_config' => [
                    'original_file' => $component['file'],
                    'supports_logo' => true,
                    'supports_company_info' => true
                ]
            ]);
        }
    }

    /**
     * Seed BillTo Components
     */
    private function seedBillToComponents(): void
    {
        $billToComponents = [
            [
                'name' => 'Left Aligned Bill To',
                'file' => 'left.blade.php',
                'description' => 'Customer info and invoice details aligned left'
            ],
            [
                'name' => 'Right Aligned Bill To',
                'file' => 'right.blade.php',
                'description' => 'Customer info and invoice details aligned right'
            ],
            [
                'name' => 'Justified Bill To',
                'file' => 'justified.blade.php',
                'description' => 'Customer info and invoice details justified'
            ]
        ];

        foreach ($billToComponents as $component) {
            $filePath = resource_path("views/template/billto/{$component['file']}");
            $code = File::exists($filePath) ? File::get($filePath) : '';

            TemplateComponent::create([
                'component_type' => 'billto',
                'component_name' => $component['name'],
                'component_code' => $code,
                'is_system' => true,
                'description' => $component['description'],
                'component_config' => [
                    'original_file' => $component['file'],
                    'supports_customer_info' => true,
                    'supports_invoice_details' => true
                ]
            ]);
        }
    }

    /**
     * Seed Table Components
     */
    private function seedTableComponents(): void
    {
        $tableComponents = [
            [
                'name' => '2 Columns Table',
                'file' => '2-columns.blade.php',
                'description' => 'Table with Description and Total columns',
                'columns' => ['Description', 'Total']
            ],
            [
                'name' => '3 Columns Table',
                'file' => '3-columns.blade.php',
                'description' => 'Table with Description, Price, and Total columns',
                'columns' => ['Description', 'Price', 'Total']
            ],
            [
                'name' => '4 Columns Table',
                'file' => '4-columns.blade.php',
                'description' => 'Table with Description, Qty, Price, and Total columns',
                'columns' => ['Description', 'Qty', 'Price', 'Total']
            ]
        ];

        foreach ($tableComponents as $component) {
            $filePath = resource_path("views/template/table/{$component['file']}");
            $code = File::exists($filePath) ? File::get($filePath) : '';

            TemplateComponent::create([
                'component_type' => 'table',
                'component_name' => $component['name'],
                'component_code' => $code,
                'is_system' => true,
                'description' => $component['description'],
                'component_config' => [
                    'original_file' => $component['file'],
                    'columns' => $component['columns'],
                    'supports_styling' => true
                ]
            ]);
        }
    }

    /**
     * Seed InWords Components
     */
    private function seedInWordsComponents(): void
    {
        $inWordsComponents = [
            [
                'name' => 'Left Aligned In Words',
                'file' => 'left.blade.php',
                'description' => 'Amount in words aligned to the left'
            ],
            [
                'name' => 'Center Aligned In Words',
                'file' => 'center.blade.php',
                'description' => 'Amount in words centered'
            ],
            [
                'name' => 'Right Aligned In Words',
                'file' => 'right.blade.php',
                'description' => 'Amount in words aligned to the right'
            ]
        ];

        foreach ($inWordsComponents as $component) {
            $filePath = resource_path("views/template/inwords/{$component['file']}");
            $code = File::exists($filePath) ? File::get($filePath) : '';

            TemplateComponent::create([
                'component_type' => 'inwords',
                'component_name' => $component['name'],
                'component_code' => $code,
                'is_system' => true,
                'description' => $component['description'],
                'component_config' => [
                    'original_file' => $component['file'],
                    'supports_styling' => true
                ]
            ]);
        }
    }

    /**
     * Seed BankInfo Components
     */
    private function seedBankInfoComponents(): void
    {
        $bankInfoComponents = [
            [
                'name' => 'Model 1 - Background & Border',
                'file' => 'model-1-bg-border.blade.php',
                'description' => 'Bank info with background and border styling'
            ],
            [
                'name' => 'Model 2 - Border Only',
                'file' => 'model-2-border.blade.php',
                'description' => 'Bank info with border styling only'
            ],
            [
                'name' => 'Model 3 - No Background & Border',
                'file' => 'model-3-nobg-noborder.blade.php',
                'description' => 'Clean bank info without background or border'
            ]
        ];

        foreach ($bankInfoComponents as $component) {
            $filePath = resource_path("views/template/bankinfo/{$component['file']}");
            $code = File::exists($filePath) ? File::get($filePath) : '';

            TemplateComponent::create([
                'component_type' => 'bankinfo',
                'component_name' => $component['name'],
                'component_code' => $code,
                'is_system' => true,
                'description' => $component['description'],
                'component_config' => [
                    'original_file' => $component['file'],
                    'supports_custom_columns' => true,
                    'supports_styling' => true
                ]
            ]);
        }
    }
}
