<?php

/**
 * Test Column Settings Fix
 * Run via: php artisan tinker
 */

echo "=== Testing Column Settings Fix ===\n";

try {
    // 1. Setup enhanced blocks
    echo "1. Setting up enhanced blocks...\n";
    include 'create_enhanced_blocks.php';
    
    // 2. Create test template with sections
    echo "\n2. Creating test template...\n";
    
    $testTemplate = App\Models\TemplateLayout::updateOrCreate(
        ['name' => 'Column Settings Test Template'],
        [
            'description' => 'Test template for column settings functionality',
            'company_id' => null,
            'version' => 'v2',
            'page_size' => 'legal',
            'orientation' => 'portrait',
            'font_family' => 'DejaVu Sans',
            'font_size' => 12,
            'is_active' => true,
            'is_default' => false,
            'created_by' => 1,
        ]
    );
    
    echo "   ✅ Test template: {$testTemplate->name} (ID: {$testTemplate->id})\n";
    
    // 3. Clear existing sections and create new ones
    echo "\n3. Creating test sections...\n";
    
    App\Models\LayoutSection::where('template_layout_id', $testTemplate->id)->delete();
    
    // Header section with 1 column (will test changing to 3)
    $headerSection = App\Models\LayoutSection::create([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'header',
        'section_name' => 'Header Section',
        'column_count' => 1,
        'column_layout' => 'equal',
        'order' => 0,
        'is_active' => true,
        'min_height' => 100,
    ]);
    
    // Body section with 2 columns (will test changing to 1)
    $bodySection = App\Models\LayoutSection::create([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'body',
        'section_name' => 'Body Section',
        'column_count' => 2,
        'column_layout' => 'equal',
        'order' => 0,
        'is_active' => true,
        'min_height' => 200,
    ]);
    
    echo "   ✅ Header section: {$headerSection->id} (1 column)\n";
    echo "   ✅ Body section: {$bodySection->id} (2 columns)\n";
    
    // 4. Add some blocks to test with
    echo "\n4. Adding test blocks...\n";
    
    $logoBlock = App\Models\BlockTemplate::where('block_type', 'logo')->first();
    $companyInfoBlock = App\Models\BlockTemplate::where('block_type', 'company_info_combined')->first();
    $freeTextBlock = App\Models\BlockTemplate::where('block_type', 'free_text')->first();
    
    if ($logoBlock) {
        $logoBlock->createContentBlock($headerSection->id, ['column_position' => 1]);
        echo "   ✅ Logo block added to header column 1\n";
    }
    
    if ($companyInfoBlock) {
        $companyInfoBlock->createContentBlock($bodySection->id, ['column_position' => 1]);
        echo "   ✅ Company info block added to body column 1\n";
    }
    
    if ($freeTextBlock) {
        $freeTextBlock->createContentBlock($bodySection->id, ['column_position' => 2]);
        echo "   ✅ Free text block added to body column 2\n";
    }
    
    // 5. Test column settings update
    echo "\n5. Testing column settings update...\n";
    
    // Test 1: Change header from 1 to 3 columns
    echo "   Test 1: Header 1 → 3 columns\n";
    $headerSection->update([
        'column_count' => 3,
        'column_layout' => 'equal'
    ]);
    $headerSection->refresh();
    echo "      ✅ Header now has {$headerSection->column_count} columns\n";
    
    // Test 2: Change body from 2 to 1 column
    echo "   Test 2: Body 2 → 1 column\n";
    $bodySection->update([
        'column_count' => 1,
        'column_layout' => 'equal'
    ]);
    $bodySection->refresh();
    echo "      ✅ Body now has {$bodySection->column_count} columns\n";
    
    // Test 3: Custom column widths
    echo "   Test 3: Custom column widths\n";
    $headerSection->update([
        'column_count' => 3,
        'column_layout' => 'custom',
        'column_widths' => [25, 50, 25] // Logo small, content large, info small
    ]);
    $headerSection->refresh();
    echo "      ✅ Header custom widths: " . json_encode($headerSection->column_widths) . "\n";
    
    // 6. Test Visual Builder page access
    echo "\n6. Testing Visual Builder access...\n";
    
    $builderUrl = "/admin/visual-template-builder?template={$testTemplate->id}";
    echo "   🎨 Visual Builder URL: {$builderUrl}\n";
    
    // 7. Test section settings data structure
    echo "\n7. Testing section settings data structure...\n";
    
    $sectionSettingsData = [
        'section_name' => $headerSection->section_name,
        'column_count' => $headerSection->column_count,
        'column_layout' => $headerSection->column_layout,
        'column_widths' => $headerSection->column_widths,
        'background_color' => $headerSection->background_color,
        'min_height' => $headerSection->min_height,
    ];
    
    echo "   📊 Section settings structure:\n";
    foreach ($sectionSettingsData as $key => $value) {
        $displayValue = is_array($value) ? json_encode($value) : ($value ?? 'null');
        echo "      - {$key}: {$displayValue}\n";
    }
    
    // 8. Test block types available
    echo "\n8. Available block types...\n";
    
    $blockTypes = App\Models\BlockTemplate::where('is_system', true)
        ->distinct('block_type')
        ->pluck('block_type')
        ->sort();
    
    echo "   📦 Block types available:\n";
    foreach ($blockTypes as $type) {
        $count = App\Models\BlockTemplate::where('block_type', $type)->count();
        echo "      - {$type} ({$count})\n";
    }
    
    // 9. Test multi-field blocks
    echo "\n9. Testing multi-field blocks...\n";
    
    $multiFieldBlocks = App\Models\BlockTemplate::where('is_system', true)
        ->where('block_type', 'like', '%_combined')
        ->get();
    
    echo "   🔗 Multi-field blocks:\n";
    foreach ($multiFieldBlocks as $block) {
        $fields = $block->template_data['fields'] ?? [];
        $fieldPaths = array_column($fields, 'path');
        echo "      - {$block->name}: " . implode(', ', $fieldPaths) . "\n";
    }
    
    // 10. Column settings testing instructions
    echo "\n=== Column Settings Testing Instructions ===\n";
    echo "1. Open Visual Builder: {$builderUrl}\n";
    echo "2. Click ⚙️ on Header Section\n";
    echo "3. Change columns from 3 to 2\n";
    echo "4. Click 'Save Section'\n";
    echo "5. Verify canvas shows 2 columns\n";
    echo "6. Try different column counts (1-4)\n";
    echo "7. Test custom column widths\n";
    echo "8. Test background color\n";
    echo "9. Test minimum height\n";
    
    echo "\n=== Block Testing Instructions ===\n";
    echo "1. Drag single field blocks (Logo, Company Name)\n";
    echo "2. Drag multi-field blocks (Company Info Combined)\n";
    echo "3. Drag Free Text Block and edit content\n";
    echo "4. Test block settings (font size, weight, color)\n";
    echo "5. Test different alignments\n";
    
    echo "\n=== Layout Examples to Test ===\n";
    echo "📋 Example 1: Logo + Company Info\n";
    echo "   - Header: 2 columns (25%, 75%)\n";
    echo "   - Column 1: Logo\n";
    echo "   - Column 2: Company Info Combined\n";
    
    echo "\n📋 Example 2: Detailed Header\n";
    echo "   - Header: 3 columns (20%, 60%, 20%)\n";
    echo "   - Column 1: Logo\n";
    echo "   - Column 2: Company Name + Address (separate blocks)\n";
    echo "   - Column 3: Invoice Details Combined\n";
    
    echo "\n📋 Example 3: Custom Layout\n";
    echo "   - Header: 4 columns (15%, 35%, 35%, 15%)\n";
    echo "   - Mix of single and combined blocks\n";
    echo "   - Free text blocks for custom content\n";
    
    // 11. Debug information
    echo "\n=== Debug Information ===\n";
    echo "🔍 If column settings don't work:\n";
    echo "1. Check browser console for JavaScript errors\n";
    echo "2. Check Laravel logs: tail -f storage/logs/laravel.log\n";
    echo "3. Verify Livewire is working: check network tab\n";
    echo "4. Test with simple column count change first\n";
    
    echo "\n🔍 Common issues:\n";
    echo "- Wire:model not binding: Check property names\n";
    echo "- Modal not saving: Check updateSectionSettings method\n";
    echo "- Canvas not updating: Check reloadTemplate method\n";
    echo "- Blocks not rendering: Check ContentBlock render methods\n";
    
    echo "\n✅ Column Settings Fix Test Complete!\n";
    echo "🚀 Ready for testing: {$builderUrl}\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
