<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_sections', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('template_id');
            $table->enum('section_type', [
                'header',      // Kop surat
                'billto',      // Company dan bill to
                'table',       // Table invoice details
                'inwords',     // Amount in words
                'footer',      // Footer dengan bank info
                'custom'       // Custom sections
            ]);
            $table->string('section_name');
            $table->json('section_config'); // JSON configuration untuk section
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('template_id')->references('id')->on('invoice_templates')->onDelete('cascade');

            // Indexes
            $table->index(['template_id', 'is_active']);
            $table->index(['template_id', 'sort_order']);
            $table->index('section_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_sections');
    }
};
