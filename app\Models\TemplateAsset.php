<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class TemplateAsset extends Model
{
    use HasFactory;

    protected $fillable = [
        'template_id',
        'asset_type',
        'file_path',
        'file_name',
        'original_name',
        'mime_type',
        'file_size',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'file_size' => 'integer',
    ];

    /**
     * Get the template that owns this asset
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(InvoiceTemplate::class, 'template_id');
    }

    /**
     * Scope for assets by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('asset_type', $type);
    }

    /**
     * Scope for image assets
     */
    public function scopeImages($query)
    {
        return $query->where('asset_type', 'image');
    }

    /**
     * Scope for font assets
     */
    public function scopeFonts($query)
    {
        return $query->where('asset_type', 'font');
    }

    /**
     * Scope for CSS assets
     */
    public function scopeCss($query)
    {
        return $query->where('asset_type', 'css');
    }

    /**
     * Scope for preview assets
     */
    public function scopePreviews($query)
    {
        return $query->where('asset_type', 'preview');
    }

    /**
     * Get the full URL for this asset
     */
    public function getUrlAttribute(): string
    {
        return Storage::url($this->file_path);
    }

    /**
     * Get the full path for this asset
     */
    public function getFullPathAttribute(): string
    {
        return Storage::path($this->file_path);
    }

    /**
     * Check if asset exists
     */
    public function exists(): bool
    {
        return Storage::exists($this->file_path);
    }

    /**
     * Get file size in human readable format
     */
    public function getHumanFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if asset is image
     */
    public function isImage(): bool
    {
        return $this->asset_type === 'image';
    }

    /**
     * Check if asset is font
     */
    public function isFont(): bool
    {
        return $this->asset_type === 'font';
    }

    /**
     * Check if asset is CSS
     */
    public function isCss(): bool
    {
        return $this->asset_type === 'css';
    }

    /**
     * Check if asset is preview
     */
    public function isPreview(): bool
    {
        return $this->asset_type === 'preview';
    }

    /**
     * Get metadata value
     */
    public function getMetadata($key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * Set metadata value
     */
    public function setMetadata($key, $value)
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
        return $this;
    }
}
