<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Preview - <?php echo e($template->name); ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @page {
            size: <?php echo e($pageSize === 'f4' ? 'A4' : 'legal'); ?>;
            margin: 1cm;
        }
        
        body {
            font-family: '<?php echo e($template->font_family); ?>', sans-serif;
            font-size: <?php echo e($template->font_size); ?>pt;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            color: #333;
        }
        
        .preview-container {
            max-width: <?php echo e($previewMode === 'desktop' ? '1200px' : ($previewMode === 'tablet' ? '768px' : '375px')); ?>;
            margin: 0 auto;
            background: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .preview-header {
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .preview-content {
            padding: 2rem;
            min-height: <?php echo e($pageSize === 'f4' ? '297mm' : '356mm'); ?>;
        }
        
        .layout-section {
            margin-bottom: 20px;
            page-break-inside: avoid;
        }
        
        .section-columns {
            display: flex;
            gap: 15px;
        }
        
        .section-column {
            flex: 1;
        }
        
        .content-block {
            margin-bottom: 10px;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .invoice-table th,
        .invoice-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .invoice-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .calculations {
            margin-top: 20px;
        }
        
        .calc-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .calc-line.total {
            border-top: 2px solid #333;
            padding-top: 8px;
            margin-top: 10px;
        }
        
        .signature-block {
            margin-top: 30px;
            text-align: center;
        }
        
        .logo-placeholder {
            background-color: #f0f0f0;
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            color: #666;
        }
        
        /* Print styles */
        @media print {
            .preview-header {
                display: none;
            }
            
            .preview-container {
                max-width: none;
                box-shadow: none;
            }
            
            .preview-content {
                padding: 0;
            }
            
            .layout-section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        
        <div class="preview-header">
            <div class="flex items-center space-x-4">
                <h1 class="text-lg font-semibold text-gray-900"><?php echo e($template->name); ?></h1>
                <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">v2.0 Preview</span>
                <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"><?php echo e(ucfirst($pageSize)); ?></span>
                <span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded"><?php echo e(ucfirst($previewMode)); ?></span>
            </div>
            
            <div class="flex items-center space-x-2">
                <button onclick="window.print()" class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                    Print Preview
                </button>
                <button onclick="window.close()" class="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200">
                    Close
                </button>
            </div>
        </div>

        
        <div class="preview-content">
            <?php if($template->sections->count() > 0): ?>
                
                <?php
                    $sectionsByType = $template->sections->sortBy('order')->groupBy('section_type');
                ?>
                
                <?php $__currentLoopData = ['header', 'body', 'footer']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(isset($sectionsByType[$sectionType])): ?>
                        <?php $__currentLoopData = $sectionsByType[$sectionType]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo $section->renderHtml($sampleData); ?>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Empty Template</h3>
                    <p class="text-gray-500 mb-4">This template doesn't have any sections yet.</p>
                    <p class="text-sm text-gray-400">Go back to the Visual Builder to add sections and content blocks.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    
    <?php if(config('app.debug')): ?>
        <div class="fixed bottom-4 right-4 bg-black bg-opacity-75 text-white p-3 rounded text-xs max-w-sm">
            <div class="font-semibold mb-2">Debug Info:</div>
            <div>Template ID: <?php echo e($template->id); ?></div>
            <div>Sections: <?php echo e($template->sections->count()); ?></div>
            <div>Total Blocks: <?php echo e($template->sections->sum(fn($s) => $s->blocks->count())); ?></div>
            <div>Page Size: <?php echo e($pageSize); ?></div>
            <div>Preview Mode: <?php echo e($previewMode); ?></div>
            <div>Font: <?php echo e($template->font_family); ?> <?php echo e($template->font_size); ?>pt</div>
        </div>
    <?php endif; ?>

    <script>
        // Auto-refresh preview when parent window updates (for development)
        if (window.opener) {
            window.addEventListener('message', function(event) {
                if (event.data === 'refresh-preview') {
                    window.location.reload();
                }
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            // Ctrl+P for print
            if (event.ctrlKey && event.key === 'p') {
                event.preventDefault();
                window.print();
            }
            
            // Escape to close
            if (event.key === 'Escape') {
                window.close();
            }
        });

        // Print event handling
        window.addEventListener('beforeprint', function() {
            document.title = 'Invoice - <?php echo e($template->name); ?>';
        });

        window.addEventListener('afterprint', function() {
            document.title = 'Template Preview - <?php echo e($template->name); ?>';
        });
    </script>
</body>
</html>
<?php /**PATH D:\sites\starterkit\web_starter\resources\views/visual-template/preview.blade.php ENDPATH**/ ?>