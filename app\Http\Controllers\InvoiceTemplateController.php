<?php

namespace App\Http\Controllers;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\View\View;

class InvoiceTemplateController extends Controller
{
    public function preview(Request $request, Company $company): View
    {
        // Load company with invoice template sections
        $company->load(['invoiceTemplateSections.cells']);

        // Get preview mode from request
        $mode = $request->get('mode', 'desktop');

        // Generate sample data from sample_data.json
        $sampleData = $this->generateSampleData($company);

        return view('invoice-template.preview', [
            'company' => $company,
            'sampleData' => $sampleData,
            'previewMode' => $mode,
        ]);
    }

    private function generateSampleData(Company $company): array
    {
        // Load sample data from file
        $sampleDataPath = base_path('sample_data.json');
        $sampleData = [];
        
        if (file_exists($sampleDataPath)) {
            $sampleData = json_decode(file_get_contents($sampleDataPath), true);
        }

        // Override company data with actual company data
        $sampleData['company'] = [
            'name' => $company->name,
            'address' => $company->address,
            'phone' => $company->phone,
            'email' => $company->email,
            'logo' => $company->logo ? url('storage/' . $company->logo) : null,
            'signature' => $company->signature ? url('storage/' . $company->signature) : null,
            'signature_name' => $company->signature_name,
        ];

        // Add fallback data if sample_data.json doesn't exist
        if (empty($sampleData)) {
            $sampleData = [
                'invoice_no' => 'INV-0001',
                'invoice_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+30 days')),
                'currency' => 'IDR',
                'invoice_details' => [
                    [
                        'description' => 'Service A',
                        'quantity' => 1,
                        'unit' => 'pcs',
                        'price' => 1000000,
                        'sub_total' => 1000000
                    ],
                    [
                        'description' => 'Service B',
                        'quantity' => 2,
                        'unit' => 'hrs',
                        'price' => 500000,
                        'sub_total' => 1000000
                    ]
                ],
                'invoice_rate' => 1,
                'booking_fee' => 100000,
                'invoice_amount' => 2100000,
                'amount_inword' => 'Dua Juta Seratus Ribu Rupiah',
                'company' => [
                    'name' => $company->name,
                    'address' => $company->address,
                    'phone' => $company->phone,
                    'email' => $company->email,
                    'logo' => $company->logo ? url('storage/' . $company->logo) : null,
                    'signature' => $company->signature ? url('storage/' . $company->signature) : null,
                    'signature_name' => $company->signature_name,
                ],
                'client' => [
                    'name' => 'PT. Client Example',
                    'address' => 'Jl. Client Address No. 123, Jakarta',
                    'phone' => '021-*********',
                    'email' => '<EMAIL>',
                ],
                'bank' => [
                    'account_name' => $company->name,
                    'bank_name' => 'BCA',
                    'account_number' => '**********',
                    'bank_address' => 'Jl. Bank Address No. 456, Jakarta',
                    'swift' => 'BCAIDJ123',
                    'routing_number' => '**********',
                    'custom_columns' => [
                        [
                            'key' => 'reference_number',
                            'type' => 'text',
                            'value' => 'BANK-REF-2024-001'
                        ]
                    ]
                ]
            ];
        }

        return $sampleData;
    }
}
