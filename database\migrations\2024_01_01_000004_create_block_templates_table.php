<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('block_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('block_type', [
                'logo', 'text', 'field', 'table', 'custom', 
                'company_info', 'client_info', 'invoice_details',
                'bank_info', 'signature', 'terms', 'calculations'
            ]);
            $table->string('category')->default('general'); // general, header, body, footer
            $table->json('template_data')->nullable(); // Reusable template configuration
            $table->json('default_styling')->nullable(); // Default styling
            $table->json('field_mappings')->nullable(); // Default field mappings
            $table->string('preview_image')->nullable(); // Preview thumbnail
            $table->text('preview_html')->nullable(); // HTML preview
            $table->boolean('is_system')->default(false); // System vs user templates
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0); // Track popularity
            $table->json('tags')->nullable(); // Searchable tags
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            // Indexes
            $table->index(['block_type', 'is_active']);
            $table->index(['category', 'is_active']);
            $table->index('is_system');
            $table->index('usage_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('block_templates');
    }
};
