<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InvoiceTemplateSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'section_type',
        'section_name',
        'rows',
        'columns',
        'table_structure',
        'section_settings',
        'order',
        'is_active',
    ];

    protected $casts = [
        'table_structure' => 'array',
        'section_settings' => 'array',
        'is_active' => 'boolean',
        'rows' => 'integer',
        'columns' => 'integer',
        'order' => 'integer',
    ];

    protected $attributes = [
        'rows' => 1,
        'columns' => 1,
        'is_active' => true,
        'order' => 0,
    ];

    // Relationships
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function cells(): HasMany
    {
        return $this->hasMany(InvoiceTemplateCell::class, 'section_id')->orderBy('row_index')->orderBy('col_index');
    }

    // Helper methods
    public function getCellAt(int $row, int $col): ?InvoiceTemplateCell
    {
        return $this->cells()
            ->where('row_index', $row)
            ->where('col_index', $col)
            ->first();
    }

    public function createDefaultCell(int $row = 0, int $col = 0): InvoiceTemplateCell
    {
        return $this->cells()->create([
            'row_index' => $row,
            'col_index' => $col,
            'rowspan' => 1,
            'colspan' => 1,
            'content_type' => 'text',
            'static_content' => 'Click to edit',
        ]);
    }

    public function addRow(): void
    {
        $this->increment('rows');
        
        // Create cells for the new row
        for ($col = 0; $col < $this->columns; $col++) {
            $this->createDefaultCell($this->rows - 1, $col);
        }
        
        $this->updateTableStructure();
    }

    public function addColumn(): void
    {
        $this->increment('columns');
        
        // Create cells for the new column in all existing rows
        for ($row = 0; $row < $this->rows; $row++) {
            $this->createDefaultCell($row, $this->columns - 1);
        }
        
        $this->updateTableStructure();
    }

    public function mergeCells(int $startRow, int $startCol, int $endRow, int $endCol): void
    {
        $rowspan = $endRow - $startRow + 1;
        $colspan = $endCol - $startCol + 1;
        
        // Get the main cell (top-left)
        $mainCell = $this->getCellAt($startRow, $startCol);
        
        if ($mainCell) {
            $mainCell->update([
                'rowspan' => $rowspan,
                'colspan' => $colspan,
            ]);
            
            // Remove other cells in the merged area
            for ($row = $startRow; $row <= $endRow; $row++) {
                for ($col = $startCol; $col <= $endCol; $col++) {
                    if ($row === $startRow && $col === $startCol) {
                        continue; // Skip main cell
                    }
                    
                    $cell = $this->getCellAt($row, $col);
                    if ($cell) {
                        $cell->delete();
                    }
                }
            }
        }
        
        $this->updateTableStructure();
    }

    public function splitCell(int $row, int $col): void
    {
        $cell = $this->getCellAt($row, $col);
        
        if ($cell && ($cell->rowspan > 1 || $cell->colspan > 1)) {
            $originalRowspan = $cell->rowspan;
            $originalColspan = $cell->colspan;
            
            // Reset main cell to 1x1
            $cell->update([
                'rowspan' => 1,
                'colspan' => 1,
            ]);
            
            // Create new cells for the split area
            for ($r = $row; $r < $row + $originalRowspan; $r++) {
                for ($c = $col; $c < $col + $originalColspan; $c++) {
                    if ($r === $row && $c === $col) {
                        continue; // Skip main cell
                    }
                    
                    $this->createDefaultCell($r, $c);
                }
            }
        }
        
        $this->updateTableStructure();
    }

    public function updateTableStructure(): void
    {
        $structure = [];
        
        for ($row = 0; $row < $this->rows; $row++) {
            $structure[$row] = [];
            for ($col = 0; $col < $this->columns; $col++) {
                $cell = $this->getCellAt($row, $col);
                $structure[$row][$col] = $cell ? [
                    'id' => $cell->id,
                    'rowspan' => $cell->rowspan,
                    'colspan' => $cell->colspan,
                    'content_type' => $cell->content_type,
                    'field_path' => $cell->field_path,
                    'static_content' => $cell->static_content,
                ] : null;
            }
        }
        
        $this->update(['table_structure' => $structure]);
    }

    public function renderHtml(array $data = []): string
    {
        $html = '<table class="invoice-table section-' . $this->section_type . '"';
        
        // Add section styling
        if ($this->section_settings) {
            $styles = [];
            foreach ($this->section_settings as $property => $value) {
                $styles[] = $property . ': ' . $value;
            }
            if (!empty($styles)) {
                $html .= ' style="' . implode('; ', $styles) . '"';
            }
        }
        
        $html .= '>';
        
        // Render table structure
        $renderedCells = [];
        
        for ($row = 0; $row < $this->rows; $row++) {
            $html .= '<tr>';
            
            for ($col = 0; $col < $this->columns; $col++) {
                $cellKey = $row . '-' . $col;
                
                // Skip if this cell is already rendered as part of a span
                if (isset($renderedCells[$cellKey])) {
                    continue;
                }
                
                $cell = $this->getCellAt($row, $col);
                
                if ($cell) {
                    $html .= $cell->renderHtml($data);
                    
                    // Mark spanned cells as rendered
                    for ($r = $row; $r < $row + $cell->rowspan; $r++) {
                        for ($c = $col; $c < $col + $cell->colspan; $c++) {
                            $renderedCells[$r . '-' . $c] = true;
                        }
                    }
                } else {
                    // Empty cell
                    $html .= '<td></td>';
                    $renderedCells[$cellKey] = true;
                }
            }
            
            $html .= '</tr>';
        }
        
        $html .= '</table>';
        
        return $html;
    }

    public function duplicate(): self
    {
        $newSection = $this->replicate();
        $newSection->save();
        
        // Duplicate all cells
        foreach ($this->cells as $cell) {
            $newCell = $cell->replicate();
            $newCell->section_id = $newSection->id;
            $newCell->save();
        }
        
        $newSection->updateTableStructure();
        
        return $newSection;
    }
}
