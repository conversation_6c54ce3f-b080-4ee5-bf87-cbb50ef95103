<?php

namespace Tests\Unit\Models;

use App\Models\Company;
use App\Models\InvoiceTemplate;
use App\Models\TemplateAsset;
use App\Models\TemplateSection;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InvoiceTemplateTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_belongs_to_a_creator()
    {
        $user = User::factory()->create();
        $template = InvoiceTemplate::factory()->create(['created_by' => $user->id]);

        $this->assertInstanceOf(User::class, $template->creator);
        $this->assertEquals($user->id, $template->creator->id);
    }

    /** @test */
    public function it_can_belong_to_a_company()
    {
        $company = Company::factory()->create();
        $template = InvoiceTemplate::factory()->create(['company_id' => $company->id]);

        $this->assertInstanceOf(Company::class, $template->company);
        $this->assertEquals($company->id, $template->company->id);
    }

    /** @test */
    public function it_can_be_global_template()
    {
        $template = InvoiceTemplate::factory()->create(['company_id' => null]);

        $this->assertNull($template->company);
        $this->assertTrue($template->isGlobal());
    }

    /** @test */
    public function it_has_many_sections()
    {
        $template = InvoiceTemplate::factory()->create();
        $sections = TemplateSection::factory()->count(3)->create(['template_id' => $template->id]);

        $this->assertCount(3, $template->sections);
        $this->assertInstanceOf(TemplateSection::class, $template->sections->first());
    }

    /** @test */
    public function it_has_many_assets()
    {
        $template = InvoiceTemplate::factory()->create();
        $assets = TemplateAsset::factory()->count(2)->create(['template_id' => $template->id]);

        $this->assertCount(2, $template->assets);
        $this->assertInstanceOf(TemplateAsset::class, $template->assets->first());
    }

    /** @test */
    public function it_can_get_active_sections_only()
    {
        $template = InvoiceTemplate::factory()->create();
        
        // Create active sections
        TemplateSection::factory()->count(2)->create([
            'template_id' => $template->id,
            'is_active' => true,
            'sort_order' => 1
        ]);
        
        // Create inactive section
        TemplateSection::factory()->create([
            'template_id' => $template->id,
            'is_active' => false,
            'sort_order' => 2
        ]);

        $activeSections = $template->activeSections;
        
        $this->assertCount(2, $activeSections);
        $activeSections->each(function ($section) {
            $this->assertTrue($section->is_active);
        });
    }

    /** @test */
    public function it_can_scope_active_templates()
    {
        InvoiceTemplate::factory()->count(3)->create(['is_active' => true]);
        InvoiceTemplate::factory()->count(2)->create(['is_active' => false]);

        $activeTemplates = InvoiceTemplate::active()->get();

        $this->assertCount(3, $activeTemplates);
        $activeTemplates->each(function ($template) {
            $this->assertTrue($template->is_active);
        });
    }

    /** @test */
    public function it_can_scope_custom_templates()
    {
        InvoiceTemplate::factory()->count(2)->create(['type' => 'custom']);
        InvoiceTemplate::factory()->count(3)->create(['type' => 'legacy']);

        $customTemplates = InvoiceTemplate::custom()->get();

        $this->assertCount(2, $customTemplates);
        $customTemplates->each(function ($template) {
            $this->assertEquals('custom', $template->type);
        });
    }

    /** @test */
    public function it_can_scope_legacy_templates()
    {
        InvoiceTemplate::factory()->count(2)->create(['type' => 'custom']);
        InvoiceTemplate::factory()->count(3)->create(['type' => 'legacy']);

        $legacyTemplates = InvoiceTemplate::legacy()->get();

        $this->assertCount(3, $legacyTemplates);
        $legacyTemplates->each(function ($template) {
            $this->assertEquals('legacy', $template->type);
        });
    }

    /** @test */
    public function it_can_scope_global_templates()
    {
        InvoiceTemplate::factory()->count(2)->create(['company_id' => null]);
        InvoiceTemplate::factory()->count(3)->create(['company_id' => Company::factory()]);

        $globalTemplates = InvoiceTemplate::global()->get();

        $this->assertCount(2, $globalTemplates);
        $globalTemplates->each(function ($template) {
            $this->assertNull($template->company_id);
        });
    }

    /** @test */
    public function it_can_scope_templates_for_company()
    {
        $company = Company::factory()->create();
        
        InvoiceTemplate::factory()->count(2)->create(['company_id' => $company->id]);
        InvoiceTemplate::factory()->count(3)->create(['company_id' => Company::factory()]);

        $companyTemplates = InvoiceTemplate::forCompany($company->id)->get();

        $this->assertCount(2, $companyTemplates);
        $companyTemplates->each(function ($template) use ($company) {
            $this->assertEquals($company->id, $template->company_id);
        });
    }

    /** @test */
    public function it_can_check_if_template_is_global()
    {
        $globalTemplate = InvoiceTemplate::factory()->create(['company_id' => null]);
        $companyTemplate = InvoiceTemplate::factory()->create(['company_id' => Company::factory()]);

        $this->assertTrue($globalTemplate->isGlobal());
        $this->assertFalse($companyTemplate->isGlobal());
    }

    /** @test */
    public function it_can_check_template_type()
    {
        $customTemplate = InvoiceTemplate::factory()->create(['type' => 'custom']);
        $legacyTemplate = InvoiceTemplate::factory()->create(['type' => 'legacy']);

        $this->assertTrue($customTemplate->isCustom());
        $this->assertFalse($customTemplate->isLegacy());

        $this->assertTrue($legacyTemplate->isLegacy());
        $this->assertFalse($legacyTemplate->isCustom());
    }

    /** @test */
    public function it_casts_template_data_to_array()
    {
        $templateData = [
            'page_size' => 'legal',
            'orientation' => 'portrait',
            'margins' => ['top' => 15, 'right' => 15, 'bottom' => 15, 'left' => 15]
        ];

        $template = InvoiceTemplate::factory()->create(['template_data' => $templateData]);

        $this->assertIsArray($template->template_data);
        $this->assertEquals($templateData, $template->template_data);
    }

    /** @test */
    public function it_casts_is_active_to_boolean()
    {
        $activeTemplate = InvoiceTemplate::factory()->create(['is_active' => 1]);
        $inactiveTemplate = InvoiceTemplate::factory()->create(['is_active' => 0]);

        $this->assertIsBool($activeTemplate->is_active);
        $this->assertIsBool($inactiveTemplate->is_active);
        $this->assertTrue($activeTemplate->is_active);
        $this->assertFalse($inactiveTemplate->is_active);
    }
}
