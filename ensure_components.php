<?php

/**
 * Script to ensure template components exist for testing
 * Run via: php artisan tinker
 */

echo "=== Ensuring Template Components Exist ===\n";

// Run this immediately
try {

// Check if components exist
$componentCount = App\Models\TemplateComponent::count();
echo "Current component count: {$componentCount}\n";

if ($componentCount < 5) {
    echo "Creating test components...\n";

    // Create test components if they don't exist
    $testComponents = [
        [
            'name' => 'Company Header',
            'type' => 'kop',
            'description' => 'Company logo and information',
            'preview_html' => '<div class="text-center"><h2>{{company.name}}</h2><p>{{company.address}}</p></div>',
            'template_html' => '<div class="kop-section text-{{config.alignment}}"><h2>{{company.name}}</h2><p>{{company.address}}</p></div>',
            'config_schema' => json_encode([
                'alignment' => ['type' => 'select', 'options' => ['left', 'center', 'right'], 'default' => 'center'],
                'font_size' => ['type' => 'select', 'options' => [12, 14, 16, 18], 'default' => 14]
            ]),
            'default_config' => json_encode(['alignment' => 'center', 'font_size' => 14])
        ],
        [
            'name' => 'Bill To Information',
            'type' => 'billto',
            'description' => 'Client billing information',
            'preview_html' => '<div><strong>Bill To:</strong><br>{{client.name}}<br>{{client.address}}</div>',
            'template_html' => '<div class="billto-section"><strong>Bill To:</strong><br>{{client.name}}<br>{{client.address}}</div>',
            'config_schema' => json_encode([
                'show_title' => ['type' => 'checkbox', 'default' => true],
                'title_text' => ['type' => 'text', 'default' => 'Bill To:']
            ]),
            'default_config' => json_encode(['show_title' => true, 'title_text' => 'Bill To:'])
        ],
        [
            'name' => 'Invoice Items Table',
            'type' => 'table',
            'description' => 'Table of invoice items',
            'preview_html' => '<table border="1"><tr><th>Description</th><th>Qty</th><th>Price</th><th>Total</th></tr><tr><td>Sample Item</td><td>1</td><td>$100</td><td>$100</td></tr></table>',
            'template_html' => '<table class="invoice-table"><thead><tr><th>Description</th><th>Quantity</th><th>Price</th><th>Total</th></tr></thead><tbody>{{#each items}}<tr><td>{{description}}</td><td>{{quantity}}</td><td>{{price}}</td><td>{{total}}</td></tr>{{/each}}</tbody></table>',
            'config_schema' => json_encode([
                'show_borders' => ['type' => 'checkbox', 'default' => true],
                'header_style' => ['type' => 'select', 'options' => ['normal', 'bold', 'colored'], 'default' => 'bold']
            ]),
            'default_config' => json_encode(['show_borders' => true, 'header_style' => 'bold'])
        ],
        [
            'name' => 'Amount in Words',
            'type' => 'inwords',
            'description' => 'Total amount spelled out in words',
            'preview_html' => '<div><em>Amount in words: One Thousand Dollars</em></div>',
            'template_html' => '<div class="amount-words"><em>{{totals.total_words}}</em></div>',
            'config_schema' => json_encode([
                'prefix' => ['type' => 'text', 'default' => 'Amount in words: '],
                'style' => ['type' => 'select', 'options' => ['italic', 'normal', 'bold'], 'default' => 'italic']
            ]),
            'default_config' => json_encode(['prefix' => 'Amount in words: ', 'style' => 'italic'])
        ],
        [
            'name' => 'Bank Information',
            'type' => 'bankinfo',
            'description' => 'Payment and bank details',
            'preview_html' => '<div><strong>Bank Details:</strong><br>Bank: Sample Bank<br>Account: **********</div>',
            'template_html' => '<div class="bank-info"><strong>Bank Details:</strong><br>Bank: {{bank.name}}<br>Account: {{bank.account_number}}</div>',
            'config_schema' => json_encode([
                'layout' => ['type' => 'select', 'options' => ['simple', 'boxed', 'table'], 'default' => 'simple'],
                'show_custom_fields' => ['type' => 'checkbox', 'default' => true]
            ]),
            'default_config' => json_encode(['layout' => 'simple', 'show_custom_fields' => true])
        ]
    ];

    foreach ($testComponents as $componentData) {
        $existing = App\Models\TemplateComponent::where('name', $componentData['name'])->first();
        if (!$existing) {
            App\Models\TemplateComponent::create($componentData);
            echo "   ✅ Created: {$componentData['name']}\n";
        } else {
            echo "   ⚠️  Already exists: {$componentData['name']}\n";
        }
    }
}

// Final count
$finalCount = App\Models\TemplateComponent::count();
echo "\nFinal component count: {$finalCount}\n";

// List all components
echo "\nAvailable components:\n";
$components = App\Models\TemplateComponent::all();
foreach ($components as $component) {
    echo "   - {$component->name} ({$component->type})\n";
}

echo "\n=== Component Setup Complete ===\n";
echo "You can now test drag & drop in the Template Builder!\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
