<?php

namespace App\Filament\Admin\Resources\FontResource\Pages;

use App\Filament\Admin\Resources\FontResource;
use Filament\Resources\Pages\CreateRecord;

class CreateFont extends CreateRecord
{
    protected static string $resource = FontResource::class;

    public function getTitle(): string
    {
        return 'Add New Font';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Font added successfully';
    }
}
