<?php

namespace App\Filament\Admin\Resources\TemplateLayoutResource\Pages;

use App\Filament\Admin\Resources\TemplateLayoutResource;
use App\Models\TemplateLayout;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditTemplateLayout extends EditRecord
{
    protected static string $resource = TemplateLayoutResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('visual_builder')
                ->label('Open Visual Builder')
                ->icon('heroicon-o-paint-brush')
                ->color('primary')
                ->url(route('filament.admin.pages.visual-template-builder', [
                    'template' => $this->record->id
                ])),

            Actions\Action::make('preview')
                ->label('Preview')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->url(route('visual-template.preview', [
                    'template' => $this->record->id
                ]))
                ->openUrlInNewTab(),

            Actions\Action::make('duplicate')
                ->label('Duplicate')
                ->icon('heroicon-o-document-duplicate')
                ->color('gray')
                ->action(function () {
                    $newTemplate = $this->record->duplicate();
                    
                    Notification::make()
                        ->title('Template Duplicated')
                        ->body("Template '{$this->record->name}' has been duplicated as '{$newTemplate->name}'.")
                        ->success()
                        ->send();

                    return redirect()->route('filament.admin.resources.template-layouts.edit', $newTemplate);
                }),

            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['updated_by'] = auth()->id();
        
        return $data;
    }

    protected function afterSave(): void
    {
        // Handle default template setting
        if ($this->record->is_default && $this->record->company_id) {
            // Remove default from other templates for this company
            TemplateLayout::where('company_id', $this->record->company_id)
                ->where('id', '!=', $this->record->id)
                ->update(['is_default' => false]);
        }
    }

    public function getTitle(): string
    {
        return 'Edit Template: ' . $this->record->name;
    }
}
