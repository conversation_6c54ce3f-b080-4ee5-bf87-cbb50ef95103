<?php

/**
 * Fix Database Schema and Create Individual Components
 * Run via: php artisan tinker
 */

echo "=== Fix Database Schema and Create Individual Components ===\n";

try {
    // 1. Run migration to fix block_type columns
    echo "1. Running migration to fix block_type columns...\n";
    
    try {
        Artisan::call('migrate', ['--path' => 'database/migrations/2025_05_27_162000_fix_block_type_columns.php']);
        echo "   ✅ Migration completed successfully\n";
    } catch (\Exception $e) {
        echo "   ⚠️  Migration warning: " . $e->getMessage() . "\n";
        echo "   🔧 Attempting manual schema fix...\n";
        
        // Manual schema fix
        try {
            DB::statement("ALTER TABLE block_templates MODIFY COLUMN block_type VARCHAR(100)");
            DB::statement("ALTER TABLE content_blocks MODIFY COLUMN block_type VARCHAR(100)");
            echo "   ✅ Manual schema fix completed\n";
        } catch (\Exception $e2) {
            echo "   ❌ Manual fix failed: " . $e2->getMessage() . "\n";
            echo "   🔧 Trying alternative approach...\n";
            
            // Alternative: Drop and recreate columns
            try {
                Schema::table('block_templates', function (Blueprint $table) {
                    $table->dropColumn('block_type');
                });
                Schema::table('block_templates', function (Blueprint $table) {
                    $table->string('block_type', 100)->after('description');
                });
                
                Schema::table('content_blocks', function (Blueprint $table) {
                    $table->dropColumn('block_type');
                });
                Schema::table('content_blocks', function (Blueprint $table) {
                    $table->string('block_type', 100)->after('layout_section_id');
                });
                
                echo "   ✅ Alternative schema fix completed\n";
            } catch (\Exception $e3) {
                echo "   ❌ All schema fixes failed. Please run migration manually:\n";
                echo "       php artisan migrate --path=database/migrations/2025_05_27_162000_fix_block_type_columns.php\n";
                return;
            }
        }
    }
    
    // 2. Verify schema fix
    echo "\n2. Verifying schema fix...\n";
    
    try {
        $blockTemplatesColumns = DB::select("DESCRIBE block_templates");
        $contentBlocksColumns = DB::select("DESCRIBE content_blocks");
        
        $blockTypeColumn = collect($blockTemplatesColumns)->firstWhere('Field', 'block_type');
        $contentBlockTypeColumn = collect($contentBlocksColumns)->firstWhere('Field', 'block_type');
        
        echo "   📊 block_templates.block_type: {$blockTypeColumn->Type}\n";
        echo "   📊 content_blocks.block_type: {$contentBlockTypeColumn->Type}\n";
        
        if (strpos($blockTypeColumn->Type, 'varchar') !== false && strpos($contentBlockTypeColumn->Type, 'varchar') !== false) {
            echo "   ✅ Schema fix verified - both columns are now varchar\n";
        } else {
            echo "   ⚠️  Schema may not be fully fixed\n";
        }
    } catch (\Exception $e) {
        echo "   ⚠️  Could not verify schema: " . $e->getMessage() . "\n";
    }
    
    // 3. Clear existing system blocks
    echo "\n3. Clearing existing system blocks...\n";
    
    App\Models\BlockTemplate::where('is_system', true)->delete();
    echo "   ✅ Existing system blocks cleared\n";
    
    // 4. Create individual field components with shorter block_type names
    echo "\n4. Creating individual field components...\n";
    
    $individualComponents = [
        // COMPANY FIELDS - INDIVIDUAL
        [
            'name' => 'Company Logo',
            'description' => 'Company logo image only',
            'block_type' => 'comp_logo',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.logo',
                'alt_text' => 'Company Logo',
                'width' => '100px',
                'height' => 'auto',
                'storage_prefix' => 'storage/'
            ],
            'field_mappings' => [
                'field' => 'company.logo',
                'format' => 'image'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'margin' => ['top' => 0, 'right' => 10, 'bottom' => 0, 'left' => 0]
            ],
            'preview_html' => '<div class="w-16 h-12 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500 rounded">[LOGO]</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'logo', 'image'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Name',
            'description' => 'Company name text only',
            'block_type' => 'comp_name',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.name',
                'heading_size_path' => 'company.heading_size',
                'text_color_path' => 'company.text_color',
                'default_heading' => 'h3',
                'default_color' => '#000000'
            ],
            'field_mappings' => [
                'field' => 'company.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 18,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="text-lg font-bold text-gray-800">PT. Sample Company</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'name', 'text'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Address',
            'description' => 'Company address text only',
            'block_type' => 'comp_address',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.address',
                'strip_tags' => true
            ],
            'field_mappings' => [
                'field' => 'company.address',
                'format' => 'html'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Sample Street No. 123<br>Jakarta Pusat 10220</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'address', 'text'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Phone',
            'description' => 'Company phone number only',
            'block_type' => 'comp_phone',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.phone',
                'prefix' => 'Phone: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.phone',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Phone: +62 21 1234 5678</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'phone', 'contact'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Email',
            'description' => 'Company email address only',
            'block_type' => 'comp_email',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.email',
                'prefix' => 'Email: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.email',
                'format' => 'email'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Email: <span class="text-blue-600"><EMAIL></span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'email', 'contact'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Website',
            'description' => 'Company website URL only',
            'block_type' => 'comp_website',
            'category' => 'company',
            'template_data' => [
                'field_path' => 'company.website',
                'prefix' => 'Website: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.website',
                'format' => 'url'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Website: <span class="text-blue-600">www.company.com</span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'website', 'contact'],
            'usage_count' => 0
        ],
        
        // INVOICE FIELDS - INDIVIDUAL
        [
            'name' => 'Invoice Number',
            'description' => 'Invoice number only',
            'block_type' => 'inv_number',
            'category' => 'invoice',
            'template_data' => [
                'field_path' => 'invoice.invoice_no',
                'prefix' => 'Invoice No: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_no',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 14,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="text-right font-bold">Invoice No: INV-2024-001</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'number'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Date',
            'description' => 'Invoice date only',
            'block_type' => 'inv_date',
            'category' => 'invoice',
            'template_data' => [
                'field_path' => 'invoice.invoice_date',
                'prefix' => 'Invoice Date: ',
                'suffix' => '',
                'date_format' => 'd/m/Y'
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_date',
                'format' => 'date'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-right text-sm text-gray-600">Invoice Date: 15/01/2024</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'date'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Due Date',
            'description' => 'Invoice due date only',
            'block_type' => 'inv_due',
            'category' => 'invoice',
            'template_data' => [
                'field_path' => 'invoice.invoice_due',
                'prefix' => 'Due Date: ',
                'suffix' => '',
                'date_format' => 'd/m/Y'
            ],
            'field_mappings' => [
                'field' => 'invoice.invoice_due',
                'format' => 'date'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-right text-sm text-gray-600">Due Date: 15/02/2024</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'due_date'],
            'usage_count' => 0
        ],
        
        // CUSTOMER FIELDS - INDIVIDUAL
        [
            'name' => 'Customer Name',
            'description' => 'Customer/client name only',
            'block_type' => 'cust_name',
            'category' => 'customer',
            'template_data' => [
                'field_path' => 'customer.name',
                'prefix' => 'Bill to: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'customer.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 14,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="font-bold">Bill to: Sample Client Corp</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'name', 'billto'],
            'usage_count' => 0
        ],
        [
            'name' => 'Customer Address',
            'description' => 'Customer/client address only',
            'block_type' => 'cust_address',
            'category' => 'customer',
            'template_data' => [
                'field_path' => 'customer.address',
                'allow_html' => true
            ],
            'field_mappings' => [
                'field' => 'customer.address',
                'format' => 'html'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Client Avenue No. 456<br>Surabaya 60123</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'address', 'billto'],
            'usage_count' => 0
        ],
        
        // UTILITY BLOCKS
        [
            'name' => 'Free Text Block',
            'description' => 'Fully customizable text content',
            'block_type' => 'free_text',
            'category' => 'utility',
            'template_data' => [
                'text' => 'Enter your custom text here',
                'allow_html' => true,
                'editable' => true
            ],
            'field_mappings' => [],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div class="border-2 border-dashed border-blue-300 bg-blue-50 p-3 text-blue-700 text-sm rounded"><span class="font-medium">✏️ Free Text Block</span><br><span class="text-xs">Click to edit custom content</span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['text', 'custom', 'editable'],
            'usage_count' => 0
        ],
        [
            'name' => 'Spacer',
            'description' => 'Empty space for layout spacing',
            'block_type' => 'spacer',
            'category' => 'utility',
            'template_data' => [
                'height' => '20px',
                'adjustable' => true
            ],
            'field_mappings' => [],
            'default_styling' => [
                'min_height' => 20
            ],
            'preview_html' => '<div class="h-5 border-b border-dashed border-gray-300 flex items-center justify-center text-xs text-gray-400 bg-gray-50 rounded">--- spacer (20px) ---</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['spacer', 'layout'],
            'usage_count' => 0
        ]
    ];
    
    $createdComponents = [];
    foreach ($individualComponents as $componentData) {
        try {
            $component = App\Models\BlockTemplate::create($componentData);
            $createdComponents[] = $component;
            echo "   ✅ Created: {$component->name} ({$component->block_type})\n";
        } catch (\Exception $e) {
            echo "   ❌ Failed to create {$componentData['name']}: " . $e->getMessage() . "\n";
        }
    }
    
    // 5. Update ContentBlock model to handle new block types
    echo "\n5. Updating ContentBlock model...\n";
    
    // Add new block types to ContentBlock switch statement
    echo "   📝 Note: ContentBlock model needs to be updated to handle new block types:\n";
    echo "      - comp_logo, comp_name, comp_address, comp_phone, comp_email, comp_website\n";
    echo "      - inv_number, inv_date, inv_due\n";
    echo "      - cust_name, cust_address\n";
    echo "      - free_text, spacer\n";
    
    // 6. Create test template
    echo "\n6. Creating test template...\n";
    
    $testTemplate = App\Models\TemplateLayout::updateOrCreate(
        ['name' => 'Individual Components Test'],
        [
            'description' => 'Template showcasing individual field components',
            'company_id' => null,
            'version' => 'v2',
            'page_size' => 'legal',
            'orientation' => 'portrait',
            'font_family' => 'DejaVu Sans',
            'font_size' => 12,
            'is_active' => true,
            'is_default' => false,
            'created_by' => 1,
        ]
    );
    
    // Clear existing sections
    App\Models\LayoutSection::where('template_layout_id', $testTemplate->id)->delete();
    
    // Create header section
    $headerSection = App\Models\LayoutSection::create([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'header',
        'section_name' => 'Individual Components Header',
        'column_count' => 3,
        'column_layout' => 'equal',
        'column_widths' => null,
        'order' => 0,
        'is_active' => true,
        'min_height' => 120,
    ]);
    
    echo "   ✅ Test template created: {$testTemplate->name} (ID: {$testTemplate->id})\n";
    echo "   ✅ Header section created: {$headerSection->id}\n";
    
    // 7. Add some test blocks
    echo "\n7. Adding test blocks...\n";
    
    $logoBlock = App\Models\BlockTemplate::where('block_type', 'comp_logo')->first();
    $nameBlock = App\Models\BlockTemplate::where('block_type', 'comp_name')->first();
    $invoiceBlock = App\Models\BlockTemplate::where('block_type', 'inv_number')->first();
    
    if ($logoBlock) {
        $logoBlock->createContentBlock($headerSection->id, ['column_position' => 1]);
        echo "   ✅ Logo block added to column 1\n";
    }
    
    if ($nameBlock) {
        $nameBlock->createContentBlock($headerSection->id, ['column_position' => 2]);
        echo "   ✅ Name block added to column 2\n";
    }
    
    if ($invoiceBlock) {
        $invoiceBlock->createContentBlock($headerSection->id, ['column_position' => 3]);
        echo "   ✅ Invoice block added to column 3\n";
    }
    
    // 8. Summary
    echo "\n=== Database Fix and Component Creation Complete ===\n";
    
    $totalComponents = count($createdComponents);
    $categories = collect($createdComponents)->groupBy('category');
    
    echo "✅ Database schema fixed - block_type columns now support longer names\n";
    echo "✅ Created {$totalComponents} individual field components\n";
    echo "📦 Categories:\n";
    foreach ($categories as $category => $components) {
        $count = $components->count();
        echo "   - {$category}: {$count} components\n";
    }
    
    $builderUrl = "/admin/visual-template-builder?template={$testTemplate->id}";
    echo "\n🚀 Test Visual Builder: {$builderUrl}\n";
    
    echo "\n📋 Individual Components Available:\n";
    echo "✅ Company: Logo, Name, Address, Phone, Email, Website\n";
    echo "✅ Invoice: Number, Date, Due Date\n";
    echo "✅ Customer: Name, Address\n";
    echo "✅ Utility: Free Text, Spacer\n";
    
    echo "\n🎯 Next Steps:\n";
    echo "1. Test Visual Builder with individual components\n";
    echo "2. Verify auto-save functionality\n";
    echo "3. Test drag & drop of individual fields\n";
    echo "4. Create custom layouts with mixed components\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
