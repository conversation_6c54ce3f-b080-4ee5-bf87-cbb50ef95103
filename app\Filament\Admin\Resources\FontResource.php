<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\FontResource\Pages;
use App\Models\Font;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class FontResource extends Resource
{
    protected static ?string $model = Font::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Template Management';

    protected static ?string $navigationLabel = 'Fonts';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Font Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Font family name (e.g., <PERSON>l, Roboto, DejaVu Sans)'),

                        Forms\Components\TextInput::make('source')
                            ->label('Font CSS URL')
                            ->url()
                            ->nullable()
                            ->placeholder('https://fonts.googleapis.com/css2?family=...')
                            ->helperText('CSS URL for web fonts (leave empty for system fonts)'),

                        Forms\Components\Select::make('type')
                            ->options([
                                'sans-serif' => 'Sans Serif',
                                'serif' => 'Serif',
                                'monospace' => 'Monospace',
                            ])
                            ->required()
                            ->helperText('Font category for proper fallbacks'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('source_type')
                    ->label('Source')
                    ->badge()
                    ->getStateUsing(fn ($record) => $record->getSourceType())
                    ->color(fn (string $state): string => match ($state) {
                        'Google Fonts' => 'warning',
                        'System Font' => 'success',
                        'Custom Font' => 'primary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('source')
                    ->label('CSS URL')
                    ->limit(50)
                    ->tooltip(fn ($record) => $record->source)
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('google_fonts')
                    ->label('Google Fonts')
                    ->query(fn ($query) => $query->where('source', 'like', '%fonts.googleapis.com%')),

                Tables\Filters\Filter::make('system_fonts')
                    ->label('System Fonts')
                    ->query(fn ($query) => $query->whereNull('source')),

                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'sans-serif' => 'Sans Serif',
                        'serif' => 'Serif',
                        'monospace' => 'Monospace',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('source')
            ->groups([
                Tables\Grouping\Group::make('source')
                    ->label('Font Source')
                    ->collapsible(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFonts::route('/'),
            'create' => Pages\CreateFont::route('/create'),
            'view' => Pages\ViewFont::route('/{record}'),
            'edit' => Pages\EditFont::route('/{record}/edit'),
        ];
    }
}
