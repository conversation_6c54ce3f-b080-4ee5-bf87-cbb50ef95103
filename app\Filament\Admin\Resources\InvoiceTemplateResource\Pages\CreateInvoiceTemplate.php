<?php

namespace App\Filament\Admin\Resources\InvoiceTemplateResource\Pages;

use App\Filament\Admin\Resources\InvoiceTemplateResource;
use Filament\Resources\Pages\CreateRecord;

class CreateInvoiceTemplate extends CreateRecord
{
    protected static string $resource = InvoiceTemplateResource::class;

    public function getTitle(): string
    {
        return 'Create Invoice Template';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set created_by to current user
        $data['created_by'] = auth()->id();

        // Ensure template_data has default structure
        if (!isset($data['template_data'])) {
            $data['template_data'] = [];
        }

        // Set default sections configuration if not provided
        if (!isset($data['template_data']['sections'])) {
            $data['template_data']['sections'] = [
                'header' => [
                    'enabled' => true,
                    'height_percentage' => 15
                ],
                'billto' => [
                    'enabled' => true,
                    'height_percentage' => 20
                ],
                'table' => [
                    'enabled' => true,
                    'height_percentage' => 45
                ],
                'inwords' => [
                    'enabled' => true,
                    'height_percentage' => 5
                ],
                'footer' => [
                    'enabled' => true,
                    'height_percentage' => 15
                ]
            ];
        }

        return $data;
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Invoice template created successfully';
    }
}
