<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syntax Fix Test</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 3px; }
        button { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="test-container" x-data="syntaxTest()">
        <h1>Alpine.js Syntax Fix Test</h1>
        
        <div class="test-section" :class="syntaxErrors.length === 0 ? 'success' : 'error'">
            <h3>Syntax Check Results</h3>
            <div x-show="syntaxErrors.length === 0">
                ✅ No syntax errors detected!
            </div>
            <div x-show="syntaxErrors.length > 0">
                ❌ Syntax errors found:
                <ul>
                    <template x-for="error in syntaxErrors" :key="error">
                        <li x-text="error"></li>
                    </template>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Component Simulation</h3>
            <p>This simulates the properties panel with safe access methods:</p>
            
            <div class="form-group">
                <label>Selected Component:</label>
                <select @change="selectComponent($event.target.value)">
                    <option value="">None</option>
                    <option value="kop">KOP Component</option>
                    <option value="table">Table Component</option>
                    <option value="bankinfo">Bank Info Component</option>
                </select>
            </div>
            
            <div x-show="selectedComponent" class="form-group">
                <label>Component Name:</label>
                <input 
                    type="text" 
                    :value="getSelectedComponentName()"
                    @input="selectedComponent.name = $event.target.value"
                    placeholder="Enter component name"
                >
            </div>
            
            <div x-show="selectedComponent" class="form-group">
                <label>Component Type:</label>
                <input type="text" :value="getSelectedComponentType()" readonly>
            </div>
            
            <div x-show="isComponentType('kop')" class="form-group">
                <label>Text Alignment:</label>
                <select 
                    :value="getConfigValue('alignment', 'center')"
                    @change="setConfigValue('alignment', $event.target.value)"
                >
                    <option value="left">Left</option>
                    <option value="center">Center</option>
                    <option value="right">Right</option>
                </select>
            </div>
            
            <div x-show="isComponentType('table')" class="form-group">
                <label>
                    <input 
                        type="checkbox" 
                        :checked="getConfigValue('show_borders', true)"
                        @change="setConfigValue('show_borders', $event.target.checked)"
                    >
                    Show Borders
                </label>
            </div>
            
            <div x-show="isComponentType('bankinfo')" class="form-group">
                <label>Layout Style:</label>
                <select 
                    :value="getConfigValue('layout', 'simple')"
                    @change="setConfigValue('layout', $event.target.value)"
                >
                    <option value="simple">Simple</option>
                    <option value="boxed">Boxed</option>
                    <option value="table">Table</option>
                </select>
            </div>
            
            <button @click="logCurrentState()">Log Current State</button>
        </div>
        
        <div class="test-section">
            <h3>Debug Log</h3>
            <div class="log" x-text="debugLog"></div>
            <button @click="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        function syntaxTest() {
            return {
                selectedComponent: null,
                syntaxErrors: [],
                debugLog: 'Test initialized...\n',
                
                init() {
                    this.checkSyntax();
                    this.log('Alpine.js syntax test initialized');
                },
                
                checkSyntax() {
                    // Test all the methods that were causing issues
                    try {
                        // Test safe access methods
                        this.getSelectedComponentName();
                        this.getSelectedComponentType();
                        this.getConfigValue('test');
                        this.isComponentType('test');
                        
                        this.log('✅ All syntax checks passed');
                    } catch (error) {
                        this.syntaxErrors.push(error.message);
                        this.log('❌ Syntax error: ' + error.message);
                    }
                },
                
                selectComponent(type) {
                    if (!type) {
                        this.selectedComponent = null;
                        this.log('Component deselected');
                        return;
                    }
                    
                    this.selectedComponent = {
                        name: `Test ${type} Component`,
                        type: type,
                        config: {}
                    };
                    
                    this.log(`Selected component: ${type}`);
                },
                
                // Safe access helper methods (same as in main template)
                getSelectedComponentName() {
                    return this.selectedComponent && this.selectedComponent.name ? this.selectedComponent.name : '';
                },

                getSelectedComponentType() {
                    return this.selectedComponent && this.selectedComponent.type ? this.selectedComponent.type : 'Unknown';
                },

                getConfigValue(key, defaultValue = '') {
                    if (!this.selectedComponent || !this.selectedComponent.config) {
                        return defaultValue;
                    }
                    return this.selectedComponent.config[key] !== undefined ? this.selectedComponent.config[key] : defaultValue;
                },

                setConfigValue(key, value) {
                    if (!this.selectedComponent) return;
                    if (!this.selectedComponent.config) {
                        this.selectedComponent.config = {};
                    }
                    this.selectedComponent.config[key] = value;
                    this.log(`Set ${key} = ${value}`);
                },

                isComponentType(type) {
                    return this.selectedComponent && this.selectedComponent.type === type;
                },
                
                logCurrentState() {
                    const state = {
                        selectedComponent: this.selectedComponent,
                        syntaxErrors: this.syntaxErrors
                    };
                    this.log('Current state: ' + JSON.stringify(state, null, 2));
                },
                
                log(message) {
                    const timestamp = new Date().toLocaleTimeString();
                    this.debugLog += `[${timestamp}] ${message}\n`;
                },
                
                clearLog() {
                    this.debugLog = '';
                    this.log('Log cleared');
                }
            }
        }
    </script>
</body>
</html>
