<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    // return view('welcome'); // Keep for future reference
    return redirect()->route('login');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // Laravel Pulse routes are automatically registered by the package at /pulse

    // Management routes (previously Admin routes)
    Route::middleware(['role:Super Admin'])->prefix('management')->name('management.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
        Route::resource('users', App\Http\Controllers\Admin\UserController::class);

        // Health Dashboard
        Route::get('/health', [App\Http\Controllers\Admin\HealthController::class, 'index'])->name('health.index');
        Route::post('/health/run-checks', [App\Http\Controllers\Admin\HealthController::class, 'runChecks'])->name('health.run-checks');
        Route::get('/health/settings', [App\Http\Controllers\Admin\HealthController::class, 'getSettings'])->name('health.settings');
        Route::post('/health/toggle-setting', [App\Http\Controllers\Admin\HealthController::class, 'toggleSetting'])->name('health.toggle-setting');
    });

    // Template Preview Routes (accessible to authenticated users)
    Route::get('/template/preview/{template?}', [App\Http\Controllers\TemplatePreviewController::class, 'show'])->name('template.preview');

    // Template Preview Routes
    Route::get('/template/preview/{template}', [App\Http\Controllers\TemplatePreviewController::class, 'show'])->name('template.preview');
});
