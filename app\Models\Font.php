<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Font extends Model
{
    use HasFactory;

    protected $table = 'fonts';

    protected $fillable = [
        'name',
        'source',
        'type',
    ];

    /**
     * Scope for fonts by source
     */
    public function scopeBySource($query, $source)
    {
        return $query->where('source', $source);
    }

    /**
     * Scope for fonts by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Check if font is Google Font (has Google Fonts URL)
     */
    public function isGoogleFont(): bool
    {
        return $this->source && str_contains($this->source, 'fonts.googleapis.com');
    }

    /**
     * Check if font is system font (no source URL)
     */
    public function isSystem(): bool
    {
        return empty($this->source);
    }

    /**
     * Check if font is custom uploaded font (has custom URL)
     */
    public function isCustom(): bool
    {
        return $this->source && !$this->isGoogleFont() && !$this->isSystem();
    }

    /**
     * Get font source type for display
     */
    public function getSourceType(): string
    {
        if ($this->isGoogleFont()) {
            return 'Google Fonts';
        } elseif ($this->isSystem()) {
            return 'System Font';
        } elseif ($this->isCustom()) {
            return 'Custom Font';
        }

        return 'Unknown';
    }

    /**
     * Get CSS font stack for this font
     */
    public function getCssStack(): string
    {
        // Use FontService to get CSS stack
        return \App\Services\FontService::getFontCss($this->name);
    }

    /**
     * Get display name with source info
     */
    public function getDisplayNameAttribute(): string
    {
        $sourceType = $this->getSourceType();
        return $this->name . " ({$sourceType})";
    }

    /**
     * Get CSS import statement for this font
     */
    public function getCssImport(): ?string
    {
        if ($this->source) {
            return "@import url('{$this->source}');";
        }

        return null;
    }

    /**
     * Get HTML link tag for this font
     */
    public function getHtmlLink(): ?string
    {
        if ($this->source) {
            return "<link href=\"{$this->source}\" rel=\"stylesheet\">";
        }

        return null;
    }
}
