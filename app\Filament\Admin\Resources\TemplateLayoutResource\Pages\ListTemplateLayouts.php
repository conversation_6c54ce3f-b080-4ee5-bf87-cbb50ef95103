<?php

namespace App\Filament\Admin\Resources\TemplateLayoutResource\Pages;

use App\Filament\Admin\Resources\TemplateLayoutResource;
use App\Models\BlockTemplate;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;

class ListTemplateLayouts extends ListRecords
{
    protected static string $resource = TemplateLayoutResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('visual_builder')
                ->label('Visual Builder v2.0')
                ->icon('heroicon-o-paint-brush')
                ->color('primary')
                ->url(route('filament.admin.pages.visual-template-builder')),

            Actions\Action::make('setup_system_blocks')
                ->label('Setup System Blocks')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('gray')
                ->action(function () {
                    BlockTemplate::createSystemTemplates();
                    
                    Notification::make()
                        ->title('System Blocks Created')
                        ->body('Default system block templates have been created successfully.')
                        ->success()
                        ->send();
                })
                ->visible(fn () => BlockTemplate::where('is_system', true)->count() === 0),

            Actions\CreateAction::make()
                ->label('New Template')
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTitle(): string
    {
        return 'Template Layouts v2.0';
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add widgets here if needed
        ];
    }
}
