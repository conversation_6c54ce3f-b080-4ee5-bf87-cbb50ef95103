<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="modern-visual-builder" x-data="modernBuilder()" x-init="init()">

        
        <div class="builder-toolbar bg-white border-b border-gray-200 px-6 py-3 sticky top-0 z-40">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6">
                    
                    <!--[if BLOCK]><![endif]--><?php if($template): ?>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="font-semibold text-gray-900"><?php echo e($template->name); ?></span>
                            <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">Table Builder</span>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                            <span class="font-medium text-gray-500">No Template Selected</span>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    
                    <div class="flex items-center space-x-2 border-l border-gray-200 pl-6">
                        <span class="text-sm text-gray-600 font-medium">Add Section:</span>
                        <button type="button"
                                class="inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-700 text-sm font-medium rounded-md hover:bg-blue-100 transition-colors"
                                @click="addNewSection('header')">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Header
                        </button>
                        <button type="button"
                                class="inline-flex items-center px-3 py-1.5 bg-green-50 text-green-700 text-sm font-medium rounded-md hover:bg-green-100 transition-colors"
                                @click="addNewSection('body')">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Body
                        </button>
                        <button type="button"
                                class="inline-flex items-center px-3 py-1.5 bg-purple-50 text-purple-700 text-sm font-medium rounded-md hover:bg-purple-100 transition-colors"
                                @click="addNewSection('footer')">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Footer
                        </button>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    
                    <div class="flex items-center space-x-2" x-show="selectedFieldId">
                        <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-blue-700 font-medium" x-text="'Selected: ' + selectedFieldName"></span>
                        <button type="button"
                                class="text-gray-400 hover:text-gray-600"
                                @click="selectedFieldId = null; selectedFieldName = null; selectedFieldType = null"
                                title="Clear Selection">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    
                    <div class="flex items-center space-x-2" x-show="autoSaving">
                        <svg class="animate-spin w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span class="text-sm text-gray-600">Saving...</span>
                    </div>

                    
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Preview:</label>
                        <select wire:model.live="previewMode" class="text-sm border-gray-300 rounded-md">
                            <option value="desktop">Desktop</option>
                            <option value="tablet">Tablet</option>
                            <option value="mobile">Mobile</option>
                        </select>
                    </div>

                    
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Page:</label>
                        <select wire:model.live="pageSize" class="text-sm border-gray-300 rounded-md">
                            <option value="legal">Legal</option>
                            <option value="f4">F4/Folio</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="builder-interface flex h-[calc(100vh-120px)]">

            
            <div class="field-palette w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="font-semibold text-gray-900 text-sm">Field Palette</h3>
                    <p class="text-xs text-gray-600 mt-1">Drag fields to table cells</p>
                </div>

                <div class="flex-1 overflow-y-auto p-3">
                    <div class="space-y-3">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableBlocks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="field-category" x-data="{ collapsed: false }">
                                
                                <button type="button"
                                        class="w-full flex items-center justify-between p-2 bg-white hover:bg-gray-50 rounded text-left border border-gray-200"
                                        @click="collapsed = !collapsed">
                                    <span class="text-sm font-medium text-gray-700"><?php echo e($category['label']); ?></span>
                                    <svg class="w-4 h-4 text-gray-500 transition-transform duration-200"
                                         :class="{ 'rotate-180': collapsed }"
                                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>

                                
                                <div class="mt-1 space-y-1"
                                     x-show="!collapsed"
                                     x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0 -translate-y-2"
                                     x-transition:enter-end="opacity-100 translate-y-0">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $category['blocks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $block): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="field-item p-2 bg-white border border-gray-200 rounded cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all"
                                             @click="selectField(<?php echo e($block['id']); ?>, '<?php echo e($block['block_type']); ?>', '<?php echo e($block['name']); ?>')"
                                             :class="{ 'border-blue-500 bg-blue-100': selectedFieldId === <?php echo e($block['id']); ?> }">
                                            <div class="flex items-center space-x-2">
                                                <div class="w-6 h-6 bg-blue-100 rounded flex items-center justify-center flex-shrink-0"
                                                     :class="{ 'bg-blue-500 text-white': selectedFieldId === <?php echo e($block['id']); ?> }">
                                                    <span class="text-xs font-bold"
                                                          :class="selectedFieldId === <?php echo e($block['id']); ?> ? 'text-white' : 'text-blue-700'">
                                                        <?php echo e(strtoupper(substr($block['block_type'], 0, 1))); ?>

                                                    </span>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-xs font-medium text-gray-900 truncate"><?php echo e($block['name']); ?></p>
                                                    <p class="text-xs text-gray-500 truncate"><?php echo e($block['block_type']); ?></p>
                                                </div>
                                                <div x-show="selectedFieldId === <?php echo e($block['id']); ?>" class="text-blue-500">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>

            
            <div class="canvas-area flex-1 bg-white flex flex-col">
                
                <div class="canvas-header p-4 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <h3 class="font-semibold text-gray-900">Visual Builder Canvas</h3>
                        <div class="text-sm text-gray-600">
                            Drag fields from palette to table cells
                        </div>
                    </div>
                </div>

                
                <div class="canvas-content flex-1 overflow-y-auto p-6">
                    <!--[if BLOCK]><![endif]--><?php if($template && $template->sections->count() > 0): ?>
                        
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $template->sections->sortBy('order'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="table-section mb-8 border border-gray-300 rounded-lg overflow-hidden"
                                 x-data="{
                                     sectionId: <?php echo e($section->id); ?>,
                                     selectedCell: null,
                                     showSectionSettings: false
                                 }">

                                
                                <div class="section-header bg-gray-50 border-b border-gray-300 p-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 rounded-full <?php echo e($section->section_type === 'header' ? 'bg-blue-500' : ($section->section_type === 'body' ? 'bg-green-500' : 'bg-purple-500')); ?>"></div>
                                            <h4 class="font-semibold text-gray-900">
                                                <?php echo e($section->section_name ?: ucfirst($section->section_type) . ' Section'); ?>

                                            </h4>
                                            <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full">
                                                Table (<?php echo e($section->table_rows ?? 1); ?>x<?php echo e($section->column_count ?? 1); ?>)
                                            </span>
                                        </div>

                                        <div class="flex items-center space-x-2">
                                            
                                            <button type="button"
                                                    class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                                                    wire:click="addTableRow(<?php echo e($section->id); ?>)"
                                                    title="Add Row">
                                                + Row
                                            </button>
                                            <button type="button"
                                                    class="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                                                    wire:click="addTableColumn(<?php echo e($section->id); ?>)"
                                                    title="Add Column">
                                                + Col
                                            </button>

                                            
                                            <button type="button"
                                                    class="p-1 text-gray-500 hover:text-blue-600 rounded"
                                                    @click="showSectionSettings = true"
                                                    title="Section Settings">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                            </button>

                                            
                                            <button type="button"
                                                    class="p-1 text-gray-500 hover:text-red-600 rounded"
                                                    wire:click="deleteSection(<?php echo e($section->id); ?>)"
                                                    onclick="return confirm('Delete this section?')"
                                                    title="Delete Section">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                
                                <div class="table-container">
                                    <table class="w-full border-collapse">
                                        <!--[if BLOCK]><![endif]--><?php for($row = 1; $row <= ($section->table_rows ?? 1); $row++): ?>
                                            <tr>
                                                <!--[if BLOCK]><![endif]--><?php for($col = 1; $col <= ($section->column_count ?? 1); $col++): ?>
                                                    <?php
                                                        $cell = $section->getCellAt($row, $col);
                                                        $cellId = $cell ? $cell->id : "new-{$section->id}-{$row}-{$col}";
                                                    ?>

                                                    <td class="border border-gray-300 p-0 relative group min-h-[80px] align-top cell-container"
                                                        <!--[if BLOCK]><![endif]--><?php if($cell && ($cell->rowspan > 1 || $cell->colspan > 1)): ?>
                                                            rowspan="<?php echo e($cell->rowspan); ?>"
                                                            colspan="<?php echo e($cell->colspan); ?>"
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                        x-data="{
                                                            cellId: '<?php echo e($cellId); ?>',
                                                            row: <?php echo e($row); ?>,
                                                            col: <?php echo e($col); ?>,
                                                            isSelected: false,
                                                            showCellMenu: false
                                                        }"
                                                        @click="dropSelectedFieldToCell(cellId, <?php echo e($section->id); ?>, row, col)"
                                                        :class="{
                                                            'bg-blue-50 ring-2 ring-blue-300': isSelected,
                                                            'hover:bg-gray-50 cursor-pointer': selectedFieldId && !isSelected,
                                                            'hover:bg-gray-50': !selectedFieldId && !isSelected
                                                        }"

                                                        
                                                        <div class="cell-content p-3 min-h-[80px]">
                                                            <!--[if BLOCK]><![endif]--><?php if($cell && $cell->block_type !== 'empty'): ?>
                                                                
                                                                <div class="field-block bg-blue-50 border border-blue-200 rounded p-2 relative group">
                                                                    <div class="flex items-center justify-between mb-1">
                                                                        <span class="text-xs font-medium text-blue-700"><?php echo e($cell->block_name ?: ucfirst($cell->block_type)); ?></span>
                                                                        <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                                            
                                                                            <button type="button"
                                                                                    class="text-blue-600 hover:text-blue-800 p-1 rounded hover:bg-blue-100"
                                                                                    @click="openFieldProperties(<?php echo e($cell->id); ?>)"
                                                                                    title="Field Properties">
                                                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                                </svg>
                                                                            </button>
                                                                            
                                                                            <div class="relative" x-data="{ showCellMenu: false }">
                                                                                <button type="button"
                                                                                        class="text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100"
                                                                                        @click="showCellMenu = !showCellMenu"
                                                                                        title="Cell Options">
                                                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                                                                    </svg>
                                                                                </button>

                                                                                
                                                                                <div x-show="showCellMenu"
                                                                                     x-transition:enter="transition ease-out duration-100"
                                                                                     x-transition:enter-start="transform opacity-0 scale-95"
                                                                                     x-transition:enter-end="transform opacity-100 scale-100"
                                                                                     x-transition:leave="transition ease-in duration-75"
                                                                                     x-transition:leave-start="transform opacity-100 scale-100"
                                                                                     x-transition:leave-end="transform opacity-0 scale-95"
                                                                                     class="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-30 w-40"
                                                                                     @click.away="showCellMenu = false">

                                                                                    
                                                                                    <button type="button"
                                                                                            class="w-full flex items-center px-3 py-2 text-sm text-purple-600 hover:bg-purple-50"
                                                                                            wire:click="splitCellRow(<?php echo e($cell->id); ?>)"
                                                                                            @click="showCellMenu = false">
                                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                                                                        </svg>
                                                                                        Split Row
                                                                                    </button>

                                                                                    
                                                                                    <button type="button"
                                                                                            class="w-full flex items-center px-3 py-2 text-sm text-purple-600 hover:bg-purple-50"
                                                                                            wire:click="splitCellColumn(<?php echo e($cell->id); ?>)"
                                                                                            @click="showCellMenu = false">
                                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                                                                        </svg>
                                                                                        Split Column
                                                                                    </button>

                                                                                    <hr class="my-1">

                                                                                    
                                                                                    <button type="button"
                                                                                            class="w-full flex items-center px-3 py-2 text-sm text-blue-600 hover:bg-blue-50"
                                                                                            @click="openCellProperties(<?php echo e($cell->id); ?>); showCellMenu = false">
                                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                                        </svg>
                                                                                        Cell Properties
                                                                                    </button>

                                                                                    
                                                                                    <button type="button"
                                                                                            class="w-full flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                                                                                            wire:click="clearCell(<?php echo e($cell->id); ?>)"
                                                                                            @click="showCellMenu = false">
                                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                                                        </svg>
                                                                                        Clear Cell
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="text-xs text-gray-600"><?php echo e($cell->block_type); ?></div>
                                                                    <!--[if BLOCK]><![endif]--><?php if($cell->field_properties): ?>
                                                                        <div class="text-xs text-gray-500 mt-1">
                                                                            <!--[if BLOCK]><![endif]--><?php if(isset($cell->field_properties['font_size'])): ?>
                                                                                Size: <?php echo e($cell->field_properties['font_size']); ?>px
                                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                            <!--[if BLOCK]><![endif]--><?php if(isset($cell->field_properties['alignment'])): ?>
                                                                                | <?php echo e(ucfirst($cell->field_properties['alignment'])); ?>

                                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                        </div>
                                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                </div>
                                                            <?php else: ?>
                                                                
                                                                <div class="drop-zone border-2 border-dashed border-gray-300 rounded p-4 text-center text-gray-500 text-xs transition-all cursor-pointer min-h-[60px] flex items-center justify-center"
                                                                     :class="{
                                                                         'border-blue-400 text-blue-600 bg-blue-50': selectedFieldId,
                                                                         'hover:border-blue-400 hover:text-blue-600 hover:bg-blue-50': selectedFieldId,
                                                                         'border-gray-300 text-gray-500': !selectedFieldId
                                                                     }">
                                                                    <div>
                                                                        <svg class="w-6 h-6 mx-auto mb-1"
                                                                             :class="selectedFieldId ? 'text-blue-400' : 'text-gray-400'"
                                                                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                                        </svg>
                                                                        <span x-text="selectedFieldId ? 'Click to add ' + selectedFieldName : 'Select field first'"></span>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                        </div>
                                                    </td>
                                                <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                            </tr>
                                        <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                    </table>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    <?php else: ?>
                        
                        <div class="empty-state text-center py-16">
                            <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Start Building Your Template</h3>
                            <p class="text-gray-600 mb-8 max-w-md mx-auto">
                                Create table-based sections and drag fields from the palette to build your invoice template.
                            </p>
                            <div class="flex justify-center space-x-3">
                                <button type="button"
                                        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                        @click="addNewSection('header')">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add Header Section
                                </button>
                                <button type="button"
                                        class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                                        @click="addNewSection('body')">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add Body Section
                                </button>
                                <button type="button"
                                        class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                                        @click="addNewSection('footer')">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add Footer Section
                                </button>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>

        
        <script>
            function modernBuilder() {
                return {
                    autoSaving: false,
                    selectedFieldId: null,
                    selectedFieldType: null,
                    selectedFieldName: null,
                    selectedCell: null,

                    init() {
                        console.log('Modern Visual Builder initialized');
                    },

                    // Select field from palette
                    selectField(fieldId, fieldType, fieldName) {
                        this.selectedFieldId = fieldId;
                        this.selectedFieldType = fieldType;
                        this.selectedFieldName = fieldName;

                        console.log('Selected field:', fieldName, fieldType);

                        // Show visual feedback
                        this.showFieldSelectedFeedback();
                    },

                    // Drop selected field to table cell
                    dropSelectedFieldToCell(cellId, sectionId, row, col) {
                        if (this.selectedFieldId) {
                            console.log('Dropping selected field to cell:', cellId, 'Field:', this.selectedFieldName);

                            // Call Livewire method to add field to cell
                            $wire.addFieldToCell(this.selectedFieldId, sectionId, row, col);

                            // Reset selection
                            this.selectedFieldId = null;
                            this.selectedFieldType = null;
                            this.selectedFieldName = null;

                            // Show success feedback
                            this.showDropSuccessFeedback();
                        } else {
                            // Show instruction to select field first
                            this.showSelectFieldFirstMessage();
                        }
                    },

                    // Add new section
                    addNewSection(sectionType) {
                        console.log('Adding new section:', sectionType);
                        $wire.addSection(sectionType);
                    },

                    // Select cell
                    selectCell(cellId) {
                        this.selectedCell = cellId;
                        console.log('Selected cell:', cellId);
                    },

                    // Open cell properties
                    openCellProperties(cellId) {
                        console.log('Opening cell properties:', cellId);
                        // TODO: Implement Filament modal for cell properties
                        // Width, padding, margin, background color
                        this.showCellPropertiesModal(cellId);
                    },

                    // Open field properties
                    openFieldProperties(fieldId) {
                        console.log('Opening field properties:', fieldId);
                        // TODO: Implement Filament modal for field properties
                        // Font size, font weight, alignment, color
                        this.showFieldPropertiesModal(fieldId);
                    },

                    // Show visual feedback for field selection
                    showFieldSelectedFeedback() {
                        // Add temporary visual indicator
                        const indicator = document.createElement('div');
                        indicator.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                        indicator.textContent = `Selected: ${this.selectedFieldName}`;
                        document.body.appendChild(indicator);

                        setTimeout(() => {
                            indicator.remove();
                        }, 2000);
                    },

                    // Show success feedback for drop
                    showDropSuccessFeedback() {
                        const indicator = document.createElement('div');
                        indicator.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                        indicator.textContent = 'Field added successfully!';
                        document.body.appendChild(indicator);

                        setTimeout(() => {
                            indicator.remove();
                        }, 2000);
                    },

                    // Show message to select field first
                    showSelectFieldFirstMessage() {
                        const indicator = document.createElement('div');
                        indicator.className = 'fixed top-4 right-4 bg-orange-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                        indicator.textContent = 'Please select a field first';
                        document.body.appendChild(indicator);

                        setTimeout(() => {
                            indicator.remove();
                        }, 2000);
                    },

                    // Show cell properties modal
                    showCellPropertiesModal(cellId) {
                        // TODO: Implement with Filament modal
                        alert('Cell Properties Modal - Cell ID: ' + cellId);
                    },

                    // Show field properties modal
                    showFieldPropertiesModal(fieldId) {
                        // TODO: Implement with Filament modal
                        alert('Field Properties Modal - Field ID: ' + fieldId);
                    },

                    // Auto-save functionality
                    autoSave() {
                        this.autoSaving = true;

                        setTimeout(() => {
                            this.autoSaving = false;
                        }, 1000);
                    }
                }
            }
        </script>

        
        <style>
            .modern-visual-builder {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .field-palette {
                background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
            }

            .field-item:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .field-item.selected {
                border-color: #3b82f6 !important;
                background-color: #dbeafe !important;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
            }

            .table-section {
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
            }

            .table-section:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .cell-container {
                transition: all 0.2s ease;
                position: relative;
            }

            .cell-container:hover {
                background-color: #f8fafc !important;
            }

            .drop-zone {
                transition: all 0.3s ease;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            }

            .drop-zone:hover {
                background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
                border-color: #3b82f6;
                transform: scale(1.02);
            }

            .field-block {
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                border: 1px solid #93c5fd;
                transition: all 0.2s ease;
            }

            .field-block:hover {
                background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
                border-color: #3b82f6;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            }

            .empty-state {
                background: radial-gradient(circle at center, #f8fafc 0%, #f1f5f9 100%);
            }

            /* Drag and drop visual feedback */
            .field-item[draggable="true"]:active {
                opacity: 0.7;
                transform: rotate(2deg);
            }

            /* Animation for successful drops */
            @keyframes dropSuccess {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            .drop-success {
                animation: dropSuccess 0.3s ease;
            }

            /* Responsive adjustments */
            @media (max-width: 1024px) {
                .field-palette {
                    width: 200px;
                }
            }

            @media (max-width: 768px) {
                .builder-interface {
                    flex-direction: column;
                }

                .field-palette {
                    width: 100%;
                    max-height: 200px;
                }
            }
        </style>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\sites\starterkit\web_starter\resources\views/filament/admin/pages/visual-template-builder.blade.php ENDPATH**/ ?>