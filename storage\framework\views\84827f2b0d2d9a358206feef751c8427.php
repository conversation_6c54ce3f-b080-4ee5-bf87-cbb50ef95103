<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="visual-template-builder" x-data="visualBuilder()" x-init="init()">

        
        <div class="builder-header bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    
                    <!--[if BLOCK]><![endif]--><?php if($template): ?>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="font-medium text-gray-900"><?php echo e($template->name); ?></span>
                            <span class="text-sm text-gray-500">v2.0</span>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                            <span class="font-medium text-gray-500">No Template Selected</span>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <div class="flex items-center space-x-4">
                    
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Preview:</label>
                        <select wire:model.live="previewMode" class="text-sm border-gray-300 rounded-md">
                            <option value="desktop">Desktop</option>
                            <option value="tablet">Tablet</option>
                            <option value="mobile">Mobile</option>
                        </select>
                    </div>

                    
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Page:</label>
                        <select wire:model.live="pageSize" class="text-sm border-gray-300 rounded-md">
                            <option value="legal">Legal</option>
                            <option value="f4">F4/Folio</option>
                        </select>
                    </div>

                    
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-xs text-gray-500">Auto-saving</span>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="builder-interface h-[calc(100vh-200px)]">

            
            <div class="canvas-column flex-1 min-w-0">
                <div class="canvas-container bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden flex flex-col">
                    <div class="canvas-header p-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900">Template Canvas</h3>
                            <div class="flex items-center space-x-2">
                                <button type="button"
                                        class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                                        wire:click="addSection('header')">
                                    + Header
                                </button>
                                <button type="button"
                                        class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
                                        wire:click="addSection('body')">
                                    + Body
                                </button>
                                <button type="button"
                                        class="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors"
                                        wire:click="addSection('footer')">
                                    + Footer
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="canvas-content p-4 overflow-y-auto flex-1">
                        <!--[if BLOCK]><![endif]--><?php if($template && $template->sections->count() > 0): ?>
                            
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $template->sections->groupBy('section_type'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionType => $sections): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="section-group mb-6">
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
                                        <?php echo e(ucfirst($sectionType)); ?> Sections
                                    </h4>

                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="template-section border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4 hover:border-blue-400 transition-colors"
                                             x-data="{
                                                sectionId: <?php echo e($section->id); ?>,
                                                isDragOver: false
                                             }"
                                             @dragover.prevent="isDragOver = true"
                                             @dragleave="isDragOver = false"
                                             @drop="dropBlock($event, sectionId); isDragOver = false"
                                             :class="{ 'border-blue-500 bg-blue-50': isDragOver }">

                                            <div class="section-header flex items-center justify-between mb-3">
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-sm font-medium text-gray-700">
                                                        <?php echo e($section->section_name ?: ucfirst($section->section_type) . ' Section'); ?>

                                                    </span>
                                                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                                        <?php echo e($section->column_count); ?> <?php echo e($section->column_count === 1 ? 'column' : 'columns'); ?>

                                                    </span>
                                                    <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                                        <?php echo e($section->blocks->count()); ?> blocks
                                                    </span>
                                                </div>
                                                <div class="flex items-center space-x-1">
                                                    <button type="button"
                                                            class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                                            @click="openSectionSettings(<?php echo e($section->id); ?>)"
                                                            title="Section Settings">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        </svg>
                                                    </button>
                                                    <button type="button"
                                                            class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                                                            wire:click="deleteSection(<?php echo e($section->id); ?>)"
                                                            onclick="return confirm('Are you sure you want to delete this section?')"
                                                            title="Delete Section">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>

                                            
                                            <div class="section-columns gap-4
                                                <?php if($section->column_layout === 'equal'): ?>
                                                    grid
                                                <?php elseif($section->column_layout === 'custom'): ?>
                                                    grid
                                                <?php else: ?>
                                                    flex
                                                <?php endif; ?>"
                                                 <?php if($section->column_layout === 'custom' && $section->column_widths): ?>
                                                     style="grid-template-columns: <?php echo e(implode(' ', array_map(fn($w) => $w.'%', $section->column_widths))); ?>;"
                                                 <?php elseif($section->column_layout === 'equal'): ?>
                                                     style="grid-template-columns: repeat(<?php echo e($section->column_count); ?>, 1fr);"
                                                 <?php else: ?>
                                                     style="display: flex;"
                                                 <?php endif; ?>>
                                                <!--[if BLOCK]><![endif]--><?php for($col = 1; $col <= $section->column_count; $col++): ?>
                                                    <div class="section-column border border-gray-200 rounded p-3 min-h-[120px] bg-gray-50 relative <?php echo e($section->column_layout === 'flex' ? 'flex-1' : ''); ?>"
                                                         x-data="{ columnId: <?php echo e($col); ?>, showColumnSettings: false }"
                                                         @dragover.prevent
                                                         @drop="dropBlockToColumn($event, sectionId, columnId)">
                                                        <div class="flex items-center justify-between mb-2">
                                                            <div class="text-xs text-gray-500 font-medium">Column <?php echo e($col); ?></div>
                                                            <button type="button"
                                                                    class="p-1 text-gray-400 hover:text-blue-600 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                                                                    @click="showColumnSettings = !showColumnSettings"
                                                                    title="Column Settings">
                                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                </svg>
                                                            </button>
                                                        </div>

                                                        
                                                        <div x-show="showColumnSettings"
                                                             x-transition:enter="transition ease-out duration-200"
                                                             x-transition:enter-start="opacity-0 scale-95"
                                                             x-transition:enter-end="opacity-100 scale-100"
                                                             x-transition:leave="transition ease-in duration-150"
                                                             x-transition:leave-start="opacity-100 scale-100"
                                                             x-transition:leave-end="opacity-0 scale-95"
                                                             class="absolute top-8 right-2 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-10 w-48"
                                                             @click.away="showColumnSettings = false">
                                                            <div class="space-y-2">
                                                                <div>
                                                                    <label class="block text-xs font-medium text-gray-700 mb-1">Layout Type</label>
                                                                    <select class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                                                        <option value="flex">Flex Layout</option>
                                                                        <option value="grid">Grid Layout</option>
                                                                        <option value="block">Block Layout</option>
                                                                    </select>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-xs font-medium text-gray-700 mb-1">Alignment</label>
                                                                    <select class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                                                        <option value="start">Start</option>
                                                                        <option value="center">Center</option>
                                                                        <option value="end">End</option>
                                                                        <option value="stretch">Stretch</option>
                                                                    </select>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-xs font-medium text-gray-700 mb-1">Gap (px)</label>
                                                                    <input type="number" class="w-full text-xs border border-gray-300 rounded px-2 py-1" value="8" min="0" max="50">
                                                                </div>
                                                            </div>
                                                        </div>

                                                        
                                                        <div class="blocks-list space-y-2">
                                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $section->blocks->where('column_position', $col)->sortBy('order'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $block): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="content-block bg-white border border-gray-200 rounded p-3 shadow-sm hover:shadow-md transition-shadow group"
                                                                     x-data="{ blockId: <?php echo e($block->id); ?>, showBlockMenu: false, isDragging: false }"
                                                                     draggable="true"
                                                                     @dragstart="startBlockDrag($event, blockId); isDragging = true"
                                                                     @dragend="isDragging = false">

                                                                    
                                                                    <div class="flex items-center justify-between mb-2">
                                                                        <div class="flex items-center space-x-2 flex-1 min-w-0">
                                                                            
                                                                            <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 p-1 rounded hover:bg-gray-100 transition-colors"
                                                                                 title="Drag to reorder">
                                                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                                                                </svg>
                                                                            </div>

                                                                            
                                                                            <div class="flex-1 min-w-0">
                                                                                <span class="text-xs font-medium text-gray-700 truncate block"><?php echo e($block->block_name ?: ucfirst($block->block_type)); ?></span>
                                                                                <span class="text-xs text-gray-500"><?php echo e($block->block_type); ?></span>
                                                                            </div>
                                                                        </div>

                                                                        
                                                                        <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                                            
                                                                            <button type="button"
                                                                                    class="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                                                                    @click="openBlockSettings(blockId)"
                                                                                    title="Block Settings">
                                                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                                </svg>
                                                                            </button>

                                                                            
                                                                            <!--[if BLOCK]><![endif]--><?php if(in_array($block->block_type, ['company_logo', 'comp_logo'])): ?>
                                                                                <button type="button"
                                                                                        class="p-1 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded transition-colors"
                                                                                        @click="openImageSettings(blockId)"
                                                                                        title="Image Settings">
                                                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                                    </svg>
                                                                                </button>
                                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                                                            
                                                                            <div class="relative" x-data="{ showMenu: false }">
                                                                                <button type="button"
                                                                                        class="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded transition-colors"
                                                                                        @click="showMenu = !showMenu"
                                                                                        title="More Actions">
                                                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                                                                    </svg>
                                                                                </button>

                                                                                
                                                                                <div x-show="showMenu"
                                                                                     x-transition:enter="transition ease-out duration-100"
                                                                                     x-transition:enter-start="transform opacity-0 scale-95"
                                                                                     x-transition:enter-end="transform opacity-100 scale-100"
                                                                                     x-transition:leave="transition ease-in duration-75"
                                                                                     x-transition:leave-start="transform opacity-100 scale-100"
                                                                                     x-transition:leave-end="transform opacity-0 scale-95"
                                                                                     class="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-20 w-40"
                                                                                     @click.away="showMenu = false">

                                                                                    
                                                                                    <button type="button"
                                                                                            class="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-600"
                                                                                            wire:click="moveBlockUp(<?php echo e($block->id); ?>)"
                                                                                            @click="showMenu = false">
                                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                                                                        </svg>
                                                                                        Move Up
                                                                                    </button>

                                                                                    
                                                                                    <button type="button"
                                                                                            class="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-600"
                                                                                            wire:click="moveBlockDown(<?php echo e($block->id); ?>)"
                                                                                            @click="showMenu = false">
                                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                                                        </svg>
                                                                                        Move Down
                                                                                    </button>

                                                                                    <hr class="my-1">

                                                                                    
                                                                                    <button type="button"
                                                                                            class="w-full flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                                                                                            wire:click="deleteBlock(<?php echo e($block->id); ?>)"
                                                                                            onclick="return confirm('Remove this block?')"
                                                                                            @click="showMenu = false">
                                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                                                        </svg>
                                                                                        Delete Block
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    
                                                                    <div class="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                                                        <div class="truncate"><?php echo e($block->block_type); ?> block</div>
                                                                        <!--[if BLOCK]><![endif]--><?php if($block->content_data): ?>
                                                                            <div class="text-xs text-gray-500 mt-1 truncate">
                                                                                <!--[if BLOCK]><![endif]--><?php if(isset($block->content_data['text'])): ?>
                                                                                    "<?php echo e(Str::limit($block->content_data['text'], 30)); ?>"
                                                                                <?php elseif(isset($block->content_data['field_path'])): ?>
                                                                                    Field: <?php echo e($block->content_data['field_path']); ?>

                                                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                            </div>
                                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                        </div>

                                                        
                                                        <div class="add-block-zone border-2 border-dashed border-gray-300 rounded p-4 text-center text-gray-500 text-xs hover:border-blue-400 hover:text-blue-600 hover:bg-blue-50 transition-all cursor-pointer"
                                                             x-data="{ showBlockSelector: false }"
                                                             @click="showBlockSelector = true">
                                                            <svg class="w-6 h-6 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                            </svg>
                                                            Click to add block

                                                            
                                                            <div x-show="showBlockSelector"
                                                                 x-transition:enter="transition ease-out duration-200"
                                                                 x-transition:enter-start="opacity-0 scale-95"
                                                                 x-transition:enter-end="opacity-100 scale-100"
                                                                 x-transition:leave="transition ease-in duration-150"
                                                                 x-transition:leave-start="opacity-100 scale-100"
                                                                 x-transition:leave-end="opacity-0 scale-95"
                                                                 class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-75"
                                                                 @click="if ($event.target === $event.currentTarget) showBlockSelector = false"
                                                                 @keydown.escape.window="showBlockSelector = false"
                                                                 x-trap.inert.noscroll="showBlockSelector"
                                                                 style="display: none;"
                                                                 x-cloak>

                                                                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden"
                                                                     @click.stop>
                                                                    
                                                                    <div class="p-4 border-b border-gray-200">
                                                                        <div class="flex items-center justify-between">
                                                                            <h3 class="text-lg font-semibold text-gray-900">Add Block to Column <?php echo e($col); ?></h3>
                                                                            <button type="button"
                                                                                    class="p-2 text-gray-400 hover:text-gray-600 rounded"
                                                                                    @click="showBlockSelector = false">
                                                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                                                </svg>
                                                                            </button>
                                                                        </div>
                                                                    </div>

                                                                    
                                                                    <div class="p-4 overflow-y-auto max-h-96">
                                                                        <div class="space-y-3">
                                                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableBlocks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <div class="block-category" x-data="{ collapsed: false }">
                                                                                    
                                                                                    <button type="button"
                                                                                            class="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                                                                                            @click="collapsed = !collapsed">
                                                                                        <div class="flex items-center space-x-3">
                                                                                            <div class="w-8 h-8 bg-gradient-to-br from-gray-200 to-gray-300 rounded flex items-center justify-center">
                                                                                                <span class="text-sm font-bold text-gray-600"><?php echo e(strtoupper(substr($category['label'], 0, 2))); ?></span>
                                                                                            </div>
                                                                                            <h4 class="font-medium text-gray-700">
                                                                                                <?php echo e($category['label']); ?>

                                                                                            </h4>
                                                                                            <span class="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">
                                                                                                <?php echo e(count($category['blocks'])); ?>

                                                                                            </span>
                                                                                        </div>
                                                                                        <svg class="w-5 h-5 text-gray-500 transition-transform duration-200"
                                                                                             :class="{ 'rotate-180': collapsed }"
                                                                                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                                                        </svg>
                                                                                    </button>

                                                                                    
                                                                                    <div class="mt-2 space-y-2"
                                                                                         x-show="!collapsed"
                                                                                         x-transition:enter="transition ease-out duration-200"
                                                                                         x-transition:enter-start="opacity-0 -translate-y-2"
                                                                                         x-transition:enter-end="opacity-100 translate-y-0"
                                                                                         x-transition:leave="transition ease-in duration-150"
                                                                                         x-transition:leave-start="opacity-100 translate-y-0"
                                                                                         x-transition:leave-end="opacity-0 -translate-y-2">
                                                                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $category['blocks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $block): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                            <div class="block-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 hover:shadow-sm transition-all duration-200 ml-4"
                                                                                                 @click="$wire.addBlockToSection(<?php echo e($block['id']); ?>, <?php echo e($section->id); ?>, <?php echo e($col); ?>); showBlockSelector = false">
                                                                                                <div class="flex items-center space-x-3">
                                                                                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded flex items-center justify-center flex-shrink-0">
                                                                                                        <span class="text-sm font-bold text-blue-700"><?php echo e(strtoupper(substr($block['block_type'], 0, 2))); ?></span>
                                                                                                    </div>
                                                                                                    <div class="flex-1 min-w-0">
                                                                                                        <p class="text-sm font-medium text-gray-900"><?php echo e($block['name']); ?></p>
                                                                                                        <p class="text-xs text-gray-500"><?php echo e($block['description']); ?></p>
                                                                                                        <!--[if BLOCK]><![endif]--><?php if(!empty($block['tags'])): ?>
                                                                                                            <div class="flex flex-wrap gap-1 mt-1">
                                                                                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = array_slice($block['tags'], 0, 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                                                    <span class="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded"><?php echo e($tag); ?></span>
                                                                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                                                                            </div>
                                                                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                                                    </div>
                                                                                                    <div class="text-blue-500 flex-shrink-0">
                                                                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                                                                        </svg>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                                                    </div>
                                                                                </div>
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                                        </div>
                                                                    </div>

                                                                    
                                                                    <div class="p-4 border-t border-gray-200 bg-gray-50">
                                                                        <div class="flex justify-between items-center">
                                                                            <p class="text-sm text-gray-600">
                                                                                💡 Tip: Use combined blocks for faster template building
                                                                            </p>
                                                                            <button type="button"
                                                                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm"
                                                                                    @click="showBlockSelector = false">
                                                                                Cancel
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        <?php else: ?>
                            
                            <div class="empty-canvas text-center py-12">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Template</h3>
                                <p class="text-gray-500 mb-4">Add sections to begin creating your invoice template</p>
                                <div class="flex justify-center space-x-2">
                                    <button type="button"
                                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                            wire:click="addSection('header')">
                                        Add Header Section
                                    </button>
                                    <button type="button"
                                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                                            wire:click="addSection('body')">
                                        Add Body Section
                                    </button>
                                    <button type="button"
                                            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                                            wire:click="addSection('footer')">
                                        Add Footer Section
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>

        </div>

        
        <div x-show="showPropertiesModal"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-50 overflow-hidden"
             style="display: none;">

            
            <div class="absolute inset-0 bg-black bg-opacity-50" @click="closePropertiesModal()"></div>

            
            <div class="absolute right-0 top-0 h-full w-96 bg-white shadow-xl"
                 x-show="showPropertiesModal"
                 x-transition:enter="transition ease-out duration-300 transform"
                 x-transition:enter-start="translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transition ease-in duration-200 transform"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="translate-x-full">

                
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <h3 class="text-lg font-semibold text-gray-900" x-text="propertiesTitle">Properties</h3>
                            
                            <div class="flex items-center space-x-1 text-xs text-gray-500">
                                <svg wire:loading.remove wire:target="autoSaveSectionSettings" class="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <svg wire:loading wire:target="autoSaveSectionSettings" class="w-3 h-3 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span wire:loading.remove wire:target="autoSaveSectionSettings">Auto-saved</span>
                                <span wire:loading wire:target="autoSaveSectionSettings">Saving...</span>
                            </div>
                        </div>
                        <button type="button"
                                class="p-2 text-gray-400 hover:text-gray-600 rounded"
                                @click="closePropertiesModal()">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                
                <div class="p-4 overflow-y-auto h-full">
                    <div x-show="propertiesType === 'section'">
                        <h4 class="font-medium text-gray-900 mb-3">Section Settings</h4>
                        <form wire:submit.prevent="updateSectionSettings">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Section Name</label>
                                    <input type="text"
                                           wire:model.live="sectionSettings.section_name"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           placeholder="Custom section name">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Columns</label>
                                    <select wire:model.live="sectionSettings.column_count"
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="1">1 Column</option>
                                        <option value="2">2 Columns</option>
                                        <option value="3">3 Columns</option>
                                        <option value="4">4 Columns</option>
                                    </select>
                                </div>
                                <div x-show="$wire.sectionSettings.column_count > 1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Column Layout</label>
                                    <select wire:model.live="sectionSettings.column_layout"
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="equal">Equal Width (Grid)</option>
                                        <option value="flex">Flexible Width (Flex)</option>
                                        <option value="custom">Custom Width (Grid)</option>
                                    </select>
                                </div>
                                <div x-show="$wire.sectionSettings.column_layout === 'custom' && $wire.sectionSettings.column_count > 1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Column Widths (%)</label>
                                    <div class="text-xs text-gray-500 mb-2">Enter percentages for each column (must total 100%)</div>
                                    <template x-for="i in parseInt($wire.sectionSettings.column_count)" :key="i">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <span class="text-sm w-16" x-text="'Column ' + i + ':'"></span>
                                            <input type="number"
                                                   x-model="$wire.sectionSettings.column_widths[i-1]"
                                                   class="flex-1 border border-gray-300 rounded-md px-2 py-1 text-sm"
                                                   min="5" max="95" placeholder="25">
                                            <span class="text-sm text-gray-500">%</span>
                                        </div>
                                    </template>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
                                    <input type="color"
                                           wire:model.live="sectionSettings.background_color"
                                           class="w-full h-10 border border-gray-300 rounded-md">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Minimum Height (px)</label>
                                    <input type="number"
                                           wire:model.live="sectionSettings.min_height"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           min="50" max="500" placeholder="120">
                                </div>
                            </div>
                        </form>
                    </div>

                    <div x-show="propertiesType === 'block'">
                        <h4 class="font-medium text-gray-900 mb-3">Block Settings</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Block Name</label>
                                <input type="text"
                                       wire:model.live="blockSettings.block_name"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       placeholder="Custom block name">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Alignment</label>
                                <select wire:model.live="blockSettings.alignment"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="left">Left</option>
                                    <option value="center">Center</option>
                                    <option value="right">Right</option>
                                    <option value="justify">Justify</option>
                                </select>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
                                    <input type="number"
                                           wire:model.live="blockSettings.font_size"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           min="8" max="24" placeholder="12">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Font Weight</label>
                                    <select wire:model.live="blockSettings.font_weight"
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="normal">Normal</option>
                                        <option value="bold">Bold</option>
                                        <option value="lighter">Light</option>
                                    </select>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Font Color</label>
                                <div class="flex items-center space-x-2">
                                    <input type="color"
                                           wire:model.live="blockSettings.font_color"
                                           class="w-12 h-10 border border-gray-300 rounded-md">
                                    <input type="text"
                                           wire:model.live="blockSettings.font_color"
                                           class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           placeholder="#000000">
                                </div>
                            </div>
                            <div x-show="propertiesType === 'block' && ['custom_text'].includes(selectedBlockType)">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Custom Text</label>
                                <textarea wire:model="blockSettings.custom_text"
                                          class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                          rows="3"
                                          placeholder="Enter custom text content"></textarea>
                            </div>
                            <div x-show="propertiesType === 'block' && ['spacer'].includes(selectedBlockType)">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Spacer Height (px)</label>
                                <input type="number"
                                       wire:model="blockSettings.spacer_height"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       min="5" max="100" placeholder="20">
                            </div>
                        </div>
                    </div>

                    <div x-show="propertiesType === 'image'">
                        <h4 class="font-medium text-gray-900 mb-3">Image Settings</h4>
                        <div class="space-y-4">
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Width</label>
                                    <input type="text"
                                           wire:model.live="blockSettings.width"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           placeholder="100px or auto">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Height</label>
                                    <input type="text"
                                           wire:model.live="blockSettings.height"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           placeholder="auto or 100px">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Image Fit</label>
                                <select wire:model.live="blockSettings.object_fit"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="contain">Contain (fit inside)</option>
                                    <option value="cover">Cover (fill area)</option>
                                    <option value="fill">Fill (stretch)</option>
                                    <option value="scale-down">Scale Down</option>
                                    <option value="none">None (original)</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Alt Text</label>
                                <input type="text"
                                       wire:model.live="blockSettings.alt_text"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       placeholder="Image description">
                            </div>
                        </div>
                    </div>
                </div>

                
                <div class="p-4 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <div class="text-xs text-gray-500">
                            <span x-show="propertiesType === 'section'">Changes are saved automatically</span>
                            <span x-show="propertiesType === 'block'">Changes are saved automatically</span>
                            <span x-show="propertiesType === 'image'">Changes are saved automatically</span>
                        </div>
                        <div class="flex space-x-2">
                            <button type="button"
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm"
                                    @click="closePropertiesModal()">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <style>
        /* Section column hover effects */
        .section-column:hover .opacity-0 {
            opacity: 1;
        }

        .content-block:hover .opacity-0 {
            opacity: 1;
        }

        /* Category collapse animation */
        .block-category button svg {
            transition: transform 0.2s ease;
        }

        /* Better spacing for combined blocks */
        .combined-fields .field-address {
            margin-bottom: 0.25rem;
        }

        .combined-fields .field-phone,
        .combined-fields .field-email,
        .combined-fields .field-website,
        .combined-fields .field-fax {
            margin-bottom: 0.125rem;
        }

        /* Company header styling */
        .company-header {
            align-items: center;
            gap: 1rem;
        }

        .company-header .company-logo img {
            max-width: 100%;
            height: auto;
            object-fit: contain;
        }

        /* Block Actions Dropdown */
        .content-block .relative {
            z-index: 10;
        }

        .content-block .relative > div[x-show="showMenu"] {
            z-index: 30;
        }

        /* Block Selector Modal Styling */
        .add-block-zone:hover {
            background-color: #eff6ff;
            border-color: #3b82f6;
            transform: translateY(-1px);
        }

        .block-selector-modal .block-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Improved block item styling in modal */
        .block-selector-modal .block-item {
            transition: all 0.2s ease;
        }

        .block-selector-modal .block-item:active {
            transform: scale(0.98);
        }

        /* Tag styling */
        .block-item .tag {
            font-size: 0.625rem;
            padding: 0.125rem 0.375rem;
        }

        /* Block preview improvements */
        .content-block {
            position: relative;
        }

        .content-block:hover {
            border-color: #3b82f6;
        }

        /* Dropdown menu styling */
        .content-block .relative > div[x-show] {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        /* Better button hover states */
        .content-block button:hover {
            transform: translateY(-1px);
        }

        /* Full width canvas adjustments */
        .canvas-container {
            width: 100%;
            max-width: none;
        }

        /* Better spacing for full width */
        .section-columns {
            gap: 1.5rem;
        }

        /* Responsive adjustments for full width */
        @media (max-width: 768px) {
            .builder-interface {
                height: auto;
                min-height: calc(100vh - 200px);
            }

            .canvas-column {
                min-height: 500px;
            }

            .section-columns {
                grid-template-columns: 1fr !important;
                gap: 1rem;
            }

            .add-block-zone {
                padding: 2rem 1rem;
            }

            .block-selector-modal .bg-white {
                max-width: 95vw;
                margin: 1rem;
            }
        }

        /* Animation for click to add */
        @keyframes pulse-add {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .add-block-zone:active {
            animation: pulse-add 0.2s ease;
        }
    </style>

    
    <script>
        function visualBuilder() {
            return {
                draggedBlockId: null,
                showPropertiesModal: false,
                propertiesType: null,
                propertiesTitle: 'Properties',
                selectedElementId: null,

                // Drag & Drop for existing blocks (reordering)
                startBlockDrag(event, blockId) {
                    this.draggedBlockId = blockId;
                    event.dataTransfer.effectAllowed = 'move';
                    event.dataTransfer.setData('text/plain', blockId);

                    // Add visual feedback
                    event.target.style.opacity = '0.5';
                    console.log('Started dragging existing block:', blockId);
                },

                dropBlockToColumn(event, sectionId, columnId) {
                    event.preventDefault();
                    const blockId = this.draggedBlockId;

                    if (blockId && sectionId && columnId) {
                        console.log('Dropping block', blockId, 'to section', sectionId, 'column', columnId);
                        // Call Livewire method to move block to specific column
                        window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('moveBlockToColumn', blockId, sectionId, columnId);
                    }

                    // Reset drag state
                    this.draggedBlockId = null;
                    event.target.style.opacity = '1';
                },

                openSectionSettings(sectionId) {
                    console.log('🔧 Opening section settings for:', sectionId);
                    this.selectedElementId = sectionId;
                    this.propertiesType = 'section';
                    this.propertiesTitle = 'Section Settings';

                    // Load section settings from server
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('selectedElementId', sectionId);
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('loadSectionSettings', sectionId)
                        .then(() => {
                            console.log('✅ Section settings loaded');
                        })
                        .catch((error) => {
                            console.error('❌ Error loading section settings:', error);
                        });

                    this.showPropertiesModal = true;
                },

                openBlockSettings(blockId) {
                    this.selectedElementId = blockId;
                    this.propertiesType = 'block';
                    this.propertiesTitle = 'Block Settings';

                    // Load block settings from server
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('loadBlockSettings', blockId)
                        .then(() => {
                            this.showPropertiesModal = true;
                            console.log('✅ Block settings loaded for:', blockId);
                        })
                        .catch((error) => {
                            console.error('❌ Error loading block settings:', error);
                        });
                },

                openImageSettings(blockId) {
                    this.selectedElementId = blockId;
                    this.propertiesType = 'image';
                    this.propertiesTitle = 'Image Settings';

                    // Load image settings from server
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('loadImageSettings', blockId)
                        .then(() => {
                            this.showPropertiesModal = true;
                            console.log('✅ Image settings loaded for:', blockId);
                        })
                        .catch((error) => {
                            console.error('❌ Error loading image settings:', error);
                        });
                },

                closePropertiesModal() {
                    this.showPropertiesModal = false;
                    this.propertiesType = null;
                    this.selectedElementId = null;
                    this.propertiesTitle = 'Properties';
                },

                init() {
                    console.log('🚀 Visual Builder v2.0 - Hybrid System initialized');
                    console.log('📋 Features: Click to Add + Drag to Reorder + Easy Settings Access');

                    // Listen for Livewire events
                    this.$wire.on('section-updated', (data) => {
                        console.log('✅ Section auto-saved:', data);
                    });

                    this.$wire.on('section-update-failed', (data) => {
                        console.error('❌ Section auto-save failed:', data);
                    });

                    // Add keyboard shortcuts
                    document.addEventListener('keydown', (e) => {
                        // ESC to close modals
                        if (e.key === 'Escape') {
                            // Close any open block selectors
                            const blockSelectors = document.querySelectorAll('[x-data*="showBlockSelector"]');
                            blockSelectors.forEach(el => {
                                if (el.__x && el.__x.$data.showBlockSelector) {
                                    el.__x.$data.showBlockSelector = false;
                                }
                            });

                            // Close properties modal
                            if (this.showPropertiesModal) {
                                this.closePropertiesModal();
                            }
                        }
                    });
                },

                addSection(sectionType) {
                    console.log('Adding section:', sectionType);
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('addSection', sectionType);
                }
            }
        }

        // Global drag event handlers for existing blocks
        document.addEventListener('dragend', function(event) {
            // Reset opacity after drag
            event.target.style.opacity = '';
        });

        document.addEventListener('dragover', function(event) {
            event.preventDefault();
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\sites\starterkit\web_starter\resources\views/filament/admin/pages/visual-template-builder.blade.php ENDPATH**/ ?>