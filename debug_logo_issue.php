<?php

/**
 * Debug Logo Issue
 * Run via: php artisan tinker
 */

echo "=== Debug Logo Issue ===\n";

try {
    // 1. Find the problematic block
    echo "1. Finding company_logo blocks...\n";
    
    $logoBlocks = App\Models\ContentBlock::where('block_type', 'company_logo')->get();
    echo "   📊 Found {$logoBlocks->count()} company_logo blocks\n";
    
    if ($logoBlocks->count() > 0) {
        $testBlock = $logoBlocks->first();
        echo "   📋 Testing block ID: {$testBlock->id}\n";
        echo "   📋 Block type: {$testBlock->block_type}\n";
        echo "   📋 Block name: {$testBlock->block_name}\n";
        echo "   📋 Section ID: {$testBlock->layout_section_id}\n";
        echo "   📋 Column position: {$testBlock->column_position}\n";
        
        // Check content_data
        echo "\n   📄 Content Data:\n";
        if ($testBlock->content_data) {
            foreach ($testBlock->content_data as $key => $value) {
                echo "      {$key}: " . (is_array($value) ? json_encode($value) : $value) . "\n";
            }
        } else {
            echo "      ❌ No content_data found\n";
        }
        
        // Check field_mapping
        echo "\n   📄 Field Mapping:\n";
        if ($testBlock->field_mapping) {
            foreach ($testBlock->field_mapping as $key => $value) {
                echo "      {$key}: " . (is_array($value) ? json_encode($value) : $value) . "\n";
            }
        } else {
            echo "      ❌ No field_mapping found\n";
        }
    }
    
    // 2. Get actual company data
    echo "\n2. Getting actual company data...\n";
    
    $companies = App\Models\Company::whereNotNull('logo')->take(3)->get();
    echo "   📊 Found {$companies->count()} companies with logos\n";
    
    foreach ($companies as $company) {
        echo "   📋 Company: {$company->name}\n";
        echo "      ID: {$company->id}\n";
        echo "      Logo: " . ($company->logo ?? 'NULL') . "\n";
        
        // Check if logo file exists
        if ($company->logo) {
            $logoPath = storage_path('app/public/' . $company->logo);
            if (file_exists($logoPath)) {
                echo "      ✅ File exists: {$logoPath}\n";
                echo "      📊 File size: " . number_format(filesize($logoPath)) . " bytes\n";
            } else {
                echo "      ❌ File NOT found: {$logoPath}\n";
            }
            
            // Test URL generation
            $testUrl = url('storage/' . ltrim($company->logo, '/'));
            echo "      🔗 Generated URL: {$testUrl}\n";
        }
    }
    
    // 3. Test with real data
    echo "\n3. Testing with real company data...\n";
    
    if ($companies->count() > 0) {
        $testCompany = $companies->first();
        
        $testData = [
            'company' => $testCompany,
            'invoice' => (object) [
                'invoice_no' => 'TEST-001',
                'invoice_date' => '2024-01-20',
            ],
            'customer' => (object) [
                'name' => 'Test Customer',
            ]
        ];
        
        echo "   📋 Test data created with company: {$testCompany->name}\n";
        echo "   📋 Company logo: " . ($testCompany->logo ?? 'NULL') . "\n";
        
        // Test rendering with real block
        if ($logoBlocks->count() > 0) {
            $testBlock = $logoBlocks->first();
            
            echo "\n   🧪 Testing block rendering...\n";
            
            try {
                $renderedHtml = $testBlock->renderHtml($testData);
                
                echo "   📄 Rendered HTML:\n";
                echo "   " . str_repeat("-", 50) . "\n";
                echo "   " . $renderedHtml . "\n";
                echo "   " . str_repeat("-", 50) . "\n";
                
                // Check for specific issues
                if (strpos($renderedHtml, 'Unknown block type') !== false) {
                    echo "   ❌ Still shows 'Unknown block type'\n";
                } elseif (strpos($renderedHtml, '<img') !== false) {
                    echo "   ✅ Contains img tag\n";
                    
                    // Extract src
                    if (preg_match('/src="([^"]*)"/', $renderedHtml, $matches)) {
                        $src = $matches[1];
                        echo "   🔗 Image src: {$src}\n";
                        
                        // Check for double slash
                        if (strpos($src, '//') !== false && strpos($src, 'http://') !== 0 && strpos($src, 'https://') !== 0) {
                            echo "   ❌ Double slash detected in URL\n";
                        } else {
                            echo "   ✅ URL looks good\n";
                        }
                    }
                } elseif (empty(trim($renderedHtml))) {
                    echo "   ⚠️  Empty output (logo might be null)\n";
                } else {
                    echo "   ⚠️  Unexpected output format\n";
                }
                
            } catch (\Exception $e) {
                echo "   ❌ Error rendering: " . $e->getMessage() . "\n";
                echo "   📄 Stack trace:\n";
                echo "   " . $e->getTraceAsString() . "\n";
            }
        }
    }
    
    // 4. Test renderFieldBasedContent directly
    echo "\n4. Testing renderFieldBasedContent directly...\n";
    
    if ($companies->count() > 0 && $logoBlocks->count() > 0) {
        $testCompany = $companies->first();
        $testBlock = $logoBlocks->first();
        
        $testData = ['company' => $testCompany];
        
        echo "   📋 Direct test with:\n";
        echo "      Company: {$testCompany->name}\n";
        echo "      Logo: " . ($testCompany->logo ?? 'NULL') . "\n";
        
        try {
            // Use reflection to call private method
            $reflection = new ReflectionClass($testBlock);
            $method = $reflection->getMethod('renderFieldBasedContent');
            $method->setAccessible(true);
            
            $fieldPath = $testBlock->content_data['field_path'] ?? 'company.logo';
            echo "      Field path: {$fieldPath}\n";
            
            $result = $method->invoke($testBlock, $testData, $fieldPath, 'image');
            
            echo "   📄 Direct result:\n";
            echo "   " . str_repeat("-", 50) . "\n";
            echo "   " . $result . "\n";
            echo "   " . str_repeat("-", 50) . "\n";
            
        } catch (\Exception $e) {
            echo "   ❌ Error in direct test: " . $e->getMessage() . "\n";
        }
    }
    
    // 5. Test data_get function
    echo "\n5. Testing data_get function...\n";
    
    if ($companies->count() > 0) {
        $testCompany = $companies->first();
        $testData = ['company' => $testCompany];
        
        $testPaths = [
            'company.logo',
            'company->logo',
            'company.name',
        ];
        
        foreach ($testPaths as $path) {
            $value = data_get($testData, $path, 'NOT_FOUND');
            echo "   📋 data_get(\$data, '{$path}'): " . ($value === 'NOT_FOUND' ? 'NOT_FOUND' : $value) . "\n";
        }
        
        // Test direct access
        echo "   📋 Direct access \$data['company']->logo: " . ($testData['company']->logo ?? 'NULL') . "\n";
        echo "   📋 Direct access \$data['company']['logo']: ";
        try {
            echo $testData['company']['logo'] ?? 'NULL';
        } catch (\Exception $e) {
            echo "ERROR: " . $e->getMessage();
        }
        echo "\n";
    }
    
    // 6. Check template layout and sections
    echo "\n6. Checking template layout and sections...\n";
    
    if ($logoBlocks->count() > 0) {
        $testBlock = $logoBlocks->first();
        $section = $testBlock->layoutSection;
        $template = $section->templateLayout ?? null;
        
        echo "   📋 Section: {$section->section_name} (ID: {$section->id})\n";
        echo "   📋 Section type: {$section->section_type}\n";
        
        if ($template) {
            echo "   📋 Template: {$template->name} (ID: {$template->id})\n";
            echo "   📋 Template company_id: " . ($template->company_id ?? 'NULL') . "\n";
        } else {
            echo "   ❌ No template found\n";
        }
    }
    
    // 7. Summary and recommendations
    echo "\n=== Debug Summary ===\n";
    
    echo "📋 Findings:\n";
    echo "✅ ContentBlock switch statement has correct cases for logo\n";
    echo "✅ renderFieldBasedContent method exists and handles 'image' format\n";
    echo "✅ URL generation logic removes double slashes\n";
    
    echo "\n🔧 Check these items:\n";
    echo "1. Does content_data have correct field_path?\n";
    echo "2. Is company data being passed correctly to renderHtml()?\n";
    echo "3. Are logo files actually accessible?\n";
    echo "4. Is data_get() working with the data structure?\n";
    
    echo "\n🚀 Next steps:\n";
    echo "1. Check the actual data being passed to template preview\n";
    echo "2. Verify the block's content_data configuration\n";
    echo "3. Test with a company that definitely has a logo\n";
    echo "4. Check browser network tab for 404s on logo URLs\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
