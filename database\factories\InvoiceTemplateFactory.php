<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\InvoiceTemplate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InvoiceTemplate>
 */
class InvoiceTemplateFactory extends Factory
{
    protected $model = InvoiceTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true) . ' Template',
            'description' => $this->faker->sentence(),
            'type' => $this->faker->randomElement(['custom', 'legacy']),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'created_by' => User::factory(),
            'company_id' => $this->faker->boolean(70) ? Company::factory() : null, // 70% chance of being company-specific
            'template_data' => [
                'page_size' => $this->faker->randomElement(['legal', 'f4']),
                'orientation' => 'portrait',
                'margins' => [
                    'top' => 15,
                    'right' => 15,
                    'bottom' => 15,
                    'left' => 15
                ],
                'font_family' => $this->faker->randomElement(['DejaVu Sans', 'Arial', 'Times New Roman']),
                'font_size' => $this->faker->numberBetween(10, 14),
                'colors' => [
                    'primary' => $this->faker->hexColor(),
                    'secondary' => $this->faker->hexColor(),
                    'text' => '#000000'
                ],
                'sections' => [
                    'header' => [
                        'enabled' => true,
                        'height_percentage' => 15
                    ],
                    'billto' => [
                        'enabled' => true,
                        'height_percentage' => 20
                    ],
                    'table' => [
                        'enabled' => true,
                        'height_percentage' => 45
                    ],
                    'inwords' => [
                        'enabled' => true,
                        'height_percentage' => 5
                    ],
                    'footer' => [
                        'enabled' => true,
                        'height_percentage' => 15
                    ]
                ]
            ],
            'preview_image' => $this->faker->boolean(50) ? 'previews/' . $this->faker->uuid() . '.png' : null,
        ];
    }

    /**
     * Indicate that the template is custom type.
     */
    public function custom(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'custom',
        ]);
    }

    /**
     * Indicate that the template is legacy type.
     */
    public function legacy(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'legacy',
        ]);
    }

    /**
     * Indicate that the template is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the template is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the template is global (not company-specific).
     */
    public function global(): static
    {
        return $this->state(fn (array $attributes) => [
            'company_id' => null,
        ]);
    }

    /**
     * Indicate that the template belongs to a specific company.
     */
    public function forCompany(Company $company): static
    {
        return $this->state(fn (array $attributes) => [
            'company_id' => $company->id,
        ]);
    }

    /**
     * Create template with Legal page size.
     */
    public function legal(): static
    {
        return $this->state(function (array $attributes) {
            $templateData = $attributes['template_data'] ?? [];
            $templateData['page_size'] = 'legal';
            
            return [
                'template_data' => $templateData,
            ];
        });
    }

    /**
     * Create template with F4 page size.
     */
    public function f4(): static
    {
        return $this->state(function (array $attributes) {
            $templateData = $attributes['template_data'] ?? [];
            $templateData['page_size'] = 'f4';
            
            return [
                'template_data' => $templateData,
            ];
        });
    }
}
