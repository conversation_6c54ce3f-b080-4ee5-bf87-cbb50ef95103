<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_layouts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('version', ['v2'])->default('v2');
            $table->json('layout_data')->nullable();
            $table->json('settings')->nullable(); // Global template settings
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->string('page_size')->default('legal'); // legal, f4
            $table->string('orientation')->default('portrait');
            $table->string('font_family')->default('DejaVu Sans');
            $table->integer('font_size')->default(12);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('last_used_at')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['company_id', 'is_active']);
            $table->index(['version', 'is_active']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_layouts');
    }
};
