<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'companies';

    protected $fillable = [
        'name',
        'address',
        'phone',
        'email',
		'type',
		'business_id',

        'template',

        'templateheading',
        'templatebillto',
        'templatetable',
        'templateinword',
        'templatebankinfo',

        'logo',
		'signature',
		'signature_name',
		'bg_color',
		'font_id',
		'heading_size',
		'text_color',
		'footer_color',
		'can_replicate',
    ];

    public function order()
    {
        return $this->hasMany(Order::class, 'company_id', 'id');
    }

    public function invoice()
    {
        return $this->hasMany(Invoice::class, 'company_id', 'id');
    }

    public function client()
    {
        return $this->hasMany(Invoice::class, 'client_id', 'id');
    }

    public function businessType()
    {
        return $this->belongsTo(BusinessType::class, 'business_type', 'id');
    }

	public function banks()
    {
        return $this->hasMany(CompanyBank::class, 'company_id', 'id');
    }
}
