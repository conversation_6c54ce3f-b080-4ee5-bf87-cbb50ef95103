<?php

namespace App\Filament\Admin\Resources\TemplateLayoutResource\Pages;

use App\Filament\Admin\Resources\TemplateLayoutResource;
use App\Models\TemplateLayout;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateTemplateLayout extends CreateRecord
{
    protected static string $resource = TemplateLayoutResource::class;

    protected function getRedirectUrl(): string
    {
        // Redirect to visual builder after creating template
        return route('filament.admin.pages.visual-template-builder', [
            'template' => $this->record->id
        ]);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        $data['version'] = 'v2';
        
        return $data;
    }

    protected function afterCreate(): void
    {
        // Handle default template setting
        if ($this->record->is_default && $this->record->company_id) {
            // Remove default from other templates for this company
            TemplateLayout::where('company_id', $this->record->company_id)
                ->where('id', '!=', $this->record->id)
                ->update(['is_default' => false]);
        }

        Notification::make()
            ->title('Template Created')
            ->body('Template has been created successfully. You will be redirected to the Visual Builder.')
            ->success()
            ->send();
    }

    public function getTitle(): string
    {
        return 'Create New Template Layout';
    }
}
