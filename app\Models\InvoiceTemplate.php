<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'type',
        'is_active',
        'created_by',
        'company_id',
        'template_data',
        'preview_image',
    ];

    protected $casts = [
        'template_data' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this template
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the company this template belongs to
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the sections for this template
     */
    public function sections(): HasMany
    {
        return $this->hasMany(TemplateSection::class, 'template_id');
    }

    /**
     * Get the active sections for this template
     */
    public function activeSections(): HasMany
    {
        return $this->sections()->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Get the assets for this template
     */
    public function assets(): HasMany
    {
        return $this->hasMany(TemplateAsset::class, 'template_id');
    }

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for custom templates
     */
    public function scopeCustom($query)
    {
        return $query->where('type', 'custom');
    }

    /**
     * Scope for legacy templates
     */
    public function scopeLegacy($query)
    {
        return $query->where('type', 'legacy');
    }

    /**
     * Scope for global templates (not company-specific)
     */
    public function scopeGlobal($query)
    {
        return $query->whereNull('company_id');
    }

    /**
     * Scope for company-specific templates
     */
    public function scopeForCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Check if template is global
     */
    public function isGlobal(): bool
    {
        return is_null($this->company_id);
    }

    /**
     * Check if template is custom type
     */
    public function isCustom(): bool
    {
        return $this->type === 'custom';
    }

    /**
     * Check if template is legacy type
     */
    public function isLegacy(): bool
    {
        return $this->type === 'legacy';
    }
}
