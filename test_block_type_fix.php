<?php

/**
 * Test Block Type Fix
 * Run via: php artisan tinker
 */

echo "=== Testing Block Type Fix ===\n";

try {
    // 1. Test data for rendering
    echo "1. Creating test data...\n";
    
    $testData = [
        'company' => (object) [
            'name' => 'FAIR DEAL INTERNATIONAL INC',
            'logo' => 'company-logos/fair-deal-logo.png',
            'address' => 'Jl. Business Center No. 123<br>Jakarta Pusat 10220<br>Indonesia',
            'phone' => '+62 21 5555 1234',
            'email' => '<EMAIL>',
            'website' => 'www.fairdeal.co.id',
        ],
        'invoice' => (object) [
            'invoice_no' => 'INV-2024-001',
            'invoice_date' => '2024-01-20',
            'invoice_due' => '2024-02-20',
        ],
        'customer' => (object) [
            'name' => 'PT. Sample Client Corp',
            'address' => 'Jl. Client Avenue No. 456<br>Surabaya 60123<br>East Java',
        ]
    ];
    
    echo "   ✅ Test data created\n";
    
    // 2. Find existing blocks with company_logo type
    echo "\n2. Finding existing blocks with company_logo type...\n";
    
    $companyLogoBlocks = App\Models\ContentBlock::where('block_type', 'company_logo')->get();
    echo "   📊 Found {$companyLogoBlocks->count()} blocks with company_logo type\n";
    
    if ($companyLogoBlocks->count() > 0) {
        foreach ($companyLogoBlocks as $block) {
            echo "   📋 Block ID: {$block->id}, Section: {$block->layout_section_id}, Column: {$block->column_position}\n";
        }
    }
    
    // 3. Test rendering of company_logo block
    echo "\n3. Testing company_logo block rendering...\n";
    
    if ($companyLogoBlocks->count() > 0) {
        $testBlock = $companyLogoBlocks->first();
        
        try {
            $renderedHtml = $testBlock->renderHtml($testData);
            echo "   ✅ Successfully rendered company_logo block\n";
            echo "   📄 HTML: " . Str::limit(strip_tags($renderedHtml), 100) . "\n";
            
            // Check if it contains "Unknown block type"
            if (strpos($renderedHtml, 'Unknown block type') !== false) {
                echo "   ❌ Still showing 'Unknown block type' error\n";
            } else {
                echo "   ✅ No 'Unknown block type' error found\n";
            }
            
        } catch (\Exception $e) {
            echo "   ❌ Error rendering block: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ⚠️  No company_logo blocks found to test\n";
    }
    
    // 4. Test creating new blocks with different types
    echo "\n4. Testing creation of new block types...\n";
    
    $testBlockTypes = [
        'company_logo' => 'Company Logo (original)',
        'comp_logo' => 'Company Logo (short)',
        'comp_name' => 'Company Name (short)',
        'inv_number' => 'Invoice Number (short)',
        'cust_name' => 'Customer Name (short)',
    ];
    
    // Find a test section to use
    $testSection = App\Models\LayoutSection::first();
    
    if ($testSection) {
        echo "   📋 Using test section: {$testSection->id} ({$testSection->section_name})\n";
        
        foreach ($testBlockTypes as $blockType => $description) {
            try {
                // Create temporary block
                $tempBlock = App\Models\ContentBlock::create([
                    'layout_section_id' => $testSection->id,
                    'block_type' => $blockType,
                    'block_name' => $description,
                    'column_position' => 1,
                    'order' => 999,
                    'content_data' => [
                        'field_path' => $blockType === 'company_logo' || $blockType === 'comp_logo' ? 'company.logo' : 'company.name'
                    ]
                ]);
                
                // Test rendering
                $renderedHtml = $tempBlock->renderHtml($testData);
                
                if (strpos($renderedHtml, 'Unknown block type') !== false) {
                    echo "   ❌ {$description}: Still shows 'Unknown block type'\n";
                } else {
                    echo "   ✅ {$description}: Renders correctly\n";
                }
                
                // Clean up
                $tempBlock->delete();
                
            } catch (\Exception $e) {
                echo "   ❌ {$description}: Error - " . $e->getMessage() . "\n";
            }
        }
    } else {
        echo "   ❌ No test section found\n";
    }
    
    // 5. Check ContentBlock switch statement coverage
    echo "\n5. Checking ContentBlock switch statement coverage...\n";
    
    $contentBlockFile = file_get_contents(app_path('Models/ContentBlock.php'));
    
    $supportedTypes = [
        'company_logo' => strpos($contentBlockFile, "case 'company_logo':") !== false,
        'comp_logo' => strpos($contentBlockFile, "case 'comp_logo':") !== false,
        'comp_name' => strpos($contentBlockFile, "case 'comp_name':") !== false,
        'comp_address' => strpos($contentBlockFile, "case 'comp_address':") !== false,
        'comp_phone' => strpos($contentBlockFile, "case 'comp_phone':") !== false,
        'comp_email' => strpos($contentBlockFile, "case 'comp_email':") !== false,
        'inv_number' => strpos($contentBlockFile, "case 'inv_number':") !== false,
        'inv_date' => strpos($contentBlockFile, "case 'inv_date':") !== false,
        'inv_due' => strpos($contentBlockFile, "case 'inv_due':") !== false,
        'cust_name' => strpos($contentBlockFile, "case 'cust_name':") !== false,
        'cust_address' => strpos($contentBlockFile, "case 'cust_address':") !== false,
    ];
    
    foreach ($supportedTypes as $type => $isSupported) {
        if ($isSupported) {
            echo "   ✅ {$type}: Supported in ContentBlock\n";
        } else {
            echo "   ❌ {$type}: NOT supported in ContentBlock\n";
        }
    }
    
    $supportedCount = array_sum($supportedTypes);
    $totalCount = count($supportedTypes);
    echo "   📊 Coverage: {$supportedCount}/{$totalCount} block types supported\n";
    
    // 6. Test database schema
    echo "\n6. Testing database schema...\n";
    
    try {
        $blockTemplatesColumns = DB::select("DESCRIBE block_templates");
        $contentBlocksColumns = DB::select("DESCRIBE content_blocks");
        
        $blockTypeColumn = collect($blockTemplatesColumns)->firstWhere('Field', 'block_type');
        $contentBlockTypeColumn = collect($contentBlocksColumns)->firstWhere('Field', 'block_type');
        
        echo "   📊 block_templates.block_type: {$blockTypeColumn->Type}\n";
        echo "   📊 content_blocks.block_type: {$contentBlockTypeColumn->Type}\n";
        
        if (strpos($blockTypeColumn->Type, 'varchar') !== false && strpos($contentBlockTypeColumn->Type, 'varchar') !== false) {
            echo "   ✅ Database schema: Both columns are varchar (can handle long names)\n";
        } else {
            echo "   ❌ Database schema: Still using enum (limited names)\n";
        }
    } catch (\Exception $e) {
        echo "   ❌ Database schema check failed: " . $e->getMessage() . "\n";
    }
    
    // 7. Test creating block template with long name
    echo "\n7. Testing block template creation with long names...\n";
    
    try {
        $testTemplate = App\Models\BlockTemplate::create([
            'name' => 'Test Long Block Type Name',
            'description' => 'Testing long block type names',
            'block_type' => 'test_very_long_block_type_name_that_would_fail_with_enum',
            'category' => 'test',
            'is_system' => false,
            'is_active' => true,
        ]);
        
        echo "   ✅ Successfully created block template with long block_type name\n";
        echo "   📋 Block type: {$testTemplate->block_type}\n";
        
        // Clean up
        $testTemplate->delete();
        
    } catch (\Exception $e) {
        echo "   ❌ Failed to create block template with long name: " . $e->getMessage() . "\n";
    }
    
    // 8. Summary and recommendations
    echo "\n=== Block Type Fix Test Summary ===\n";
    
    if ($supportedCount === $totalCount) {
        echo "✅ All block types are supported in ContentBlock model\n";
    } else {
        echo "⚠️  Some block types are missing from ContentBlock model\n";
    }
    
    echo "📋 Recommendations:\n";
    echo "1. Refresh the page/template to see if 'Unknown block type' error is gone\n";
    echo "2. If error persists, check the exact block_type value in database\n";
    echo "3. Ensure ContentBlock model has all necessary case statements\n";
    echo "4. Test creating new individual components\n";
    
    echo "\n🔧 Next Steps:\n";
    echo "1. Run: include 'fix_database_and_create_components.php'\n";
    echo "2. Test Visual Builder with individual components\n";
    echo "3. Verify auto-save functionality\n";
    echo "4. Create custom layouts with individual fields\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
