<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Invoice Template Sections (Header, Body, Footer)
        Schema::create('invoice_template_sections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            $table->enum('section_type', ['header', 'body', 'footer'])->default('header');
            $table->string('section_name')->nullable();
            $table->integer('rows')->default(1); // Number of table rows
            $table->integer('columns')->default(1); // Number of table columns
            $table->json('table_structure')->nullable(); // Store complete table structure
            $table->json('section_settings')->nullable(); // Section-level settings
            $table->integer('order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['company_id', 'section_type', 'order']);
        });

        // Invoice Template Cells (Individual table cells)
        Schema::create('invoice_template_cells', function (Blueprint $table) {
            $table->id();
            $table->foreignId('section_id')->constrained('invoice_template_sections')->cascadeOnDelete();
            $table->integer('row_index'); // 0-based row position
            $table->integer('col_index'); // 0-based column position
            $table->integer('rowspan')->default(1); // How many rows this cell spans
            $table->integer('colspan')->default(1); // How many columns this cell spans
            
            // Content Configuration
            $table->enum('content_type', ['text', 'field', 'image'])->default('text');
            $table->string('field_path')->nullable(); // e.g., 'company.name', 'invoice_no'
            $table->text('static_content')->nullable(); // Free text content
            $table->json('field_config')->nullable(); // Field-specific configuration
            
            // Cell Styling
            $table->enum('text_align', ['left', 'center', 'right', 'justify'])->default('left');
            $table->enum('vertical_align', ['top', 'middle', 'bottom'])->default('top');
            $table->integer('font_size')->default(12);
            $table->enum('font_weight', ['normal', 'bold', 'lighter'])->default('normal');
            $table->string('font_color')->default('#000000');
            $table->string('background_color')->nullable();
            
            // Spacing & Border
            $table->json('padding')->nullable(); // {top, right, bottom, left}
            $table->json('margin')->nullable(); // {top, right, bottom, left}
            $table->json('border')->nullable(); // {width, style, color}
            
            // Image-specific settings
            $table->string('image_width')->nullable(); // e.g., '100px', 'auto'
            $table->string('image_height')->nullable(); // e.g., '100px', 'auto'
            $table->enum('image_fit', ['contain', 'cover', 'fill', 'scale-down'])->default('contain');
            
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['section_id', 'row_index', 'col_index']);
            $table->unique(['section_id', 'row_index', 'col_index'], 'unique_cell_position');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('invoice_template_cells');
        Schema::dropIfExists('invoice_template_sections');
    }
};
