<?php

/**
 * Create Combined Company Info Block Templates
 * Run via: php artisan tinker
 */

echo "=== Creating Combined Company Info Block Templates ===\n";

try {
    // 1. Create combined company contact block
    echo "1. Creating combined company contact block...\n";
    
    $combinedContactBlock = App\Models\BlockTemplate::updateOrCreate(
        ['block_type' => 'company_contact_combined'],
        [
            'name' => 'Company Contact Info (Combined)',
            'description' => 'Company address, phone, email, website, and fax in one block',
            'category' => 'company',
            'template_data' => [
                'fields' => [
                    'address' => [
                        'field_path' => 'company.address',
                        'label' => 'Address',
                        'format' => 'html',
                        'show_label' => false,
                        'order' => 1
                    ],
                    'phone' => [
                        'field_path' => 'company.phone',
                        'label' => 'Phone',
                        'format' => 'text',
                        'prefix' => 'Phone: ',
                        'show_label' => false,
                        'order' => 2
                    ],
                    'email' => [
                        'field_path' => 'company.email',
                        'label' => 'Email',
                        'format' => 'email',
                        'prefix' => 'Email: ',
                        'show_label' => false,
                        'order' => 3
                    ],
                    'website' => [
                        'field_path' => 'company.website',
                        'label' => 'Website',
                        'format' => 'url',
                        'prefix' => 'Website: ',
                        'show_label' => false,
                        'order' => 4
                    ],
                    'fax' => [
                        'field_path' => 'company.fax',
                        'label' => 'Fax',
                        'format' => 'text',
                        'prefix' => 'Fax: ',
                        'show_label' => false,
                        'order' => 5
                    ]
                ],
                'layout' => 'vertical',
                'spacing' => 'compact'
            ],
            'field_mappings' => [
                'fields' => ['company.address', 'company.phone', 'company.email', 'company.website', 'company.fax'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11,
                'line_height' => 1.4
            ],
            'preview_html' => '<div class="text-sm text-gray-600 space-y-1">
                <div>Jl. Sample Street No. 123<br>Jakarta Pusat 10220</div>
                <div>Phone: +62 21 1234 5678</div>
                <div>Email: <span class="text-blue-600"><EMAIL></span></div>
                <div>Website: <span class="text-blue-600">www.company.com</span></div>
                <div>Fax: +62 21 1234 5679</div>
            </div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'contact', 'combined', 'address', 'phone', 'email', 'website', 'fax'],
            'usage_count' => 0
        ]
    );
    
    echo "   ✅ Created: {$combinedContactBlock->name}\n";
    
    // 2. Create company header block (logo + name + tagline)
    echo "\n2. Creating company header block...\n";
    
    $companyHeaderBlock = App\Models\BlockTemplate::updateOrCreate(
        ['block_type' => 'company_header_combined'],
        [
            'name' => 'Company Header (Logo + Name)',
            'description' => 'Company logo and name side by side',
            'category' => 'company',
            'template_data' => [
                'layout' => 'horizontal',
                'logo' => [
                    'field_path' => 'company.logo',
                    'width' => '80px',
                    'height' => 'auto',
                    'position' => 'left'
                ],
                'name' => [
                    'field_path' => 'company.name',
                    'heading_size_path' => 'company.heading_size',
                    'text_color_path' => 'company.text_color',
                    'position' => 'right',
                    'vertical_align' => 'middle'
                ],
                'spacing' => '15px'
            ],
            'field_mappings' => [
                'fields' => ['company.logo', 'company.name'],
                'format' => 'header_layout'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'display' => 'flex',
                'align_items' => 'center'
            ],
            'preview_html' => '<div class="flex items-center space-x-4">
                <div class="w-16 h-12 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500 rounded">[LOGO]</div>
                <div class="text-lg font-bold text-gray-800">PT. Sample Company</div>
            </div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'header', 'logo', 'name', 'combined'],
            'usage_count' => 0
        ]
    );
    
    echo "   ✅ Created: {$companyHeaderBlock->name}\n";
    
    // 3. Create invoice info combined block
    echo "\n3. Creating invoice info combined block...\n";
    
    $invoiceInfoBlock = App\Models\BlockTemplate::updateOrCreate(
        ['block_type' => 'invoice_info_combined'],
        [
            'name' => 'Invoice Info (Combined)',
            'description' => 'Invoice number, date, due date, and amount in one block',
            'category' => 'invoice',
            'template_data' => [
                'fields' => [
                    'number' => [
                        'field_path' => 'invoice.invoice_no',
                        'label' => 'Invoice No',
                        'format' => 'text',
                        'prefix' => 'Invoice No: ',
                        'order' => 1
                    ],
                    'date' => [
                        'field_path' => 'invoice.invoice_date',
                        'label' => 'Invoice Date',
                        'format' => 'date',
                        'prefix' => 'Date: ',
                        'date_format' => 'd/m/Y',
                        'order' => 2
                    ],
                    'due_date' => [
                        'field_path' => 'invoice.invoice_due',
                        'label' => 'Due Date',
                        'format' => 'date',
                        'prefix' => 'Due: ',
                        'date_format' => 'd/m/Y',
                        'order' => 3
                    ],
                    'amount' => [
                        'field_path' => 'invoice.invoice_amount',
                        'label' => 'Total Amount',
                        'format' => 'currency',
                        'prefix' => 'Total: ',
                        'order' => 4,
                        'style' => 'bold'
                    ]
                ],
                'layout' => 'vertical',
                'alignment' => 'right'
            ],
            'field_mappings' => [
                'fields' => ['invoice.invoice_no', 'invoice.invoice_date', 'invoice.invoice_due', 'invoice.invoice_amount'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-right space-y-1">
                <div class="font-bold">Invoice No: INV-2024-001</div>
                <div>Date: 15/01/2024</div>
                <div>Due: 15/02/2024</div>
                <div class="font-bold text-lg">Total: Rp 1,500,000</div>
            </div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'info', 'combined', 'number', 'date', 'amount'],
            'usage_count' => 0
        ]
    );
    
    echo "   ✅ Created: {$invoiceInfoBlock->name}\n";
    
    // 4. Create customer info combined block
    echo "\n4. Creating customer info combined block...\n";
    
    $customerInfoBlock = App\Models\BlockTemplate::updateOrCreate(
        ['block_type' => 'customer_info_combined'],
        [
            'name' => 'Customer Info (Combined)',
            'description' => 'Customer name, address, phone, and email in one block',
            'category' => 'customer',
            'template_data' => [
                'fields' => [
                    'name' => [
                        'field_path' => 'customer.name',
                        'label' => 'Bill To',
                        'format' => 'text',
                        'prefix' => 'Bill To: ',
                        'order' => 1,
                        'style' => 'bold'
                    ],
                    'address' => [
                        'field_path' => 'customer.address',
                        'label' => 'Address',
                        'format' => 'html',
                        'order' => 2
                    ],
                    'phone' => [
                        'field_path' => 'customer.phone',
                        'label' => 'Phone',
                        'format' => 'text',
                        'prefix' => 'Phone: ',
                        'order' => 3
                    ],
                    'email' => [
                        'field_path' => 'customer.email',
                        'label' => 'Email',
                        'format' => 'email',
                        'prefix' => 'Email: ',
                        'order' => 4
                    ]
                ],
                'layout' => 'vertical'
            ],
            'field_mappings' => [
                'fields' => ['customer.name', 'customer.address', 'customer.phone', 'customer.email'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div class="space-y-1">
                <div class="font-bold">Bill To: Sample Client Corp</div>
                <div class="text-sm text-gray-600">Jl. Client Avenue No. 456<br>Surabaya 60123</div>
                <div class="text-sm text-gray-600">Phone: +62 31 7777 5678</div>
                <div class="text-sm text-gray-600">Email: <span class="text-blue-600"><EMAIL></span></div>
            </div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['customer', 'info', 'combined', 'billto', 'address', 'contact'],
            'usage_count' => 0
        ]
    );
    
    echo "   ✅ Created: {$customerInfoBlock->name}\n";
    
    // 5. Update ContentBlock model to handle combined blocks
    echo "\n5. Note: ContentBlock model needs to be updated to handle combined blocks\n";
    echo "   📝 Add cases for:\n";
    echo "      - company_contact_combined\n";
    echo "      - company_header_combined\n";
    echo "      - invoice_info_combined\n";
    echo "      - customer_info_combined\n";
    
    // 6. Summary
    echo "\n=== Combined Block Templates Created ===\n";
    
    $createdBlocks = [
        $combinedContactBlock,
        $companyHeaderBlock,
        $invoiceInfoBlock,
        $customerInfoBlock
    ];
    
    echo "📊 Total combined blocks created: " . count($createdBlocks) . "\n";
    
    foreach ($createdBlocks as $block) {
        echo "✅ {$block->name} (Category: {$block->category})\n";
    }
    
    echo "\n🎯 Benefits of Combined Blocks:\n";
    echo "✅ Faster template building - drag one block instead of multiple\n";
    echo "✅ Consistent spacing and alignment\n";
    echo "✅ Pre-configured field relationships\n";
    echo "✅ Professional layouts out of the box\n";
    
    echo "\n📋 Usage Examples:\n";
    echo "🏢 Company Contact Combined: All company contact info in one block\n";
    echo "🎨 Company Header Combined: Logo + name side by side\n";
    echo "📄 Invoice Info Combined: All invoice details in one block\n";
    echo "👤 Customer Info Combined: Complete customer information\n";
    
    echo "\n🚀 Next Steps:\n";
    echo "1. Update ContentBlock model to handle combined block types\n";
    echo "2. Test combined blocks in Visual Builder\n";
    echo "3. Create more combined blocks as needed\n";
    echo "4. Add collapsible categories in block palette\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
