<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Template Preview - {{ $template->name }}</title>
    
    <style>
        @page {
            size: {{ $pageSize === 'f4' ? 'A4' : 'legal' }};
            margin: 1cm;
        }

        body {
            font-family: '{{ $template->font_family ?? 'Arial' }}', sans-serif;
            font-size: {{ $template->font_size ?? 12 }}pt;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .template-container {
            width: 100%;
            max-width: 100%;
        }

        .layout-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .layout-table.section-header {
            margin-bottom: 30px;
        }

        .layout-table.section-body {
            margin-bottom: 20px;
        }

        .layout-table.section-footer {
            margin-top: 30px;
        }

        .table-cell {
            border: 1px solid #ddd;
            padding: 8px;
            vertical-align: top;
        }

        .table-cell.field-text {
            /* Text field styling */
        }

        .table-cell.field-image {
            text-align: center;
        }

        .table-cell.field-currency {
            text-align: right;
        }

        .table-cell.field-date {
            /* Date field styling */
        }

        .table-cell img {
            max-width: 100%;
            height: auto;
        }

        /* Preview mode specific styles */
        @media screen {
            body {
                background-color: #f5f5f5;
                padding: 20px;
            }

            .template-container {
                background-color: white;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                padding: 40px;
                max-width: {{ $previewMode === 'mobile' ? '400px' : '800px' }};
                margin: 0 auto;
            }

            .preview-header {
                background-color: #1f2937;
                color: white;
                padding: 15px 20px;
                border-radius: 8px 8px 0 0;
                margin: -20px -20px 20px -20px;
                display: flex;
                justify-content: between;
                align-items: center;
            }

            .preview-title {
                font-size: 18px;
                font-weight: bold;
            }

            .preview-info {
                font-size: 12px;
                opacity: 0.8;
            }
        }

        /* Print styles */
        @media print {
            body {
                background-color: white !important;
                padding: 0 !important;
            }

            .template-container {
                box-shadow: none !important;
                border-radius: 0 !important;
                padding: 0 !important;
                max-width: none !important;
                margin: 0 !important;
            }

            .preview-header {
                display: none !important;
            }

            .layout-table {
                page-break-inside: avoid;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .template-container {
                padding: 20px;
                margin: 10px;
                max-width: calc(100% - 20px);
            }

            .layout-table {
                font-size: 11pt;
            }

            .table-cell {
                padding: 6px;
            }
        }
    </style>
</head>
<body>
    <div class="template-container">
        @if($previewMode !== 'print')
            <div class="preview-header">
                <div>
                    <div class="preview-title">{{ $template->name }}</div>
                    <div class="preview-info">Table Layout Preview • {{ ucfirst($previewMode) }} Mode • {{ strtoupper($pageSize) }} Size</div>
                </div>
                <div>
                    <button onclick="window.print()" style="background: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        Print Preview
                    </button>
                </div>
            </div>
        @endif

        {{-- Render Table Sections --}}
        @foreach(['header', 'body', 'footer'] as $sectionType)
            @php
                $sections = $template->tableSections->where('section_type', $sectionType)->sortBy('order');
            @endphp
            
            @foreach($sections as $section)
                {!! $section->renderHtml($sampleData) !!}
            @endforeach
        @endforeach

        {{-- Fallback if no table sections exist --}}
        @if($template->tableSections->isEmpty())
            <div style="text-align: center; padding: 40px; color: #666; border: 2px dashed #ddd; border-radius: 8px;">
                <h3 style="margin: 0 0 10px 0; color: #999;">No Table Layout Found</h3>
                <p style="margin: 0; font-size: 14px;">
                    This template doesn't have any table sections yet.<br>
                    Use the Table Visual Builder to create your layout.
                </p>
                <div style="margin-top: 20px;">
                    <a href="/admin/table-visual-builder?template={{ $template->id }}" 
                       style="background: #10b981; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">
                        Open Table Builder
                    </a>
                </div>
            </div>
        @endif
    </div>

    <script>
        // Auto-refresh preview when in development
        @if(config('app.debug'))
            setTimeout(() => {
                if (document.hidden === false) {
                    // Only refresh if tab is active
                    location.reload();
                }
            }, 30000); // Refresh every 30 seconds
        @endif

        // Print functionality
        function printPreview() {
            window.print();
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printPreview();
            }
            if (e.key === 'F5') {
                e.preventDefault();
                location.reload();
            }
        });
    </script>
</body>
</html>
