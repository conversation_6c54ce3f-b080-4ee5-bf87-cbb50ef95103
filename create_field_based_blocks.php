<?php

/**
 * Create Field-Based Block Templates
 * Run via: php artisan tinker
 */

echo "=== Creating Field-Based Block Templates ===\n";

try {
    // 1. Clear existing system blocks
    echo "1. Clearing existing system blocks...\n";
    
    App\Models\BlockTemplate::where('is_system', true)->delete();
    echo "   ✅ Existing system blocks cleared\n";
    
    // 2. Create field-based block templates
    echo "\n2. Creating field-based block templates...\n";
    
    $fieldBasedBlocks = [
        // HEADER FIELDS
        [
            'name' => 'Company Logo',
            'description' => 'Company logo image',
            'block_type' => 'logo',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.logo',
                'alt_text' => 'Company Logo',
                'width' => '120px',
                'height' => 'auto'
            ],
            'field_mappings' => [
                'field' => 'company.logo',
                'format' => 'image'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'margin' => ['top' => 0, 'right' => 10, 'bottom' => 0, 'left' => 0]
            ],
            'preview_html' => '<div class="w-20 h-16 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500">[LOGO]</div>',
            'is_system' => true,
            'tags' => ['logo', 'company', 'header', 'image']
        ],
        [
            'name' => 'Company Name',
            'description' => 'Company name text',
            'block_type' => 'company_name',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.name',
                'prefix' => '',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 18,
                'font_weight' => 'bold',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="text-lg font-bold">PT. Sample Company</div>',
            'is_system' => true,
            'tags' => ['company', 'name', 'header', 'text']
        ],
        [
            'name' => 'Company Address',
            'description' => 'Company address text',
            'block_type' => 'company_address',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.address',
                'prefix' => '',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.address',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Sample Street No. 123, Jakarta 12345</div>',
            'is_system' => true,
            'tags' => ['company', 'address', 'header', 'text']
        ],
        [
            'name' => 'Company Phone',
            'description' => 'Company phone number',
            'block_type' => 'company_phone',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.phone',
                'prefix' => 'Phone: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.phone',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Phone: +62 21 1234 5678</div>',
            'is_system' => true,
            'tags' => ['company', 'phone', 'header', 'contact']
        ],
        [
            'name' => 'Company Email',
            'description' => 'Company email address',
            'block_type' => 'company_email',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.email',
                'prefix' => 'Email: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.email',
                'format' => 'email'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Email: <EMAIL></div>',
            'is_system' => true,
            'tags' => ['company', 'email', 'header', 'contact']
        ],
        
        // INVOICE DETAILS
        [
            'name' => 'Invoice Number',
            'description' => 'Invoice number field',
            'block_type' => 'invoice_number',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'invoice.number',
                'prefix' => 'Invoice #: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'invoice.number',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 14,
                'font_weight' => 'bold',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="text-right font-bold">Invoice #: INV-2024-001</div>',
            'is_system' => true,
            'tags' => ['invoice', 'number', 'header']
        ],
        [
            'name' => 'Invoice Date',
            'description' => 'Invoice date field',
            'block_type' => 'invoice_date',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'invoice.date',
                'prefix' => 'Date: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'invoice.date',
                'format' => 'date'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-right text-gray-600">Date: 01/01/2024</div>',
            'is_system' => true,
            'tags' => ['invoice', 'date', 'header']
        ],
        [
            'name' => 'Due Date',
            'description' => 'Invoice due date field',
            'block_type' => 'invoice_due_date',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'invoice.due_date',
                'prefix' => 'Due Date: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'invoice.due_date',
                'format' => 'date'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-right text-gray-600">Due Date: 31/01/2024</div>',
            'is_system' => true,
            'tags' => ['invoice', 'due_date', 'header']
        ],
        
        // CLIENT INFORMATION
        [
            'name' => 'Client Name',
            'description' => 'Client company name',
            'block_type' => 'client_name',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'client.name',
                'prefix' => 'Bill To: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'client.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 14,
                'font_weight' => 'bold',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="font-bold">Bill To: Sample Client Corp</div>',
            'is_system' => true,
            'tags' => ['client', 'name', 'header', 'billto']
        ],
        [
            'name' => 'Client Address',
            'description' => 'Client address',
            'block_type' => 'client_address',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'client.address',
                'prefix' => '',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'client.address',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Client Avenue No. 456, Surabaya 60123</div>',
            'is_system' => true,
            'tags' => ['client', 'address', 'header', 'billto']
        ],
        
        // SPACER/DIVIDER
        [
            'name' => 'Spacer',
            'description' => 'Empty space for layout',
            'block_type' => 'spacer',
            'category' => 'general',
            'template_data' => [
                'height' => '20px'
            ],
            'field_mappings' => [],
            'default_styling' => [
                'min_height' => 20
            ],
            'preview_html' => '<div class="h-5 border-b border-dashed border-gray-300"></div>',
            'is_system' => true,
            'tags' => ['spacer', 'layout', 'divider']
        ],
        [
            'name' => 'Custom Text',
            'description' => 'Custom text content',
            'block_type' => 'custom_text',
            'category' => 'general',
            'template_data' => [
                'text' => 'Custom text content'
            ],
            'field_mappings' => [],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div>Custom text content</div>',
            'is_system' => true,
            'tags' => ['text', 'custom', 'content']
        ]
    ];
    
    foreach ($fieldBasedBlocks as $blockData) {
        $block = App\Models\BlockTemplate::create($blockData);
        echo "   ✅ Created: {$block->name} ({$block->block_type})\n";
    }
    
    // 3. Verification
    echo "\n3. Verification...\n";
    
    $totalBlocks = App\Models\BlockTemplate::where('is_system', true)->count();
    $categories = App\Models\BlockTemplate::where('is_system', true)->distinct('category')->pluck('category');
    
    echo "   📊 Total field-based blocks: {$totalBlocks}\n";
    echo "   📦 Categories: " . $categories->implode(', ') . "\n";
    
    foreach ($categories as $category) {
        $count = App\Models\BlockTemplate::where('is_system', true)->where('category', $category)->count();
        echo "      - {$category}: {$count} blocks\n";
    }
    
    // 4. Update existing template with new blocks
    echo "\n4. Updating test template...\n";
    
    $testTemplate = App\Models\TemplateLayout::where('name', 'Test Visual Builder Template')->first();
    if ($testTemplate) {
        // Clear existing blocks
        App\Models\ContentBlock::whereIn('layout_section_id', 
            $testTemplate->sections->pluck('id')
        )->delete();
        
        $headerSection = $testTemplate->sections()->where('section_type', 'header')->first();
        if ($headerSection) {
            // Add field-based blocks to header
            $logoBlock = App\Models\BlockTemplate::where('block_type', 'logo')->first();
            $companyNameBlock = App\Models\BlockTemplate::where('block_type', 'company_name')->first();
            $invoiceNumberBlock = App\Models\BlockTemplate::where('block_type', 'invoice_number')->first();
            
            if ($logoBlock) {
                $logoBlock->createContentBlock($headerSection->id, ['column_position' => 1]);
                echo "   ✅ Logo block added to header column 1\n";
            }
            
            if ($companyNameBlock) {
                $companyNameBlock->createContentBlock($headerSection->id, ['column_position' => 2]);
                echo "   ✅ Company name block added to header column 2\n";
            }
            
            if ($invoiceNumberBlock) {
                $invoiceNumberBlock->createContentBlock($headerSection->id, ['column_position' => 3]);
                echo "   ✅ Invoice number block added to header column 3\n";
            }
        }
        
        echo "   ✅ Test template updated with field-based blocks\n";
    }
    
    echo "\n=== Field-Based Block Templates Created ===\n";
    echo "🎯 Now you have per-field blocks:\n";
    echo "   - Company Logo (separate)\n";
    echo "   - Company Name (separate)\n";
    echo "   - Company Address (separate)\n";
    echo "   - Company Phone (separate)\n";
    echo "   - Company Email (separate)\n";
    echo "   - Invoice Number (separate)\n";
    echo "   - Invoice Date (separate)\n";
    echo "   - Client Name (separate)\n";
    echo "   - Client Address (separate)\n";
    echo "   - Spacer & Custom Text\n";
    
    echo "\n🎨 Layout Examples Now Possible:\n";
    echo "   Case 1: Logo (col 1) + Company Info (col 2-4)\n";
    echo "   Case 2: Company Info (col 1-3) + Logo (col 4)\n";
    echo "   Case 3: Logo centered + Company Name + Address below\n";
    
    echo "\n🚀 Ready for flexible header layouts!\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
