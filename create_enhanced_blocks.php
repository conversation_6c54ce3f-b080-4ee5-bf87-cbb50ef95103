<?php

/**
 * Create Enhanced Block Templates with Multi-Field Support
 * Run via: php artisan tinker
 */

echo "=== Creating Enhanced Block Templates ===\n";

try {
    // 1. Clear existing system blocks
    echo "1. Clearing existing system blocks...\n";
    
    App\Models\BlockTemplate::where('is_system', true)->delete();
    echo "   ✅ Existing system blocks cleared\n";
    
    // 2. Create enhanced block templates
    echo "\n2. Creating enhanced block templates...\n";
    
    $enhancedBlocks = [
        // SINGLE FIELD BLOCKS
        [
            'name' => 'Company Logo',
            'description' => 'Company logo image',
            'block_type' => 'logo',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.logo',
                'alt_text' => 'Company Logo',
                'width' => '120px',
                'height' => 'auto'
            ],
            'field_mappings' => [
                'field' => 'company.logo',
                'format' => 'image'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'margin' => ['top' => 0, 'right' => 10, 'bottom' => 0, 'left' => 0]
            ],
            'preview_html' => '<div class="w-20 h-16 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500">[LOGO]</div>',
            'is_system' => true,
            'tags' => ['logo', 'company', 'header', 'image']
        ],
        [
            'name' => 'Company Name',
            'description' => 'Company name text',
            'block_type' => 'company_name',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.name',
                'prefix' => '',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 18,
                'font_weight' => 'bold',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="text-lg font-bold">PT. Sample Company</div>',
            'is_system' => true,
            'tags' => ['company', 'name', 'header', 'text']
        ],
        [
            'name' => 'Company Address',
            'description' => 'Company address text',
            'block_type' => 'company_address',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.address',
                'prefix' => '',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.address',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Sample Street No. 123, Jakarta 12345</div>',
            'is_system' => true,
            'tags' => ['company', 'address', 'header', 'text']
        ],
        [
            'name' => 'Company Phone',
            'description' => 'Company phone number',
            'block_type' => 'company_phone',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.phone',
                'prefix' => 'Phone: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.phone',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Phone: +62 21 1234 5678</div>',
            'is_system' => true,
            'tags' => ['company', 'phone', 'header', 'contact']
        ],
        [
            'name' => 'Company Email',
            'description' => 'Company email address',
            'block_type' => 'company_email',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.email',
                'prefix' => 'Email: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.email',
                'format' => 'email'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Email: <EMAIL></div>',
            'is_system' => true,
            'tags' => ['company', 'email', 'header', 'contact']
        ],
        
        // MULTI-FIELD BLOCKS (COMBINED)
        [
            'name' => 'Company Info Block',
            'description' => 'Combined company name, address, phone, email',
            'block_type' => 'company_info_combined',
            'category' => 'header',
            'template_data' => [
                'fields' => [
                    ['path' => 'company.name', 'format' => 'text', 'style' => 'font-weight: bold; font-size: 18px;'],
                    ['path' => 'company.address', 'format' => 'text', 'style' => 'font-size: 12px; color: #666;'],
                    ['path' => 'company.phone', 'format' => 'text', 'prefix' => 'Phone: ', 'style' => 'font-size: 12px; color: #666;'],
                    ['path' => 'company.email', 'format' => 'email', 'prefix' => 'Email: ', 'style' => 'font-size: 12px; color: #666;']
                ],
                'layout' => 'vertical'
            ],
            'field_mappings' => [
                'fields' => ['company.name', 'company.address', 'company.phone', 'company.email'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'line_height' => 1.4
            ],
            'preview_html' => '<div><div class="font-bold text-lg">PT. Sample Company</div><div class="text-sm text-gray-600">Jl. Sample Street No. 123</div><div class="text-sm text-gray-600">Phone: +62 21 1234 5678</div><div class="text-sm text-gray-600">Email: <EMAIL></div></div>',
            'is_system' => true,
            'tags' => ['company', 'info', 'header', 'combined']
        ],
        [
            'name' => 'Contact Info Block',
            'description' => 'Combined phone, email, website',
            'block_type' => 'contact_info_combined',
            'category' => 'header',
            'template_data' => [
                'fields' => [
                    ['path' => 'company.phone', 'format' => 'text', 'prefix' => 'Phone: '],
                    ['path' => 'company.email', 'format' => 'email', 'prefix' => 'Email: '],
                    ['path' => 'company.website', 'format' => 'url', 'prefix' => 'Web: ']
                ],
                'layout' => 'vertical'
            ],
            'field_mappings' => [
                'fields' => ['company.phone', 'company.email', 'company.website'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-sm"><div>Phone: +62 21 1234 5678</div><div>Email: <EMAIL></div><div>Web: www.company.com</div></div>',
            'is_system' => true,
            'tags' => ['contact', 'info', 'header', 'combined']
        ],
        
        // INVOICE FIELDS
        [
            'name' => 'Invoice Number',
            'description' => 'Invoice number field',
            'block_type' => 'invoice_number',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'invoice.number',
                'prefix' => 'Invoice #: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'invoice.number',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 14,
                'font_weight' => 'bold',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="text-right font-bold">Invoice #: INV-2024-001</div>',
            'is_system' => true,
            'tags' => ['invoice', 'number', 'header']
        ],
        [
            'name' => 'Invoice Details Block',
            'description' => 'Combined invoice number, date, due date',
            'block_type' => 'invoice_details_combined',
            'category' => 'header',
            'template_data' => [
                'fields' => [
                    ['path' => 'invoice.number', 'format' => 'text', 'prefix' => 'Invoice #: ', 'style' => 'font-weight: bold;'],
                    ['path' => 'invoice.date', 'format' => 'date', 'prefix' => 'Date: '],
                    ['path' => 'invoice.due_date', 'format' => 'date', 'prefix' => 'Due Date: ']
                ],
                'layout' => 'vertical'
            ],
            'field_mappings' => [
                'fields' => ['invoice.number', 'invoice.date', 'invoice.due_date'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12
            ],
            'preview_html' => '<div class="text-right text-sm"><div class="font-bold">Invoice #: INV-2024-001</div><div>Date: 01/01/2024</div><div>Due Date: 31/01/2024</div></div>',
            'is_system' => true,
            'tags' => ['invoice', 'details', 'header', 'combined']
        ],
        
        // CLIENT INFORMATION
        [
            'name' => 'Client Name',
            'description' => 'Client company name',
            'block_type' => 'client_name',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'client.name',
                'prefix' => 'Bill To: ',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'client.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 14,
                'font_weight' => 'bold',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="font-bold">Bill To: Sample Client Corp</div>',
            'is_system' => true,
            'tags' => ['client', 'name', 'header', 'billto']
        ],
        [
            'name' => 'Client Info Block',
            'description' => 'Combined client name and address',
            'block_type' => 'client_info_combined',
            'category' => 'header',
            'template_data' => [
                'fields' => [
                    ['path' => 'client.name', 'format' => 'text', 'prefix' => 'Bill To: ', 'style' => 'font-weight: bold;'],
                    ['path' => 'client.address', 'format' => 'text', 'style' => 'color: #666;']
                ],
                'layout' => 'vertical'
            ],
            'field_mappings' => [
                'fields' => ['client.name', 'client.address'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div><div class="font-bold">Bill To: Sample Client Corp</div><div class="text-gray-600">Jl. Client Avenue No. 456, Surabaya 60123</div></div>',
            'is_system' => true,
            'tags' => ['client', 'info', 'header', 'billto', 'combined']
        ],
        
        // UTILITY BLOCKS
        [
            'name' => 'Free Text Block',
            'description' => 'Customizable text content with full styling control',
            'block_type' => 'free_text',
            'category' => 'general',
            'template_data' => [
                'text' => 'Enter your custom text here',
                'allow_html' => true,
                'editable' => true
            ],
            'field_mappings' => [],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="border-2 border-dashed border-blue-300 p-2 text-blue-600 text-sm">✏️ Free Text Block - Click to edit</div>',
            'is_system' => true,
            'tags' => ['text', 'custom', 'editable', 'free']
        ],
        [
            'name' => 'Spacer',
            'description' => 'Empty space for layout',
            'block_type' => 'spacer',
            'category' => 'general',
            'template_data' => [
                'height' => '20px',
                'show_border' => false
            ],
            'field_mappings' => [],
            'default_styling' => [
                'min_height' => 20
            ],
            'preview_html' => '<div class="h-5 border-b border-dashed border-gray-300 flex items-center justify-center text-xs text-gray-400">--- spacer ---</div>',
            'is_system' => true,
            'tags' => ['spacer', 'layout', 'divider']
        ],
        [
            'name' => 'Horizontal Line',
            'description' => 'Horizontal divider line',
            'block_type' => 'horizontal_line',
            'category' => 'general',
            'template_data' => [
                'thickness' => '1px',
                'color' => '#cccccc',
                'style' => 'solid'
            ],
            'field_mappings' => [],
            'default_styling' => [
                'margin' => ['top' => 10, 'bottom' => 10]
            ],
            'preview_html' => '<hr class="border-gray-300">',
            'is_system' => true,
            'tags' => ['line', 'divider', 'separator']
        ]
    ];
    
    foreach ($enhancedBlocks as $blockData) {
        $block = App\Models\BlockTemplate::create($blockData);
        echo "   ✅ Created: {$block->name} ({$block->block_type})\n";
    }
    
    // 3. Verification
    echo "\n3. Verification...\n";
    
    $totalBlocks = App\Models\BlockTemplate::where('is_system', true)->count();
    $categories = App\Models\BlockTemplate::where('is_system', true)->distinct('category')->pluck('category');
    
    echo "   📊 Total enhanced blocks: {$totalBlocks}\n";
    echo "   📦 Categories: " . $categories->implode(', ') . "\n";
    
    foreach ($categories as $category) {
        $count = App\Models\BlockTemplate::where('is_system', true)->where('category', $category)->count();
        echo "      - {$category}: {$count} blocks\n";
    }
    
    // 4. Block types summary
    echo "\n4. Block Types Summary...\n";
    echo "   🔹 Single Field Blocks:\n";
    echo "      - Company Logo, Name, Address, Phone, Email\n";
    echo "      - Invoice Number, Date, Due Date\n";
    echo "      - Client Name, Address\n";
    echo "   🔹 Multi-Field Combined Blocks:\n";
    echo "      - Company Info Block (Name + Address + Phone + Email)\n";
    echo "      - Contact Info Block (Phone + Email + Website)\n";
    echo "      - Invoice Details Block (Number + Date + Due Date)\n";
    echo "      - Client Info Block (Name + Address)\n";
    echo "   🔹 Utility Blocks:\n";
    echo "      - Free Text Block (Fully editable)\n";
    echo "      - Spacer (Adjustable height)\n";
    echo "      - Horizontal Line (Divider)\n";
    
    echo "\n=== Enhanced Block Templates Created ===\n";
    echo "🎯 Now you have maximum flexibility:\n";
    echo "   ✅ Single fields for precise control\n";
    echo "   ✅ Combined blocks for quick layouts\n";
    echo "   ✅ Free text blocks for custom content\n";
    echo "   ✅ Utility blocks for spacing and dividers\n";
    
    echo "\n🎨 Layout Examples Now Possible:\n";
    echo "   Case 1: Logo + Company Info Block (all in one)\n";
    echo "   Case 2: Logo + Individual fields (precise control)\n";
    echo "   Case 3: Free text blocks for custom headers\n";
    echo "   Case 4: Mixed approach (some combined, some individual)\n";
    
    echo "\n🚀 Ready for any layout requirement!\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
