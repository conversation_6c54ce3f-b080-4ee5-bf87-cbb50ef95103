<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BlockTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'block_type',
        'category',
        'template_data',
        'default_styling',
        'field_mappings',
        'preview_image',
        'preview_html',
        'is_system',
        'is_active',
        'usage_count',
        'tags',
        'created_by',
    ];

    protected $casts = [
        'template_data' => 'array',
        'default_styling' => 'array',
        'field_mappings' => 'array',
        'is_system' => 'boolean',
        'is_active' => 'boolean',
        'usage_count' => 'integer',
        'tags' => 'array',
    ];

    protected $attributes = [
        'category' => 'general',
        'is_system' => false,
        'is_active' => true,
        'usage_count' => 0,
    ];

    // Relationships
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('block_type', $type);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    public function scopeUserCreated($query)
    {
        return $query->where('is_system', false);
    }

    public function scopePopular($query)
    {
        return $query->orderBy('usage_count', 'desc');
    }

    // Methods
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    public function createContentBlock(int $layoutSectionId, array $overrides = []): ContentBlock
    {
        $blockData = array_merge([
            'layout_section_id' => $layoutSectionId,
            'block_type' => $this->block_type,
            'block_name' => $this->name,
            'content_data' => $this->template_data,
            'styling' => $this->default_styling,
            'field_mapping' => $this->field_mappings,
        ], $overrides);

        $block = ContentBlock::create($blockData);
        $this->incrementUsage();

        return $block;
    }

    public function getPreviewHtml(): string
    {
        return $this->preview_html ?? $this->generatePreviewHtml();
    }

    private function generatePreviewHtml(): string
    {
        // Generate basic preview based on block type
        switch ($this->block_type) {
            case 'logo':
                return '<div class="preview-logo">[Company Logo]</div>';
            case 'text':
                return '<div class="preview-text">Sample Text Content</div>';
            case 'field':
                return '<div class="preview-field">[Dynamic Field]</div>';
            case 'table':
                return '<table class="preview-table"><tr><th>Column 1</th><th>Column 2</th></tr><tr><td>Data 1</td><td>Data 2</td></tr></table>';
            case 'company_info':
                return '<div class="preview-company"><strong>Company Name</strong><br>Company Address</div>';
            case 'client_info':
                return '<div class="preview-client"><strong>Bill To:</strong><br>Client Name<br>Client Address</div>';
            case 'invoice_details':
                return '<div class="preview-invoice"><strong>Invoice #:</strong> INV-001<br><strong>Date:</strong> 01/01/2024</div>';
            case 'bank_info':
                return '<div class="preview-bank"><strong>Bank Details:</strong><br>Bank Name<br>Account: **********</div>';
            case 'signature':
                return '<div class="preview-signature">[Signature Area]<br>Authorized Person</div>';
            case 'terms':
                return '<div class="preview-terms">Terms and conditions...</div>';
            case 'calculations':
                return '<div class="preview-calculations">Subtotal: Rp 100,000<br><strong>Total: Rp 110,000</strong></div>';
            default:
                return '<div class="preview-default">' . $this->name . '</div>';
        }
    }

    public static function createSystemTemplates(): void
    {
        $systemTemplates = [
            [
                'name' => 'Company Logo',
                'description' => 'Company logo with configurable size',
                'block_type' => 'logo',
                'category' => 'header',
                'template_data' => [
                    'logo_url' => '',
                    'alt_text' => 'Company Logo',
                    'width' => '150px',
                    'height' => 'auto'
                ],
                'default_styling' => [
                    'alignment' => 'center'
                ],
                'is_system' => true,
                'tags' => ['logo', 'company', 'header']
            ],
            [
                'name' => 'Company Information',
                'description' => 'Company name, address, and contact details',
                'block_type' => 'company_info',
                'category' => 'header',
                'template_data' => [],
                'field_mappings' => [
                    'fields' => ['name', 'address', 'phone', 'email']
                ],
                'default_styling' => [
                    'alignment' => 'left'
                ],
                'is_system' => true,
                'tags' => ['company', 'info', 'header']
            ],
            [
                'name' => 'Client Information',
                'description' => 'Bill to client details',
                'block_type' => 'client_info',
                'category' => 'header',
                'template_data' => [],
                'field_mappings' => [
                    'fields' => ['name', 'address', 'phone', 'email']
                ],
                'default_styling' => [
                    'alignment' => 'left'
                ],
                'is_system' => true,
                'tags' => ['client', 'bill to', 'header']
            ],
            [
                'name' => 'Invoice Details',
                'description' => 'Invoice number, date, and due date',
                'block_type' => 'invoice_details',
                'category' => 'header',
                'template_data' => [],
                'field_mappings' => [
                    'fields' => ['number', 'date', 'due_date']
                ],
                'default_styling' => [
                    'alignment' => 'right'
                ],
                'is_system' => true,
                'tags' => ['invoice', 'details', 'header']
            ],
            [
                'name' => 'Invoice Items Table',
                'description' => 'Table for invoice line items',
                'block_type' => 'table',
                'category' => 'body',
                'template_data' => [
                    'columns' => [
                        ['field' => 'description', 'label' => 'Description', 'width' => '40%'],
                        ['field' => 'quantity', 'label' => 'Qty', 'width' => '15%'],
                        ['field' => 'price', 'label' => 'Price', 'width' => '20%'],
                        ['field' => 'total', 'label' => 'Total', 'width' => '25%']
                    ]
                ],
                'default_styling' => [
                    'show_borders' => true,
                    'header_style' => 'bold'
                ],
                'is_system' => true,
                'tags' => ['table', 'items', 'body']
            ],
            [
                'name' => 'Calculations Summary',
                'description' => 'Subtotal, tax, and total calculations',
                'block_type' => 'calculations',
                'category' => 'body',
                'template_data' => [
                    'show_subtotal' => true,
                    'show_tax' => true,
                    'show_total' => true
                ],
                'default_styling' => [
                    'alignment' => 'right'
                ],
                'is_system' => true,
                'tags' => ['calculations', 'total', 'body']
            ],
            [
                'name' => 'Bank Information',
                'description' => 'Bank account details for payment',
                'block_type' => 'bank_info',
                'category' => 'footer',
                'template_data' => [
                    'layout' => 'simple'
                ],
                'field_mappings' => [
                    'fields' => ['name', 'account_number', 'account_name']
                ],
                'default_styling' => [
                    'alignment' => 'left'
                ],
                'is_system' => true,
                'tags' => ['bank', 'payment', 'footer']
            ],
            [
                'name' => 'Signature Block',
                'description' => 'Signature area with name',
                'block_type' => 'signature',
                'category' => 'footer',
                'template_data' => [
                    'signature_url' => '',
                    'signature_name' => 'Authorized Signature'
                ],
                'default_styling' => [
                    'alignment' => 'center'
                ],
                'is_system' => true,
                'tags' => ['signature', 'authorization', 'footer']
            ],
            [
                'name' => 'Terms & Conditions',
                'description' => 'Terms and conditions text',
                'block_type' => 'terms',
                'category' => 'footer',
                'template_data' => [
                    'terms' => 'Payment is due within 30 days of invoice date.'
                ],
                'default_styling' => [
                    'alignment' => 'left',
                    'font_size' => 10
                ],
                'is_system' => true,
                'tags' => ['terms', 'conditions', 'footer']
            ]
        ];

        foreach ($systemTemplates as $template) {
            static::updateOrCreate(
                ['name' => $template['name'], 'is_system' => true],
                $template
            );
        }
    }
}
