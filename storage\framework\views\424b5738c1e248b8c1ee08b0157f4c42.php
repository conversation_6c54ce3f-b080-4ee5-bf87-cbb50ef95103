
<div x-show="showCellEditor"
     x-transition:enter="transition ease-out duration-200"
     x-transition:enter-start="opacity-0 scale-95"
     x-transition:enter-end="opacity-100 scale-100"
     x-transition:leave="transition ease-in duration-150"
     x-transition:leave-start="opacity-100 scale-100"
     x-transition:leave-end="opacity-0 scale-95"
     class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-75"
     @click="if ($event.target === $event.currentTarget) showCellEditor = false"
     @keydown.escape.window="showCellEditor = false"
     x-trap.inert.noscroll="showCellEditor"
     style="display: none;"
     x-cloak>

    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden"
         @click.stop>
        
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Cell Editor</h3>
                <button type="button"
                        class="p-2 text-gray-400 hover:text-gray-600 rounded"
                        @click="showCellEditor = false">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                
                <div class="lg:col-span-2 space-y-6">
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Content Configuration</h4>
                        
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Content Type</label>
                            <select wire:model.live="cellEditor.content_type" 
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="text">Static Text</option>
                                <option value="field">Dynamic Field</option>
                                <option value="image">Image</option>
                            </select>
                        </div>

                        
                        <div x-show="$wire.cellEditor.content_type === 'field'" class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Select Field</label>
                            <select wire:model.live="cellEditor.field_path"
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">Select field...</option>
                                <optgroup label="Company Information">
                                    <option value="company.name">Company Name</option>
                                    <option value="company.address">Company Address</option>
                                    <option value="company.phone">Company Phone</option>
                                    <option value="company.email">Company Email</option>
                                </optgroup>
                                <optgroup label="Invoice Information">
                                    <option value="invoice_no">Invoice Number</option>
                                    <option value="invoice_date">Invoice Date</option>
                                    <option value="due_date">Due Date</option>
                                    <option value="currency">Currency</option>
                                    <option value="invoice_amount">Invoice Amount</option>
                                    <option value="amount_inword">Amount in Words</option>
                                </optgroup>
                                <optgroup label="Client Information">
                                    <option value="client.name">Client Name</option>
                                    <option value="client.address">Client Address</option>
                                    <option value="client.phone">Client Phone</option>
                                    <option value="client.email">Client Email</option>
                                </optgroup>
                                <optgroup label="Bank Information">
                                    <option value="bank.account_name">Bank Account Name</option>
                                    <option value="bank.bank_name">Bank Name</option>
                                    <option value="bank.account_number">Account Number</option>
                                    <option value="bank.swift">SWIFT Code</option>
                                </optgroup>
                            </select>
                        </div>

                        
                        <div x-show="$wire.cellEditor.content_type === 'text'" class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Text Content</label>
                            <textarea wire:model.live="cellEditor.static_content"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                      rows="4"
                                      placeholder="Enter your text content"></textarea>
                        </div>

                        
                        <div x-show="$wire.cellEditor.content_type === 'image'" class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Image Source</label>
                            <select wire:model.live="cellEditor.field_path"
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">Select image field...</option>
                                <option value="company.logo">Company Logo</option>
                                <option value="company.signature">Company Signature</option>
                            </select>
                        </div>
                    </div>

                    
                    <div x-show="$wire.cellEditor.content_type !== 'image'">
                        <h4 class="text-md font-medium text-gray-900 mb-4">Text Styling</h4>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
                                <input type="number"
                                       wire:model.live="cellEditor.font_size"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       min="8" max="72" placeholder="12">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Font Weight</label>
                                <select wire:model.live="cellEditor.font_weight"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="normal">Normal</option>
                                    <option value="bold">Bold</option>
                                    <option value="lighter">Light</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Text Align</label>
                                <select wire:model.live="cellEditor.text_align"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="left">Left</option>
                                    <option value="center">Center</option>
                                    <option value="right">Right</option>
                                    <option value="justify">Justify</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Vertical Align</label>
                                <select wire:model.live="cellEditor.vertical_align"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="top">Top</option>
                                    <option value="middle">Middle</option>
                                    <option value="bottom">Bottom</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Font Color</label>
                            <div class="flex items-center space-x-2">
                                <input type="color"
                                       wire:model.live="cellEditor.font_color"
                                       class="w-12 h-10 border border-gray-300 rounded-md">
                                <input type="text"
                                       wire:model.live="cellEditor.font_color"
                                       class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       placeholder="#000000">
                            </div>
                        </div>
                    </div>

                    
                    <div x-show="$wire.cellEditor.content_type === 'image'">
                        <h4 class="text-md font-medium text-gray-900 mb-4">Image Styling</h4>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Width</label>
                                <input type="text"
                                       wire:model.live="cellEditor.image_width"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       placeholder="auto, 100px, 50%">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Height</label>
                                <input type="text"
                                       wire:model.live="cellEditor.image_height"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       placeholder="auto, 100px, 50%">
                            </div>
                            
                            <div class="col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Object Fit</label>
                                <select wire:model.live="cellEditor.image_fit"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="contain">Contain</option>
                                    <option value="cover">Cover</option>
                                    <option value="fill">Fill</option>
                                    <option value="scale-down">Scale Down</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                
                <div class="space-y-6">
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Cell Controls</h4>
                        
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Split Cell</label>
                            <div class="grid grid-cols-2 gap-2">
                                <button type="button"
                                        class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                                        wire:click="splitCellHorizontal(<?php echo e($cellEditor['id'] ?? 0); ?>)">
                                    Split Horizontal
                                </button>
                                <button type="button"
                                        class="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                                        wire:click="splitCellVertical(<?php echo e($cellEditor['id'] ?? 0); ?>)">
                                    Split Vertical
                                </button>
                            </div>
                        </div>

                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
                            <div class="flex items-center space-x-2">
                                <input type="color"
                                       wire:model.live="cellEditor.background_color"
                                       class="w-12 h-10 border border-gray-300 rounded-md">
                                <input type="text"
                                       wire:model.live="cellEditor.background_color"
                                       class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       placeholder="transparent">
                            </div>
                        </div>

                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Padding (px)</label>
                            <div class="grid grid-cols-2 gap-2">
                                <input type="number"
                                       wire:model.live="cellEditor.padding.top"
                                       class="border border-gray-300 rounded-md px-2 py-1 text-sm"
                                       placeholder="Top" min="0">
                                <input type="number"
                                       wire:model.live="cellEditor.padding.right"
                                       class="border border-gray-300 rounded-md px-2 py-1 text-sm"
                                       placeholder="Right" min="0">
                                <input type="number"
                                       wire:model.live="cellEditor.padding.bottom"
                                       class="border border-gray-300 rounded-md px-2 py-1 text-sm"
                                       placeholder="Bottom" min="0">
                                <input type="number"
                                       wire:model.live="cellEditor.padding.left"
                                       class="border border-gray-300 rounded-md px-2 py-1 text-sm"
                                       placeholder="Left" min="0">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="p-4 border-t border-gray-200 bg-gray-50">
            <div class="flex justify-between items-center">
                <div class="text-xs text-gray-500">
                    Changes are saved automatically
                </div>
                <div class="flex space-x-2">
                    <button type="button"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm"
                            @click="showCellEditor = false">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\sites\starterkit\web_starter\resources\views/components/invoice-builder/cell-editor.blade.php ENDPATH**/ ?>