<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class GoogleFontsService
{
    private const API_URL = 'https://www.googleapis.com/webfonts/v1/webfonts';
    private const CACHE_KEY = 'google_fonts_list';
    private const CACHE_DURATION = 24 * 60 * 60; // 24 hours

    /**
     * Get all Google Fonts
     */
    public static function getAllFonts(): array
    {
        return Cache::remember(self::CACHE_KEY, self::CACHE_DURATION, function () {
            try {
                $response = Http::get(self::API_URL, [
                    'key' => config('services.google_fonts.api_key'),
                    'sort' => 'popularity'
                ]);

                if ($response->successful()) {
                    return $response->json('items', []);
                }

                return [];
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Google Fonts API error: ' . $e->getMessage());
                return [];
            }
        });
    }

    /**
     * Search Google Fonts
     */
    public static function searchFonts(string $query, int $limit = 20): array
    {
        $allFonts = self::getAllFonts();

        if (empty($query)) {
            return array_slice($allFonts, 0, $limit);
        }

        $filtered = array_filter($allFonts, function ($font) use ($query) {
            return stripos($font['family'], $query) !== false;
        });

        return array_slice($filtered, 0, $limit);
    }

    /**
     * Get popular fonts for select options (serif/sans-serif only)
     */
    public static function getPopularFonts(int $limit = 30): array
    {
        $fonts = self::getAllFonts();

        // Filter only serif and sans-serif fonts
        $filtered = array_filter($fonts, function ($font) {
            return in_array($font['category'], ['serif', 'sans-serif']);
        });

        // Get popular ones
        $popular = array_slice($filtered, 0, $limit);

        $options = [];
        foreach ($popular as $font) {
            $category = $font['category'] === 'serif' ? 'Serif' : 'Sans';
            $options[$font['family']] = $font['family'] . " ({$category} - Google)";
        }

        return $options;
    }

    /**
     * Get curated safe fonts for PDF (serif/sans-serif only)
     */
    public static function getSafeFonts(): array
    {
        // Curated list of fonts that work well in PDFs
        $safeFontNames = [
            // Popular Sans-Serif
            'Roboto',
            'Open Sans',
            'Lato',
            'Montserrat',
            'Source Sans Pro',
            'Nunito',
            'Ubuntu',
            'Raleway',
            'Poppins',
            'Inter',

            // Popular Serif
            'Playfair Display',
            'Merriweather',
            'Lora',
            'Source Serif Pro',
            'Crimson Text',
            'Libre Baskerville',
            'Cormorant Garamond',
            'EB Garamond',
        ];

        $allFonts = self::getAllFonts();
        $options = [];

        foreach ($safeFontNames as $fontName) {
            foreach ($allFonts as $font) {
                if ($font['family'] === $fontName && in_array($font['category'], ['serif', 'sans-serif'])) {
                    $category = $font['category'] === 'serif' ? 'Serif' : 'Sans';
                    $options[$font['family']] = $font['family'] . " ({$category} - Google)";
                    break;
                }
            }
        }

        return $options;
    }

    /**
     * Get font CSS URL for embedding
     */
    public static function getFontCssUrl(array $fontFamilies): string
    {
        $families = implode('|', array_map(function ($family) {
            return str_replace(' ', '+', $family);
        }, $fontFamilies));

        return "https://fonts.googleapis.com/css2?family={$families}&display=swap";
    }

    /**
     * Check if font is available in Google Fonts
     */
    public static function isFontAvailable(string $fontFamily): bool
    {
        $fonts = self::getAllFonts();

        foreach ($fonts as $font) {
            if ($font['family'] === $fontFamily) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get font details
     */
    public static function getFontDetails(string $fontFamily): ?array
    {
        $fonts = self::getAllFonts();

        foreach ($fonts as $font) {
            if ($font['family'] === $fontFamily) {
                return $font;
            }
        }

        return null;
    }

    /**
     * Get fonts by category
     */
    public static function getFontsByCategory(string $category): array
    {
        $fonts = self::getAllFonts();

        return array_filter($fonts, function ($font) use ($category) {
            return $font['category'] === $category;
        });
    }
}
