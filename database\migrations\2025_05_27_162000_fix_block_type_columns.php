<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix block_templates table
        Schema::table('block_templates', function (Blueprint $table) {
            // Drop the enum constraint and change to string
            $table->string('block_type', 100)->change();
        });

        // Fix content_blocks table
        Schema::table('content_blocks', function (Blueprint $table) {
            // Drop the enum constraint and change to string
            $table->string('block_type', 100)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to enum (if needed)
        Schema::table('block_templates', function (Blueprint $table) {
            $table->enum('block_type', [
                'logo', 'text', 'field', 'table', 'custom', 
                'company_info', 'client_info', 'invoice_details',
                'bank_info', 'signature', 'terms', 'calculations'
            ])->change();
        });

        Schema::table('content_blocks', function (Blueprint $table) {
            $table->enum('block_type', [
                'logo', 'text', 'field', 'table', 'custom', 
                'company_info', 'client_info', 'invoice_details',
                'bank_info', 'signature', 'terms', 'calculations'
            ])->change();
        });
    }
};
