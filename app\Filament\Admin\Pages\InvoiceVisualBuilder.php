<?php

namespace App\Filament\Admin\Pages;

use App\Models\Company;
use App\Models\InvoiceTemplateSection;
use App\Models\InvoiceTemplateCell;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Illuminate\Support\Facades\Log;

class InvoiceVisualBuilder extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-table-cells';
    protected static string $view = 'filament.admin.pages.invoice-visual-builder';
    protected static ?string $navigationLabel = 'Invoice Visual Builder';
    protected static ?string $title = 'Invoice Visual Builder';
    protected static ?string $navigationGroup = 'Template Management';
    protected static ?int $navigationSort = 2;

    // Properties
    public ?Company $company = null;
    public ?InvoiceTemplateSection $headerSection = null;
    public array $cellEditor = [];

    // Page state
    public string $previewMode = 'desktop';

    public function mount(): void
    {
        // Get company from URL parameter or first company
        $companyId = request()->get('company');
        
        if ($companyId) {
            $this->company = Company::with(['invoiceTemplateSections.cells'])->find($companyId);
        }
        
        if (!$this->company) {
            $this->company = Company::with(['invoiceTemplateSections.cells'])->first();
        }
        
        if (!$this->company) {
            // Create default company for demo
            $this->company = Company::create([
                'name' => 'Demo Company',
                'address' => 'Demo Address',
                'phone' => '021-*********',
                'email' => '<EMAIL>',
            ]);
        }

        // Load header section
        $this->loadHeaderSection();
    }

    private function loadHeaderSection(): void
    {
        $this->headerSection = $this->company->invoiceTemplateSections()
            ->where('section_type', 'header')
            ->with('cells')
            ->first();
    }

    public function createHeaderSection(): void
    {
        try {
            $this->headerSection = InvoiceTemplateSection::create([
                'company_id' => $this->company->id,
                'section_type' => 'header',
                'section_name' => 'Header Section',
                'rows' => 1,
                'columns' => 1,
                'order' => 0,
            ]);

            // Create default cell
            $this->headerSection->createDefaultCell(0, 0);
            $this->headerSection->updateTableStructure();

            $this->loadHeaderSection();

        } catch (\Exception $e) {
            Log::error('Failed to create header section: ' . $e->getMessage());
        }
    }

    public function addRow(): void
    {
        try {
            if ($this->headerSection) {
                $this->headerSection->addRow();
                $this->loadHeaderSection();
            }
        } catch (\Exception $e) {
            Log::error('Failed to add row: ' . $e->getMessage());
        }
    }

    public function addColumn(): void
    {
        try {
            if ($this->headerSection) {
                $this->headerSection->addColumn();
                $this->loadHeaderSection();
            }
        } catch (\Exception $e) {
            Log::error('Failed to add column: ' . $e->getMessage());
        }
    }

    public function mergeCells(array $cellIds): void
    {
        try {
            if (count($cellIds) < 2) {
                return;
            }

            // Get cells and find bounds
            $cells = InvoiceTemplateCell::whereIn('id', $cellIds)->get();
            
            if ($cells->isEmpty()) {
                return;
            }

            $minRow = $cells->min('row_index');
            $maxRow = $cells->max('row_index');
            $minCol = $cells->min('col_index');
            $maxCol = $cells->max('col_index');

            // Validate selection is rectangular
            $expectedCells = ($maxRow - $minRow + 1) * ($maxCol - $minCol + 1);
            if ($cells->count() !== $expectedCells) {
                Log::warning('Invalid cell selection for merge - not rectangular');
                return;
            }

            $this->headerSection->mergeCells($minRow, $minCol, $maxRow, $maxCol);
            $this->loadHeaderSection();

        } catch (\Exception $e) {
            Log::error('Failed to merge cells: ' . $e->getMessage());
        }
    }

    public function splitCell(int $row, int $col): void
    {
        try {
            if ($this->headerSection) {
                $this->headerSection->splitCell($row, $col);
                $this->loadHeaderSection();
            }
        } catch (\Exception $e) {
            Log::error('Failed to split cell: ' . $e->getMessage());
        }
    }

    public function loadCellEditor(int $cellId): void
    {
        try {
            $cell = InvoiceTemplateCell::find($cellId);
            
            if (!$cell) {
                return;
            }

            $this->cellEditor = [
                'id' => $cell->id,
                'content_type' => $cell->content_type,
                'field_path' => $cell->field_path,
                'static_content' => $cell->static_content,
                'text_align' => $cell->text_align,
                'vertical_align' => $cell->vertical_align,
                'font_size' => $cell->font_size,
                'font_weight' => $cell->font_weight,
                'font_color' => $cell->font_color,
                'background_color' => $cell->background_color,
                'image_width' => $cell->image_width,
                'image_height' => $cell->image_height,
                'image_fit' => $cell->image_fit,
            ];

        } catch (\Exception $e) {
            Log::error('Failed to load cell editor: ' . $e->getMessage());
        }
    }

    // Auto-save cell editor changes
    public function updatedCellEditor(): void
    {
        if (!isset($this->cellEditor['id'])) {
            return;
        }

        try {
            $cell = InvoiceTemplateCell::find($this->cellEditor['id']);
            
            if (!$cell) {
                return;
            }

            $updateData = [
                'content_type' => $this->cellEditor['content_type'] ?? 'text',
                'text_align' => $this->cellEditor['text_align'] ?? 'left',
                'vertical_align' => $this->cellEditor['vertical_align'] ?? 'top',
                'font_size' => (int) ($this->cellEditor['font_size'] ?? 12),
                'font_weight' => $this->cellEditor['font_weight'] ?? 'normal',
                'font_color' => $this->cellEditor['font_color'] ?? '#000000',
                'background_color' => $this->cellEditor['background_color'] ?? null,
                'image_width' => $this->cellEditor['image_width'] ?? null,
                'image_height' => $this->cellEditor['image_height'] ?? null,
                'image_fit' => $this->cellEditor['image_fit'] ?? 'contain',
            ];

            // Handle content based on content type
            switch ($this->cellEditor['content_type']) {
                case 'text':
                    $updateData['static_content'] = $this->cellEditor['static_content'] ?? '';
                    $updateData['field_path'] = null;
                    break;
                case 'field':
                case 'image':
                    $updateData['field_path'] = $this->cellEditor['field_path'] ?? null;
                    $updateData['static_content'] = null;
                    break;
            }

            $cell->update($updateData);

            // Refresh section
            $this->loadHeaderSection();

        } catch (\Exception $e) {
            Log::error('Failed to update cell: ' . $e->getMessage());
        }
    }

    public function previewTemplate(): void
    {
        if (!$this->company) {
            return;
        }

        // Generate preview URL
        $previewUrl = route('invoice-template.preview', [
            'company' => $this->company->id,
            'mode' => $this->previewMode,
            '_t' => time() // Cache buster
        ]);

        // Open preview in new tab
        $this->js("window.open('$previewUrl', '_blank')");
    }

    public function getTitle(): string
    {
        return 'Invoice Visual Builder' . ($this->company ? ' - ' . $this->company->name : '');
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('switch_company')
                ->label('Switch Company')
                ->icon('heroicon-o-building-office')
                ->action(function () {
                    // Implement company switching logic
                }),
            
            \Filament\Actions\Action::make('preview')
                ->label('Preview')
                ->icon('heroicon-o-eye')
                ->action('previewTemplate'),
        ];
    }
}
