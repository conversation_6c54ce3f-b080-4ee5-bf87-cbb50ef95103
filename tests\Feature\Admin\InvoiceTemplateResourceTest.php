<?php

namespace Tests\Feature\Admin;

use App\Filament\Admin\Resources\InvoiceTemplateResource;
use App\Models\Company;
use App\Models\InvoiceTemplate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class InvoiceTemplateResourceTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create();
        $this->actingAs($this->adminUser);
    }

    /** @test */
    public function it_can_render_invoice_template_list_page()
    {
        $this->get(InvoiceTemplateResource::getUrl('index'))
            ->assertSuccessful();
    }

    /** @test */
    public function it_can_list_invoice_templates()
    {
        $templates = InvoiceTemplate::factory()->count(3)->create();

        Livewire::test(InvoiceTemplateResource\Pages\ListInvoiceTemplates::class)
            ->assertCanSeeTableRecords($templates);
    }

    /** @test */
    public function it_can_render_create_page()
    {
        $this->get(InvoiceTemplateResource::getUrl('create'))
            ->assertSuccessful();
    }

    /** @test */
    public function it_can_create_invoice_template()
    {
        $company = Company::factory()->create();
        
        $newData = [
            'name' => 'Test Template',
            'description' => 'Test template description',
            'type' => 'custom',
            'company_id' => $company->id,
            'is_active' => true,
            'template_data.page_size' => 'legal',
            'template_data.orientation' => 'portrait',
            'template_data.font_family' => 'DejaVu Sans',
            'template_data.font_size' => 12,
            'template_data.colors.primary' => '#000000',
            'template_data.colors.secondary' => '#666666',
            'template_data.margins.top' => 15,
            'template_data.margins.right' => 15,
            'template_data.margins.bottom' => 15,
            'template_data.margins.left' => 15,
        ];

        Livewire::test(InvoiceTemplateResource\Pages\CreateInvoiceTemplate::class)
            ->fillForm($newData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('invoice_templates', [
            'name' => 'Test Template',
            'type' => 'custom',
            'company_id' => $company->id,
            'created_by' => $this->adminUser->id,
        ]);
    }

    /** @test */
    public function it_can_validate_required_fields()
    {
        Livewire::test(InvoiceTemplateResource\Pages\CreateInvoiceTemplate::class)
            ->fillForm([
                'name' => '',
                'type' => '',
            ])
            ->call('create')
            ->assertHasFormErrors(['name', 'type']);
    }

    /** @test */
    public function it_can_render_view_page()
    {
        $template = InvoiceTemplate::factory()->create();

        $this->get(InvoiceTemplateResource::getUrl('view', ['record' => $template]))
            ->assertSuccessful();
    }

    /** @test */
    public function it_can_render_edit_page()
    {
        $template = InvoiceTemplate::factory()->create();

        $this->get(InvoiceTemplateResource::getUrl('edit', ['record' => $template]))
            ->assertSuccessful();
    }

    /** @test */
    public function it_can_retrieve_data_for_editing()
    {
        $template = InvoiceTemplate::factory()->create([
            'name' => 'Original Template',
            'type' => 'custom',
        ]);

        Livewire::test(InvoiceTemplateResource\Pages\EditInvoiceTemplate::class, ['record' => $template->getRouteKey()])
            ->assertFormSet([
                'name' => 'Original Template',
                'type' => 'custom',
            ]);
    }

    /** @test */
    public function it_can_save_edited_template()
    {
        $template = InvoiceTemplate::factory()->create();
        $company = Company::factory()->create();

        $newData = [
            'name' => 'Updated Template',
            'description' => 'Updated description',
            'type' => 'legacy',
            'company_id' => $company->id,
        ];

        Livewire::test(InvoiceTemplateResource\Pages\EditInvoiceTemplate::class, ['record' => $template->getRouteKey()])
            ->fillForm($newData)
            ->call('save')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('invoice_templates', [
            'id' => $template->id,
            'name' => 'Updated Template',
            'type' => 'legacy',
            'company_id' => $company->id,
        ]);
    }

    /** @test */
    public function it_can_delete_template()
    {
        $template = InvoiceTemplate::factory()->create();

        Livewire::test(InvoiceTemplateResource\Pages\EditInvoiceTemplate::class, ['record' => $template->getRouteKey()])
            ->callAction('delete');

        $this->assertSoftDeleted('invoice_templates', [
            'id' => $template->id,
        ]);
    }

    /** @test */
    public function it_can_filter_templates_by_type()
    {
        $customTemplates = InvoiceTemplate::factory()->count(2)->create(['type' => 'custom']);
        $legacyTemplates = InvoiceTemplate::factory()->count(3)->create(['type' => 'legacy']);

        Livewire::test(InvoiceTemplateResource\Pages\ListInvoiceTemplates::class)
            ->filterTable('type', 'custom')
            ->assertCanSeeTableRecords($customTemplates)
            ->assertCanNotSeeTableRecords($legacyTemplates);
    }

    /** @test */
    public function it_can_filter_templates_by_company()
    {
        $company1 = Company::factory()->create();
        $company2 = Company::factory()->create();
        
        $company1Templates = InvoiceTemplate::factory()->count(2)->create(['company_id' => $company1->id]);
        $company2Templates = InvoiceTemplate::factory()->count(3)->create(['company_id' => $company2->id]);

        Livewire::test(InvoiceTemplateResource\Pages\ListInvoiceTemplates::class)
            ->filterTable('company_id', $company1->id)
            ->assertCanSeeTableRecords($company1Templates)
            ->assertCanNotSeeTableRecords($company2Templates);
    }

    /** @test */
    public function it_can_search_templates_by_name()
    {
        $template1 = InvoiceTemplate::factory()->create(['name' => 'Searchable Template']);
        $template2 = InvoiceTemplate::factory()->create(['name' => 'Another Template']);

        Livewire::test(InvoiceTemplateResource\Pages\ListInvoiceTemplates::class)
            ->searchTable('Searchable')
            ->assertCanSeeTableRecords([$template1])
            ->assertCanNotSeeTableRecords([$template2]);
    }
}
