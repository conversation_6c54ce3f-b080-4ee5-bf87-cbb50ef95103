<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\InvoiceTemplate;
use App\Models\TemplateComponent;
use App\Models\Company;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TemplateBuilderTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected InvoiceTemplate $template;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
        
        // Create test template
        $company = Company::factory()->create();
        $this->template = InvoiceTemplate::factory()->create([
            'name' => 'Test Template',
            'company_id' => $company->id,
            'created_by' => $this->user->id,
            'template_data' => [
                'page_size' => 'legal',
                'orientation' => 'portrait',
                'font_family' => 'DejaVu Sans',
                'sections' => [
                    'header' => [],
                    'body' => [],
                    'footer' => []
                ]
            ]
        ]);
    }

    /** @test */
    public function it_can_access_template_builder_page()
    {
        $response = $this->get(route('filament.admin.pages.template-builder'));
        
        $response->assertStatus(200);
        $response->assertSee('Template Builder');
    }

    /** @test */
    public function it_can_load_template_builder_with_template_parameter()
    {
        $response = $this->get(route('filament.admin.pages.template-builder') . '?template=' . $this->template->id);
        
        $response->assertStatus(200);
        $response->assertSee('Template Builder: ' . $this->template->name);
    }

    /** @test */
    public function it_displays_available_components()
    {
        // Create test components
        TemplateComponent::factory()->create([
            'name' => 'Test Kop Component',
            'type' => 'kop',
            'description' => 'Test kop description'
        ]);

        TemplateComponent::factory()->create([
            'name' => 'Test Table Component',
            'type' => 'table',
            'description' => 'Test table description'
        ]);

        $response = $this->get(route('filament.admin.pages.template-builder'));
        
        $response->assertStatus(200);
        $response->assertSee('Test Kop Component');
        $response->assertSee('Test Table Component');
    }

    /** @test */
    public function it_can_save_template_configuration()
    {
        $this->withoutExceptionHandling();
        
        // Simulate saving template via Livewire
        $newSections = [
            'header' => [
                [
                    'id' => 'test-component-1',
                    'component_id' => 1,
                    'name' => 'Test Header Component',
                    'type' => 'kop',
                    'config' => ['alignment' => 'center'],
                    'order' => 0
                ]
            ],
            'body' => [],
            'footer' => []
        ];

        // Update template data
        $this->template->update([
            'template_data' => array_merge($this->template->template_data, [
                'sections' => $newSections,
                'last_modified' => now()->toISOString()
            ])
        ]);

        $this->template->refresh();
        
        $this->assertEquals($newSections, $this->template->template_data['sections']);
        $this->assertArrayHasKey('last_modified', $this->template->template_data);
    }

    /** @test */
    public function it_can_access_template_preview()
    {
        $response = $this->get(route('template.preview', ['template' => $this->template->id]));
        
        $response->assertStatus(200);
        $response->assertSee('Template Preview');
    }

    /** @test */
    public function it_can_preview_template_with_custom_data()
    {
        $customData = [
            'sections' => [
                'header' => [
                    [
                        'id' => 'test-1',
                        'component_id' => 1,
                        'name' => 'Test Component',
                        'type' => 'kop'
                    ]
                ],
                'body' => [],
                'footer' => []
            ],
            'config' => [
                'page_size' => 'f4',
                'font_family' => 'Arial'
            ]
        ];

        $encodedData = base64_encode(json_encode($customData));
        
        $response = $this->get(route('template.preview') . '?data=' . $encodedData);
        
        $response->assertStatus(200);
        $response->assertSee('F4/Folio');
    }

    /** @test */
    public function it_shows_builder_action_in_template_list()
    {
        $response = $this->get(route('filament.admin.resources.invoice-templates.index'));
        
        $response->assertStatus(200);
        // Check if builder action is present (this might need adjustment based on Filament rendering)
    }

    /** @test */
    public function it_handles_empty_template_gracefully()
    {
        $emptyTemplate = InvoiceTemplate::factory()->create([
            'name' => 'Empty Template',
            'created_by' => $this->user->id,
            'template_data' => []
        ]);

        $response = $this->get(route('filament.admin.pages.template-builder') . '?template=' . $emptyTemplate->id);
        
        $response->assertStatus(200);
        $response->assertSee('Template Builder');
    }

    /** @test */
    public function it_loads_sample_data_for_preview()
    {
        $response = $this->get(route('template.preview', ['template' => $this->template->id]));
        
        $response->assertStatus(200);
        $response->assertSee('INV-2024-001'); // Sample invoice number
        $response->assertSee('PT. Sample Company'); // Sample company name
    }

    /** @test */
    public function it_handles_different_page_sizes()
    {
        // Test Legal page size
        $response = $this->get(route('template.preview', ['template' => $this->template->id]));
        $response->assertSee('Legal (8.5" × 14")');

        // Test F4 page size
        $this->template->update([
            'template_data' => array_merge($this->template->template_data, [
                'page_size' => 'f4'
            ])
        ]);

        $response = $this->get(route('template.preview', ['template' => $this->template->id]));
        $response->assertSee('F4/Folio (210mm × 330mm)');
    }

    /** @test */
    public function it_includes_font_loading_in_preview()
    {
        // Create a template with Google Font
        $this->template->update([
            'template_data' => array_merge($this->template->template_data, [
                'font_family' => 'Roboto'
            ])
        ]);

        $response = $this->get(route('template.preview', ['template' => $this->template->id]));
        
        $response->assertStatus(200);
        // Check if font is loaded (this might need adjustment based on actual font loading)
    }

    /** @test */
    public function it_validates_template_access_permissions()
    {
        // Create another user
        $otherUser = User::factory()->create();
        
        // Create template owned by other user
        $otherTemplate = InvoiceTemplate::factory()->create([
            'created_by' => $otherUser->id
        ]);

        // Current user should still be able to access (depending on business rules)
        $response = $this->get(route('filament.admin.pages.template-builder') . '?template=' . $otherTemplate->id);
        
        $response->assertStatus(200);
    }

    /** @test */
    public function it_handles_invalid_template_id_gracefully()
    {
        $response = $this->get(route('filament.admin.pages.template-builder') . '?template=99999');
        
        $response->assertStatus(200);
        $response->assertSee('Template Builder'); // Should show default builder
    }

    /** @test */
    public function it_can_change_page_size_in_builder()
    {
        $response = $this->get(route('filament.admin.pages.template-builder') . '?template=' . $this->template->id);
        
        $response->assertStatus(200);
        $response->assertSee('Legal (8.5" x 14")');
        $response->assertSee('F4/Folio (210mm x 330mm)');
    }

    /** @test */
    public function it_shows_component_categories()
    {
        // Create components in different categories
        TemplateComponent::factory()->create(['type' => 'kop']);
        TemplateComponent::factory()->create(['type' => 'billto']);
        TemplateComponent::factory()->create(['type' => 'table']);
        TemplateComponent::factory()->create(['type' => 'inwords']);
        TemplateComponent::factory()->create(['type' => 'bankinfo']);

        $response = $this->get(route('filament.admin.pages.template-builder'));
        
        $response->assertStatus(200);
        // Check if categories are displayed
        $response->assertSee('Components');
    }
}
