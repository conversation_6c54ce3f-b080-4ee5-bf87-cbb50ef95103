<?php

namespace Database\Factories;

use App\Models\InvoiceTemplate;
use App\Models\TemplateSection;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TemplateSection>
 */
class TemplateSectionFactory extends Factory
{
    protected $model = TemplateSection::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $sectionType = $this->faker->randomElement(['header', 'billto', 'table', 'inwords', 'footer', 'custom']);
        
        return [
            'template_id' => InvoiceTemplate::factory(),
            'section_type' => $sectionType,
            'section_name' => $this->getSectionName($sectionType),
            'section_config' => $this->getSectionConfig($sectionType),
            'sort_order' => $this->faker->numberBetween(1, 10),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Get section name based on type
     */
    private function getSectionName(string $type): string
    {
        return match($type) {
            'header' => 'Header Section',
            'billto' => 'Bill To Section',
            'table' => 'Invoice Table Section',
            'inwords' => 'Amount In Words Section',
            'footer' => 'Footer Section',
            'custom' => $this->faker->words(2, true) . ' Section',
            default => 'Unknown Section'
        };
    }

    /**
     * Get section configuration based on type
     */
    private function getSectionConfig(string $type): array
    {
        $baseConfig = [
            'height' => $this->faker->numberBetween(50, 200),
            'padding' => [
                'top' => $this->faker->numberBetween(5, 20),
                'right' => $this->faker->numberBetween(5, 20),
                'bottom' => $this->faker->numberBetween(5, 20),
                'left' => $this->faker->numberBetween(5, 20),
            ],
            'background_color' => $this->faker->boolean(30) ? $this->faker->hexColor() : null,
            'border' => $this->faker->boolean(40) ? [
                'width' => $this->faker->numberBetween(1, 3),
                'style' => $this->faker->randomElement(['solid', 'dashed', 'dotted']),
                'color' => $this->faker->hexColor()
            ] : null,
        ];

        return match($type) {
            'header' => array_merge($baseConfig, [
                'logo_position' => $this->faker->randomElement(['left', 'center', 'right']),
                'logo_size' => $this->faker->numberBetween(50, 150),
                'company_info_position' => $this->faker->randomElement(['left', 'center', 'right']),
                'show_logo' => $this->faker->boolean(80),
                'show_company_name' => $this->faker->boolean(95),
                'show_company_address' => $this->faker->boolean(85),
                'show_company_contact' => $this->faker->boolean(70),
            ]),
            
            'billto' => array_merge($baseConfig, [
                'layout' => $this->faker->randomElement(['left', 'right', 'justified']),
                'show_customer_name' => true,
                'show_customer_address' => $this->faker->boolean(90),
                'show_invoice_number' => true,
                'show_invoice_date' => true,
                'show_due_date' => $this->faker->boolean(85),
            ]),
            
            'table' => array_merge($baseConfig, [
                'columns' => $this->faker->randomElement([
                    ['Description', 'Total'],
                    ['Description', 'Price', 'Total'],
                    ['Description', 'Qty', 'Price', 'Total']
                ]),
                'show_header' => true,
                'show_footer' => true,
                'stripe_rows' => $this->faker->boolean(60),
                'row_height' => $this->faker->numberBetween(25, 40),
            ]),
            
            'inwords' => array_merge($baseConfig, [
                'alignment' => $this->faker->randomElement(['left', 'center', 'right']),
                'font_style' => $this->faker->randomElement(['normal', 'italic', 'bold']),
                'prefix_text' => 'In words: ',
            ]),
            
            'footer' => array_merge($baseConfig, [
                'show_bank_info' => $this->faker->boolean(90),
                'show_signature' => $this->faker->boolean(80),
                'show_remarks' => $this->faker->boolean(70),
                'bank_info_layout' => $this->faker->randomElement(['model-1-bg-border', 'model-2-border', 'model-3-nobg-noborder']),
                'signature_position' => $this->faker->randomElement(['left', 'center', 'right']),
            ]),
            
            'custom' => array_merge($baseConfig, [
                'content_type' => $this->faker->randomElement(['text', 'html', 'image']),
                'content' => $this->faker->paragraph(),
            ]),
            
            default => $baseConfig
        };
    }

    /**
     * Indicate that the section is header type.
     */
    public function header(): static
    {
        return $this->state(fn (array $attributes) => [
            'section_type' => 'header',
            'section_name' => 'Header Section',
            'sort_order' => 1,
        ]);
    }

    /**
     * Indicate that the section is billto type.
     */
    public function billto(): static
    {
        return $this->state(fn (array $attributes) => [
            'section_type' => 'billto',
            'section_name' => 'Bill To Section',
            'sort_order' => 2,
        ]);
    }

    /**
     * Indicate that the section is table type.
     */
    public function table(): static
    {
        return $this->state(fn (array $attributes) => [
            'section_type' => 'table',
            'section_name' => 'Invoice Table Section',
            'sort_order' => 3,
        ]);
    }

    /**
     * Indicate that the section is inwords type.
     */
    public function inwords(): static
    {
        return $this->state(fn (array $attributes) => [
            'section_type' => 'inwords',
            'section_name' => 'Amount In Words Section',
            'sort_order' => 4,
        ]);
    }

    /**
     * Indicate that the section is footer type.
     */
    public function footer(): static
    {
        return $this->state(fn (array $attributes) => [
            'section_type' => 'footer',
            'section_name' => 'Footer Section',
            'sort_order' => 5,
        ]);
    }

    /**
     * Indicate that the section is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the section is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
