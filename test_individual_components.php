<?php

/**
 * Test Individual Field Components
 * Run via: php artisan tinker
 */

echo "=== Testing Individual Field Components ===\n";

try {
    // 1. Create individual components
    echo "1. Creating individual field components...\n";
    include 'create_individual_field_components.php';
    
    echo "\n" . str_repeat("=", 60) . "\n";
    
    // 2. Create test template with individual components
    echo "2. Creating test template with individual components...\n";
    
    $individualTemplate = App\Models\TemplateLayout::updateOrCreate(
        ['name' => 'Individual Components Test'],
        [
            'description' => 'Template showcasing individual field components',
            'company_id' => null,
            'version' => 'v2',
            'page_size' => 'legal',
            'orientation' => 'portrait',
            'font_family' => 'DejaVu Sans',
            'font_size' => 12,
            'is_active' => true,
            'is_default' => false,
            'created_by' => 1,
        ]
    );
    
    // Clear existing sections
    App\Models\LayoutSection::where('template_layout_id', $individualTemplate->id)->delete();
    
    // Create sections to showcase different layouts
    $headerSection = App\Models\LayoutSection::create([
        'template_layout_id' => $individualTemplate->id,
        'section_type' => 'header',
        'section_name' => 'Company Header (Individual Fields)',
        'column_count' => 4,
        'column_layout' => 'custom',
        'column_widths' => [15, 35, 35, 15],
        'order' => 0,
        'is_active' => true,
        'min_height' => 120,
    ]);
    
    $contactSection = App\Models\LayoutSection::create([
        'template_layout_id' => $individualTemplate->id,
        'section_type' => 'body',
        'section_name' => 'Contact Information',
        'column_count' => 3,
        'column_layout' => 'equal',
        'column_widths' => null,
        'order' => 1,
        'is_active' => true,
        'min_height' => 80,
    ]);
    
    $invoiceSection = App\Models\LayoutSection::create([
        'template_layout_id' => $individualTemplate->id,
        'section_type' => 'body',
        'section_name' => 'Invoice Details',
        'column_count' => 2,
        'column_layout' => 'custom',
        'column_widths' => [60, 40],
        'order' => 2,
        'is_active' => true,
        'min_height' => 100,
    ]);
    
    $signatureSection = App\Models\LayoutSection::create([
        'template_layout_id' => $individualTemplate->id,
        'section_type' => 'footer',
        'section_name' => 'Signature Area',
        'column_count' => 2,
        'column_layout' => 'custom',
        'column_widths' => [70, 30],
        'order' => 3,
        'is_active' => true,
        'min_height' => 100,
    ]);
    
    echo "   ✅ Template: {$individualTemplate->name} (ID: {$individualTemplate->id})\n";
    echo "   ✅ Header Section: {$headerSection->id} (4 columns)\n";
    echo "   ✅ Contact Section: {$contactSection->id} (3 columns)\n";
    echo "   ✅ Invoice Section: {$invoiceSection->id} (2 columns)\n";
    echo "   ✅ Signature Section: {$signatureSection->id} (2 columns)\n";
    
    // 3. Add individual components to sections
    echo "\n3. Adding individual components to sections...\n";
    
    // Header Section: Logo + Company Name + Company Address + Invoice Number
    $components = [
        'company_logo' => [$headerSection->id, 1],
        'company_name' => [$headerSection->id, 2],
        'company_address' => [$headerSection->id, 3],
        'invoice_number' => [$headerSection->id, 4],
    ];
    
    // Contact Section: Phone + Email + Website
    $contactComponents = [
        'company_phone' => [$contactSection->id, 1],
        'company_email' => [$contactSection->id, 2],
        'company_website' => [$contactSection->id, 3],
    ];
    
    // Invoice Section: Customer Info + Invoice Details
    $invoiceComponents = [
        'customer_name' => [$invoiceSection->id, 1],
        'customer_address' => [$invoiceSection->id, 1],
        'invoice_date' => [$invoiceSection->id, 2],
        'invoice_due_date' => [$invoiceSection->id, 2],
        'invoice_amount' => [$invoiceSection->id, 2],
    ];
    
    // Signature Section: Free Text + Signature
    $signatureComponents = [
        'free_text' => [$signatureSection->id, 1],
        'company_signature_image' => [$signatureSection->id, 2],
        'signature_name' => [$signatureSection->id, 2],
    ];
    
    $allComponents = array_merge($components, $contactComponents, $invoiceComponents, $signatureComponents);
    
    foreach ($allComponents as $blockType => [$sectionId, $columnPosition]) {
        $blockTemplate = App\Models\BlockTemplate::where('block_type', $blockType)->first();
        
        if ($blockTemplate) {
            $contentBlock = $blockTemplate->createContentBlock($sectionId, [
                'column_position' => $columnPosition,
                'order' => 0
            ]);
            echo "   ✅ Added {$blockTemplate->name} to section {$sectionId}, column {$columnPosition}\n";
        } else {
            echo "   ❌ Block template not found: {$blockType}\n";
        }
    }
    
    // 4. Create comprehensive test data
    echo "\n4. Creating comprehensive test data...\n";
    
    $testData = [
        'company' => (object) [
            'name' => 'PT. Individual Components Demo',
            'logo' => 'company-logos/demo-logo.png',
            'address' => 'Jl. Component Street No. 123<br>Jakarta Pusat 10220<br>Indonesia',
            'phone' => '+62 21 5555 1234',
            'email' => '<EMAIL>',
            'website' => 'www.components.co.id',
            'fax' => '+62 21 5555 1235',
            'signature' => 'signatures/demo-signature.png',
            'signature_name' => 'Jane Smith',
            'heading_size' => 'h2',
            'text_color' => '#1e40af',
        ],
        'invoice' => (object) [
            'invoice_no' => 'INV-COMP-2024-001',
            'invoice_date' => '2024-01-20',
            'invoice_due' => '2024-02-20',
            'invoice_amount' => 2750000,
            'remarks' => 'Individual components showcase - flexible layout design',
            'currency' => (object) [
                'symbol' => 'Rp'
            ]
        ],
        'customer' => (object) [
            'name' => 'PT. Client Individual Corp',
            'address' => 'Jl. Client Individual No. 789<br>Bandung 40123<br>West Java, Indonesia',
            'phone' => '+62 22 8888 9999',
            'email' => '<EMAIL>'
        ]
    ];
    
    echo "   ✅ Test data created with all individual fields\n";
    
    // 5. Test content rendering for each component type
    echo "\n5. Testing content rendering for individual components...\n";
    
    $componentCategories = [
        'company' => ['company_logo', 'company_name', 'company_address', 'company_phone', 'company_email', 'company_website', 'company_fax'],
        'invoice' => ['invoice_number', 'invoice_date', 'invoice_due_date', 'invoice_amount', 'invoice_remarks'],
        'customer' => ['customer_name', 'customer_address', 'customer_phone', 'customer_email'],
        'signature' => ['company_signature_image', 'signature_name'],
        'utility' => ['free_text', 'spacer', 'horizontal_line']
    ];
    
    foreach ($componentCategories as $category => $blockTypes) {
        echo "   🧪 Testing {$category} components:\n";
        
        foreach ($blockTypes as $blockType) {
            $blockTemplate = App\Models\BlockTemplate::where('block_type', $blockType)->first();
            
            if ($blockTemplate) {
                // Create temporary content block for testing
                $tempSection = $headerSection; // Use any section for testing
                $contentBlock = $blockTemplate->createContentBlock($tempSection->id, [
                    'column_position' => 1,
                    'order' => 999 // High order to avoid conflicts
                ]);
                
                try {
                    $renderedHtml = $contentBlock->renderHtml($testData);
                    $cleanText = strip_tags($renderedHtml);
                    $preview = Str::limit($cleanText, 40);
                    echo "      ✅ {$blockTemplate->name}: {$preview}\n";
                } catch (\Exception $e) {
                    echo "      ❌ {$blockTemplate->name}: Error - {$e->getMessage()}\n";
                }
                
                // Clean up temporary block
                $contentBlock->delete();
            } else {
                echo "      ❌ {$blockType}: Template not found\n";
            }
        }
    }
    
    // 6. Test component flexibility scenarios
    echo "\n6. Testing component flexibility scenarios...\n";
    
    $flexibilityTests = [
        'Minimal Layout' => ['company_logo', 'company_name', 'invoice_number'],
        'Contact Heavy' => ['company_phone', 'company_email', 'company_website', 'company_fax', 'customer_phone', 'customer_email'],
        'Invoice Focus' => ['invoice_number', 'invoice_date', 'invoice_due_date', 'invoice_amount', 'invoice_remarks'],
        'Custom Mix' => ['free_text', 'company_name', 'spacer', 'customer_name', 'horizontal_line', 'signature_name']
    ];
    
    foreach ($flexibilityTests as $scenario => $blockTypes) {
        echo "   📋 Scenario: {$scenario}\n";
        echo "      Components: " . implode(', ', $blockTypes) . "\n";
        
        $availableComponents = 0;
        foreach ($blockTypes as $blockType) {
            if (App\Models\BlockTemplate::where('block_type', $blockType)->exists()) {
                $availableComponents++;
            }
        }
        
        $totalComponents = count($blockTypes);
        echo "      Availability: {$availableComponents}/{$totalComponents} components available\n";
        
        if ($availableComponents === $totalComponents) {
            echo "      ✅ Scenario fully supported\n";
        } else {
            echo "      ⚠️  Some components missing\n";
        }
    }
    
    // 7. Generate Visual Builder URL
    echo "\n7. Visual Builder URL for testing...\n";
    
    $builderUrl = "/admin/visual-template-builder?template={$individualTemplate->id}";
    echo "   🎨 URL: {$builderUrl}\n";
    
    // 8. Component statistics
    echo "\n8. Component statistics...\n";
    
    $totalComponents = App\Models\BlockTemplate::where('is_system', true)->count();
    $categories = App\Models\BlockTemplate::where('is_system', true)
        ->distinct('category')
        ->pluck('category');
    
    echo "   📊 Total individual components: {$totalComponents}\n";
    echo "   📦 Categories: " . $categories->implode(', ') . "\n";
    
    foreach ($categories as $category) {
        $count = App\Models\BlockTemplate::where('is_system', true)
            ->where('category', $category)
            ->count();
        echo "      - {$category}: {$count} components\n";
    }
    
    // 9. Usage instructions
    echo "\n=== Individual Components Usage Instructions ===\n";
    echo "📋 How to Use Individual Components:\n";
    echo "1. Open Visual Builder: {$builderUrl}\n";
    echo "2. Components are now organized by category in the palette\n";
    echo "3. Drag individual fields exactly where you need them\n";
    echo "4. Mix and match fields from different categories\n";
    echo "5. Create custom layouts with precise field placement\n";
    
    echo "\n🎯 Component Categories:\n";
    echo "✅ Company: Logo, Name, Address, Phone, Email, Website, Fax\n";
    echo "✅ Invoice: Number, Date, Due Date, Amount, Remarks\n";
    echo "✅ Customer: Name, Address, Phone, Email\n";
    echo "✅ Signature: Image, Name (separate components)\n";
    echo "✅ Utility: Free Text, Spacer, Horizontal Line\n";
    
    echo "\n🎨 Layout Examples You Can Create:\n";
    echo "📋 Example 1: Logo + Name only (minimal)\n";
    echo "📋 Example 2: All company contact fields (comprehensive)\n";
    echo "📋 Example 3: Invoice fields only (focused)\n";
    echo "📋 Example 4: Custom mix with free text and spacers\n";
    echo "📋 Example 5: Signature image and name separately positioned\n";
    
    echo "\n🚀 Benefits of Individual Components:\n";
    echo "✅ Maximum flexibility - use only what you need\n";
    echo "✅ Precise positioning - place each field exactly where wanted\n";
    echo "✅ Custom layouts - create unique designs\n";
    echo "✅ Easy maintenance - modify individual fields without affecting others\n";
    echo "✅ Scalable - add new fields easily\n";
    
    echo "\n=== Individual Components System Complete ===\n";
    echo "🎉 Ready for maximum layout flexibility!\n";
    echo "🚀 Test URL: {$builderUrl}\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
