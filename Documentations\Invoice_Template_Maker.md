# Invoice Template Maker

## Batasan
1. backward compatibility dengan template invoice yang sudah ada. ini berarti template invoice yang sudah ada tidak perlu diubah, setiap perusahaan yang sudah memiliki template, tetap dapat menggunakan template invoice yang sudah ada sebelumnya atau membuat template baru jika ingin melalui tool ini.
2. mudah untuk diintegrasikan dengan filament builder.
3. mudah digunakan oleh user yang tidak terlalu teknis/awam.
4. Pengujian dilakukan setiap kali fungsi atau metode baru dibuat. jika tidak memungkinkan, maka pengujian dilakukan setelah semua fungsi atau metode dibuat dalam satu fitur.

## Fitur yang ingin dikembangkan.
Setiap kali Staff invoice mendapatkan perusahaan baru, mereka harus membuat template invoice baru untuk perusahaan tersebut. yang selama ini dibuat menggunakan blade html secara manual oleh developer. ini tentu akan sangat mengham<PERSON> karena proses pembuatan secara manual akan sangat lambat dan tidak efisien.

## Tujuan
menyediakan tool untuk membuat template invoice secara otomatis berdasarkan inputan user. editable deletable replicable.
component yang dibutuhkan dalam template invoice:
- kop surat
- company dan bill to
- table
- inword
- footer

desain dan tata letak yang sangat dinamis. dapat dibatasi dengan delivered section, misal:
Section Header = Kop surat, tata letak dan design yang sangat dinamis
Section Company = Company dan bill to, tata letak dan design yang sangat dinamis
Section Body Message = Message, tata letak dan design yang sangat dinamis.
Section Table = Table, tata letak dan design yang sangat dinamis
Section Inword = Inword, tata letak dan design yang sangat dinamis
Section Footer = Footer, tata letak dan design yang sangat dinamis

---

# Rencana Implementasi Invoice Template Maker

## 🆕 Phase 0: Visual Builder v2.0 Foundation (NEW)

### Step 0.1: New Database Schema v2.0
- [ ] **Create template_layouts table** (v2.0 main table)
  - [ ] id, name, company_id, version ('v2')
  - [ ] layout_data (JSON), is_active, created_at, updated_at
- [ ] **Create layout_sections table** (section management)
  - [ ] id, template_layout_id, section_type (header/body/footer)
  - [ ] column_count, settings (JSON), order, created_at
- [ ] **Create content_blocks table** (content elements)
  - [ ] id, layout_section_id, block_type (logo/text/table/field/custom)
  - [ ] column_position, content_data (JSON), styling (JSON)
  - [ ] order, created_at
- [ ] **Create block_templates table** (reusable blocks)
  - [ ] id, name, block_type, template_data (JSON)
  - [ ] preview_image, is_system, created_at

### Step 0.2: Visual Builder Models v2.0
- [ ] **TemplateLayout Model** dengan relationships
- [ ] **LayoutSection Model** dengan section management
- [ ] **ContentBlock Model** dengan block rendering
- [ ] **BlockTemplate Model** untuk reusable components
- [ ] **Testing: Unit tests untuk semua models v2.0**

### Step 0.3: Visual Builder Page v2.0
- [ ] **Create VisualTemplateBuilder Filament page**
- [ ] **Setup Vue.js 3 + Composition API integration**
- [ ] **Implement section builder interface**
- [ ] **Create content block palette**
- [ ] **Build drag & drop functionality**
- [ ] **Testing: Test visual builder basic functionality**

### Step 0.4: Company Template Version Selection
- [ ] **Add template_version field to companies table**
- [ ] **Create template version selection interface**
- [ ] **Implement version-based routing logic**
- [ ] **Setup migration tools dari v1 ke v2**
- [ ] **Testing: Test version selection dan routing**

---

## Phase 1: Foundation & Database Setup (LEGACY - v1.0)

### Step 1.1: Database Schema Design
- [x] Buat tabel `invoice_templates` untuk menyimpan template master
  - [x] id, name, description, type (custom/legacy), is_active
  - [x] created_by, company_id (nullable), template_data (JSON)
  - [x] preview_image (optional), timestamps
- [x] Buat tabel `template_sections` untuk komponen modular
  - [x] id, template_id, section_type, section_name
  - [x] section_config (JSON), sort_order, is_active
- [x] Buat tabel `template_components` untuk library komponen
  - [x] id, component_type, component_name, component_code
  - [x] preview_image, is_system (true untuk komponen bawaan)
- [x] Buat tabel `template_assets` untuk media files
  - [x] id, template_id, asset_type, file_path, file_name
- [x] Setup relasi antar tabel dengan proper indexing

### Step 1.2: Migration Strategy
- [x] Buat migration files untuk semua tabel baru
- [x] Buat seeder untuk populate komponen default dari template existing
- [x] Setup foreign key constraints dan cascade rules
- [ ] Backup existing template system sebagai fallback

### Step 1.3: Model & Relationship Setup
- [x] Buat Eloquent models untuk semua tabel baru
- [x] Define relationships (hasMany, belongsTo, morphMany)
- [x] Setup model factories untuk testing
- [x] Implement soft deletes untuk template versioning
- [x] **Testing: Write unit tests untuk setiap model dan relationship**

---

## Phase 2: Core Template Builder Interface

### Step 2.1: Filament Resource Creation
- [x] Buat `InvoiceTemplateResource` dengan CRUD operations
- [x] **Testing: Test CRUD operations untuk InvoiceTemplateResource**
- [x] Setup form fields untuk template basic info
- [x] **Testing: Test form validation dan data handling**
- [x] Implement table view dengan search & filter
- [x] **Testing: Test search, filter, dan pagination functionality**
- [x] Add bulk actions untuk template management
- [x] **Testing: Test bulk operations**

### Step 2.2: Visual Builder Page
- [x] Buat custom Filament page untuk template builder
- [x] **Testing: Test page rendering dan basic functionality**
- [x] Setup drag & drop interface menggunakan Alpine.js
- [x] **Testing: Test drag & drop functionality**
- [x] Implement section-based editing system
- [x] **Testing: Test section creation, editing, dan deletion**
- [x] Create component palette/library sidebar
- [x] **Testing: Test component selection dan insertion**

### Step 2.3: Single Page Preview System
- [ ] Buat iframe-based preview panel dengan single page constraint
- [ ] **Testing: Test preview rendering dengan sample data dalam Legal/F4 format**
- [ ] Implement real-time preview updates dengan page usage indicator
- [ ] **Testing: Test real-time synchronization dan page fill percentage**
- [ ] Setup responsive preview modes (Legal/F4 page sizes)
- [ ] **Testing: Test page size switching dalam preview**
- [ ] Add zoom & pan functionality untuk detailed editing
- [ ] **Testing: Test zoom dan pan controls**
- [ ] Implement content overflow warnings dalam preview
- [ ] **Testing: Test overflow detection dan visual warnings**
- [ ] Create maximum content preview mode untuk testing
- [ ] **Testing: Test preview dengan maximum invoice items (25+)**

---

## Phase 3: Component Library Development

### Step 3.1: Core Components Migration
- [ ] Migrate existing template components ke database
  - [ ] Kop components (left, right, center, centerthara, justified)
  - [ ] Billto components (left, right, justified)
  - [ ] Table components (2-columns, 3-columns, 4-columns)
  - [ ] Inwords components (left, center, right)
  - [ ] Bankinfo components (model-1-bg-border, model-2-border, model-3-nobg-noborder)
- [ ] Create component categories dan preview thumbnails
- [ ] Setup component configuration schemas

### Step 3.2: Advanced Components
- [ ] Develop rich text editor components
- [ ] Create table builder dengan dynamic columns
- [ ] Implement image/logo placement components
- [ ] Build signature & bank info components
- [ ] **Enhanced: Custom columns support untuk CompanyBank JSON parsing**

### Step 3.3: Single Page Aware Styling System
- [ ] Create color picker interface
- [ ] Implement typography controls dengan single page constraints
- [ ] **Testing: Test font size limits untuk single page fit**
- [ ] Build spacing & layout controls dengan space optimization
- [ ] **Testing: Test margin/padding impact pada page usage**
- [ ] Add border & shadow styling options
- [ ] Implement background options (colors, gradients, images)
- [ ] Create content density controls untuk space management
- [ ] **Testing: Test content density impact pada readability**
- [ ] Implement responsive font scaling system
- [ ] **Testing: Test automatic font scaling dengan various content volumes**

---

## Phase 4: Template Generation Engine

### Step 4.1: JSON to Blade Converter
- [ ] Develop template parser untuk convert JSON config ke Blade
- [ ] **Testing: Test JSON parsing dengan various template configurations**
- [ ] Implement dynamic include system
- [ ] **Testing: Test dynamic includes dengan different component types**
- [ ] Create fallback mechanism ke legacy templates
- [ ] **Testing: Test fallback functionality dengan legacy templates**
- [ ] Setup template caching untuk performance
- [ ] **Testing: Test caching mechanism dan cache invalidation**

### Step 4.2: Enhanced Data Binding System
- [ ] Implement variable replacement engine
- [ ] **Testing: Test variable replacement dengan sample data**
- [ ] Create conditional rendering logic
- [ ] **Testing: Test conditional rendering dengan different scenarios**
- [ ] Setup multi-language support
- [ ] **Testing: Test multi-language rendering**
- [ ] Build calculation engine untuk dynamic values
- [ ] **Testing: Test calculations dengan various formulas**
- [ ] **Enhanced: JSON field parsing untuk CompanyBank custom_columns**
- [ ] **Testing: Test custom_columns parsing dan rendering**
- [ ] **Enhanced: Support bank_custom_columns dari Invoice**
- [ ] **Testing: Test bank_custom_columns integration**
- [ ] **Enhanced: Variable mapping untuk {{bank.custom_columns.field_name.value}}**
- [ ] **Testing: Test variable mapping untuk custom columns**

### Step 4.3: Single Page PDF Generation (DomPDF)
- [ ] Setup DomPDF dengan Legal/F4 page configurations
- [ ] **Testing: Test basic PDF generation dengan Legal dan F4 sizes**
- [ ] Implement content height calculation system
- [ ] **Testing: Test content measurement accuracy dengan various invoice lengths**
- [ ] Create dynamic font scaling system berdasarkan content density
- [ ] **Testing: Test font scaling dengan different content volumes**
- [ ] Implement content compression algorithms untuk single page fit
- [ ] **Testing: Test compression quality vs readability balance**
- [ ] Build single page validation system
- [ ] **Testing: Test validation dengan maximum content scenarios (25+ items)**
- [ ] Create overflow handling mechanisms dan fallback behaviors
- [ ] **Testing: Test overflow scenarios dan automatic adjustments**
- [ ] Implement page size auto-selection (Legal vs F4)
- [ ] **Testing: Test page size optimization berdasarkan content**
- [ ] Setup image handling untuk single page constraints
- [ ] **Testing: Test logo dan signature rendering dalam space-limited layout**
- [ ] Integrate dengan existing PDF generation workflow
- [ ] **Testing: Test backward compatibility dengan existing invoice generation**

---

## Phase 5: User Experience Enhancement

### Step 5.1: Template Management
- [ ] Implement template duplication functionality
- [ ] Create template sharing between companies
- [ ] Build template versioning system
- [ ] Add template import/export features
- [ ] **Enhanced: Template component library dengan custom columns support**

### Step 5.2: Single Page Aware User Interface
- [ ] Implement undo/redo functionality
- [ ] Add keyboard shortcuts untuk power users
- [ ] Create guided tutorial/onboarding dengan single page best practices
- [ ] **Testing: Test tutorial effectiveness untuk single page constraints**
- [ ] Build context-sensitive help system dengan page usage tips
- [ ] Add layer management seperti Photoshop
- [ ] Implement real-time page usage indicator (progress bar/percentage)
- [ ] **Testing: Test page usage accuracy dan visual feedback**
- [ ] Create content overflow alerts dan suggestions
- [ ] **Testing: Test alert system dengan various overflow scenarios**
- [ ] Add automatic layout optimization suggestions
- [ ] **Testing: Test optimization suggestions effectiveness**

### Step 5.3: Performance Optimization
- [ ] Implement lazy loading untuk components
- [ ] Setup template caching strategy
- [ ] Optimize database queries
- [ ] Add progress indicators untuk long operations

---

## Phase 6: Integration & Compatibility

### Step 6.1: Company Template Selection Enhancement
- [ ] Enhance existing company form dengan template picker
- [ ] Add template preview dalam selection
- [ ] Implement template assignment logic
- [ ] Create migration tools dari legacy ke custom templates

### Step 6.2: Invoice Generation Integration
- [ ] Modify existing invoice generation untuk support custom templates
- [ ] Implement template type detection (legacy vs custom)
- [ ] Setup fallback mechanisms
- [ ] Test compatibility dengan existing invoice flow
- [ ] **Enhanced: Integration dengan CompanyBank custom_columns parsing**

### Step 6.3: Backward Compatibility Testing
- [ ] Test semua existing templates tetap berfungsi
- [ ] Verify invoice generation tidak berubah untuk legacy templates
- [ ] Ensure data integrity selama migration
- [ ] Create rollback procedures jika diperlukan

---

## Phase 7: Advanced Features

### Step 7.1: Business Logic Integration
- [ ] Implement conditional sections berdasarkan business type
- [ ] Create dynamic pricing calculations
- [ ] Setup multi-currency support
- [ ] Build tax calculation integration
- [ ] **Enhanced: Dynamic custom columns rendering berdasarkan CompanyBank data**

### Step 7.2: Automation Features
- [ ] Create template auto-selection rules
- [ ] Implement batch template operations
- [ ] Build template analytics & usage tracking
- [ ] Setup automated template optimization suggestions

### Step 7.3: Extensibility Framework
- [ ] Create plugin architecture untuk custom components
- [ ] Implement API endpoints untuk external integrations
- [ ] Build webhook system untuk real-time updates
- [ ] Setup custom field type framework

---

## Phase 8: Comprehensive Testing & Quality Assurance

### Step 8.1: Consolidated Unit Testing
- [ ] **Comprehensive review**: Verify semua unit tests dari Phase 1-7 sudah complete
- [ ] **Missing tests**: Identify dan create tests untuk functions yang belum di-test
- [ ] **Test coverage**: Ensure minimum 90% code coverage
- [ ] **Performance tests**: Test individual functions untuk performance bottlenecks
- [ ] **Enhanced: Comprehensive JSON parsing tests untuk custom columns**

### Step 8.2: Integration Testing
- [ ] **End-to-end workflow testing**: Test complete template creation workflow
- [ ] **Cross-component testing**: Test interactions between different components
- [ ] **Database integration**: Test semua database operations
- [ ] **API integration**: Test external integrations
- [ ] **Enhanced: Full custom columns integration testing**

### Step 8.3: System Testing
- [ ] **Load testing**: Test system dengan multiple concurrent users
- [ ] **Stress testing**: Test system limits dan error handling
- [ ] **Security testing**: Test untuk vulnerabilities
- [ ] **Compatibility testing**: Test pada different browsers dan devices
- [ ] **Backward compatibility**: Comprehensive testing dengan legacy templates

### Step 8.4: User Acceptance Testing
- [ ] **Staff testing**: Test dengan actual users (staff invoice)
- [ ] **Feedback collection**: Gather detailed feedback pada user experience
- [ ] **Real data testing**: Test dengan real company data dan custom columns
- [ ] **Business requirements validation**: Validate semua requirements terpenuhi
- [ ] **Training effectiveness**: Test apakah user bisa menggunakan system setelah training

---

## Phase 9: Documentation & Training

### Step 9.1: Technical Documentation
- [ ] Document database schema & relationships
- [ ] Create API documentation
- [ ] Write developer guide untuk extensibility
- [ ] Document troubleshooting procedures
- [ ] **Enhanced: Document custom columns integration**

### Step 9.2: User Documentation
- [ ] Create user manual untuk template builder
- [ ] Build video tutorials
- [ ] Create quick start guides
- [ ] Document best practices untuk custom columns usage

### Step 9.3: Training Materials
- [ ] Prepare training sessions untuk staff
- [ ] Create hands-on exercises
- [ ] Build FAQ & common issues guide
- [ ] Setup support procedures

---

## Phase 10: Deployment & Monitoring

### Step 10.1: Production Deployment
- [ ] Setup staging environment untuk final testing
- [ ] Create deployment checklist
- [ ] Implement monitoring & logging
- [ ] Setup backup procedures

### Step 10.2: Performance Monitoring
- [ ] Monitor template generation performance
- [ ] Track user adoption metrics
- [ ] Monitor system resource usage
- [ ] Setup alerting untuk issues

### Step 10.3: Continuous Improvement
- [ ] Gather user feedback post-deployment
- [ ] Monitor usage patterns
- [ ] Plan feature enhancements
- [ ] Setup regular maintenance procedures

---

## Timeline Estimasi

- **Phase 1-2**: 2-3 minggu (Foundation & Core Interface)
- **Phase 3-4**: 3-4 minggu (Components & Engine)
- **Phase 5-6**: 2-3 minggu (UX & Integration)
- **Phase 7**: 2-3 minggu (Advanced Features)
- **Phase 8-10**: 2-3 minggu (Testing & Deployment)

**Total Estimasi: 11-16 minggu** (tergantung kompleksitas dan resource availability)

---

## Success Metrics

- [ ] ✅ Backward compatibility 100% maintained
- [ ] ✅ User dapat membuat template tanpa coding
- [ ] ✅ Template generation time < 5 detik
- [ ] ✅ User adoption rate > 80% dalam 3 bulan
- [ ] ✅ Zero downtime selama implementation
- [ ] ✅ Performance tidak menurun dari sistem existing
- [ ] ✅ Custom columns dari CompanyBank terintegrasi sempurna
- [ ] ✅ **Single page constraint**: 100% invoice fit dalam 1 halaman (Legal/F4)
- [ ] ✅ **Content overflow**: 0% overflow cases dalam production
- [ ] ✅ **PDF quality**: Professional output quality untuk printing

---

## Testing Strategy & Approach

### Incremental Testing Approach
Sesuai dengan batasan yang ditetapkan: **"Pengujian dilakukan setiap kali fungsi atau metode baru dibuat. jika tidak memungkinkan, maka pengujian dilakukan setelah semua fungsi atau metode dibuat dalam satu fitur."**

### Testing Levels:
1. **Unit Testing**: Setiap function/method di-test segera setelah dibuat
2. **Feature Testing**: Setelah satu fitur complete, test keseluruhan fitur
3. **Integration Testing**: Test interaksi antar fitur
4. **System Testing**: Test keseluruhan system

### Testing Tools:
- **PHPUnit**: Untuk unit dan feature testing
- **Laravel Dusk**: Untuk browser testing
- **Pest**: Alternative testing framework (optional)
- **Coverage Tools**: Untuk monitoring test coverage

### Test-Driven Development (TDD) Guidelines:
- Write test cases sebelum atau bersamaan dengan development
- Minimum 90% code coverage untuk critical functions
- Automated testing pipeline dengan CI/CD
- Regular test review dan refactoring

---

## Special Considerations

### Single Page PDF Constraints
- [ ] **Page Size Support**: Legal (8.5" x 14") dan F4/Folio (210mm x 330mm)
- [ ] **Content Measurement**: Accurate height calculation untuk prevent overflow
- [ ] **Dynamic Font Scaling**: Automatic font size adjustment berdasarkan content density
- [ ] **Content Compression**: Smart compression algorithms untuk fit content dalam 1 halaman
- [ ] **Overflow Handling**: Graceful handling ketika content terlalu banyak
- [ ] **Page Usage Indicator**: Real-time feedback untuk space utilization
- [ ] **Maximum Content Testing**: Testing dengan 25+ invoice items untuk worst-case scenarios

### Custom Columns Integration
- [ ] **CompanyBank JSON parsing**: Field `custom_columns` dengan structure `{"field_name": {"type": "text/richtext/textarea", "value": "content"}}`
- [ ] **Invoice bank_custom_columns**: Integration dengan invoice-specific custom columns
- [ ] **Dynamic rendering**: Template harus bisa render custom columns yang berbeda per company
- [ ] **Variable system**: Support untuk `{{bank.custom_columns.field_name.value}}` dalam template
- [ ] **Conditional display**: Show/hide custom columns berdasarkan ketersediaan data
- [ ] **Single page impact**: Custom columns harus consider space constraints

### Tingkat Dinamisme: 8/10 (Adjusted untuk Single Page)
- **Layout flexibility** dengan single page constraints
- **Smart customization** options yang consider space limitations
- **Real-time preview** dengan page usage feedback
- **Advanced business logic** integration
- **Professional-grade output** dalam single page format
- **Extensible architecture** untuk future needs
- **Enhanced**: Full support untuk dynamic custom columns dengan space awareness
- **Single page optimization**: Automatic content fitting dan compression
- **Responsive design**: Adapt layout berdasarkan content volume

