<?php

/**
 * Debug Section Settings Issue
 * Run via: php artisan tinker
 */

echo "=== Debugging Section Settings Issue ===\n";

try {
    // 1. Check database structure
    echo "1. Checking database structure...\n";
    
    $columns = DB::select("DESCRIBE layout_sections");
    echo "   📊 layout_sections columns:\n";
    foreach ($columns as $column) {
        echo "      - {$column->Field} ({$column->Type})\n";
    }
    
    // 2. Create test section
    echo "\n2. Creating test section...\n";
    
    $testTemplate = App\Models\TemplateLayout::firstOrCreate(
        ['name' => 'Debug Test Template'],
        [
            'description' => 'Template for debugging section settings',
            'company_id' => null,
            'version' => 'v2',
            'created_by' => 1,
        ]
    );
    
    $testSection = App\Models\LayoutSection::updateOrCreate(
        [
            'template_layout_id' => $testTemplate->id,
            'section_type' => 'header'
        ],
        [
            'section_name' => 'Debug Header Section',
            'column_count' => 1,
            'column_layout' => 'equal',
            'column_widths' => null,
            'background_color' => null,
            'min_height' => 100,
            'order' => 0,
            'is_active' => true,
        ]
    );
    
    echo "   ✅ Test section created: ID {$testSection->id}\n";
    echo "   📊 Initial values:\n";
    echo "      - column_count: {$testSection->column_count}\n";
    echo "      - column_layout: {$testSection->column_layout}\n";
    echo "      - column_widths: " . json_encode($testSection->column_widths) . "\n";
    echo "      - background_color: " . ($testSection->background_color ?? 'null') . "\n";
    echo "      - min_height: {$testSection->min_height}\n";
    
    // 3. Test direct update
    echo "\n3. Testing direct database update...\n";
    
    $updateData = [
        'section_name' => 'Updated Debug Section',
        'column_count' => 3,
        'column_layout' => 'custom',
        'column_widths' => [25, 50, 25],
        'background_color' => '#f0f0f0',
        'min_height' => 150,
    ];
    
    echo "   📝 Updating with data:\n";
    foreach ($updateData as $key => $value) {
        $displayValue = is_array($value) ? json_encode($value) : ($value ?? 'null');
        echo "      - {$key}: {$displayValue}\n";
    }
    
    $result = $testSection->update($updateData);
    echo "   📊 Update result: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
    
    // 4. Verify update
    echo "\n4. Verifying update...\n";
    
    $testSection->refresh();
    echo "   📊 After update:\n";
    echo "      - section_name: {$testSection->section_name}\n";
    echo "      - column_count: {$testSection->column_count}\n";
    echo "      - column_layout: {$testSection->column_layout}\n";
    echo "      - column_widths: " . json_encode($testSection->column_widths) . "\n";
    echo "      - background_color: " . ($testSection->background_color ?? 'null') . "\n";
    echo "      - min_height: {$testSection->min_height}\n";
    
    // 5. Test Livewire-style update
    echo "\n5. Testing Livewire-style update...\n";
    
    $sectionSettings = [
        'section_name' => 'Livewire Test Section',
        'column_count' => 4,
        'column_layout' => 'equal',
        'column_widths' => [],
        'background_color' => '#e0e0e0',
        'min_height' => 200,
    ];
    
    echo "   📝 Livewire-style data:\n";
    foreach ($sectionSettings as $key => $value) {
        $displayValue = is_array($value) ? json_encode($value) : ($value ?? 'null');
        echo "      - {$key}: {$displayValue}\n";
    }
    
    $livewireUpdateData = [
        'section_name' => $sectionSettings['section_name'] ?? $testSection->section_name,
        'column_count' => (int) ($sectionSettings['column_count'] ?? $testSection->column_count),
        'column_layout' => $sectionSettings['column_layout'] ?? $testSection->column_layout,
        'column_widths' => $sectionSettings['column_widths'] ?? $testSection->column_widths,
        'background_color' => $sectionSettings['background_color'] ?? $testSection->background_color,
        'min_height' => (int) ($sectionSettings['min_height'] ?? $testSection->min_height),
    ];
    
    $livewireResult = $testSection->update($livewireUpdateData);
    echo "   📊 Livewire update result: " . ($livewireResult ? 'SUCCESS' : 'FAILED') . "\n";
    
    // 6. Final verification
    echo "\n6. Final verification...\n";
    
    $testSection->refresh();
    echo "   📊 Final state:\n";
    echo "      - section_name: {$testSection->section_name}\n";
    echo "      - column_count: {$testSection->column_count}\n";
    echo "      - column_layout: {$testSection->column_layout}\n";
    echo "      - column_widths: " . json_encode($testSection->column_widths) . "\n";
    echo "      - background_color: " . ($testSection->background_color ?? 'null') . "\n";
    echo "      - min_height: {$testSection->min_height}\n";
    
    // 7. Test fillable attributes
    echo "\n7. Testing fillable attributes...\n";
    
    $fillable = $testSection->getFillable();
    echo "   📋 Fillable attributes:\n";
    foreach ($fillable as $attr) {
        echo "      - {$attr}\n";
    }
    
    $requiredFields = ['section_name', 'column_count', 'column_layout', 'column_widths', 'background_color', 'min_height'];
    $missingFields = array_diff($requiredFields, $fillable);
    
    if (empty($missingFields)) {
        echo "   ✅ All required fields are fillable\n";
    } else {
        echo "   ❌ Missing fillable fields: " . implode(', ', $missingFields) . "\n";
    }
    
    // 8. Test casts
    echo "\n8. Testing casts...\n";
    
    $casts = $testSection->getCasts();
    echo "   📋 Model casts:\n";
    foreach ($casts as $field => $cast) {
        echo "      - {$field}: {$cast}\n";
    }
    
    // 9. Create block templates for content testing
    echo "\n9. Creating block templates for content testing...\n";
    
    // Clear existing system blocks
    App\Models\BlockTemplate::where('is_system', true)->delete();
    
    $basicBlocks = [
        [
            'name' => 'Company Logo',
            'description' => 'Company logo image',
            'block_type' => 'logo',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.logo',
                'width' => '120px',
                'height' => 'auto'
            ],
            'field_mappings' => [
                'field' => 'company.logo',
                'format' => 'image'
            ],
            'default_styling' => [
                'alignment' => 'left'
            ],
            'preview_html' => '<div class="w-20 h-16 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500">[LOGO]</div>',
            'is_system' => true,
            'tags' => ['logo', 'company', 'header']
        ],
        [
            'name' => 'Company Name',
            'description' => 'Company name text',
            'block_type' => 'company_name',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.name',
                'prefix' => '',
                'suffix' => ''
            ],
            'field_mappings' => [
                'field' => 'company.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 18,
                'font_weight' => 'bold'
            ],
            'preview_html' => '<div class="text-lg font-bold">PT. Sample Company</div>',
            'is_system' => true,
            'tags' => ['company', 'name', 'header']
        ],
        [
            'name' => 'Free Text Block',
            'description' => 'Customizable text content',
            'block_type' => 'free_text',
            'category' => 'general',
            'template_data' => [
                'text' => 'Enter your custom text here',
                'allow_html' => true,
                'editable' => true
            ],
            'field_mappings' => [],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12
            ],
            'preview_html' => '<div class="border-2 border-dashed border-blue-300 p-2 text-blue-600 text-sm">✏️ Free Text Block</div>',
            'is_system' => true,
            'tags' => ['text', 'custom', 'editable']
        ]
    ];
    
    foreach ($basicBlocks as $blockData) {
        $block = App\Models\BlockTemplate::create($blockData);
        echo "   ✅ Created block: {$block->name}\n";
    }
    
    // 10. Add test blocks to section
    echo "\n10. Adding test blocks to section...\n";
    
    $logoBlock = App\Models\BlockTemplate::where('block_type', 'logo')->first();
    $nameBlock = App\Models\BlockTemplate::where('block_type', 'company_name')->first();
    
    if ($logoBlock && $nameBlock) {
        // Clear existing blocks
        App\Models\ContentBlock::where('layout_section_id', $testSection->id)->delete();
        
        $logoContentBlock = $logoBlock->createContentBlock($testSection->id, [
            'column_position' => 1,
            'order' => 0
        ]);
        
        $nameContentBlock = $nameBlock->createContentBlock($testSection->id, [
            'column_position' => 2,
            'order' => 0
        ]);
        
        echo "   ✅ Logo block added: ID {$logoContentBlock->id}\n";
        echo "   ✅ Name block added: ID {$nameContentBlock->id}\n";
        
        // Test content rendering
        echo "\n11. Testing content rendering...\n";
        
        $testData = [
            'company' => [
                'logo' => '/path/to/logo.png',
                'name' => 'PT. Test Company'
            ]
        ];
        
        $logoHtml = $logoContentBlock->renderHtml($testData);
        $nameHtml = $nameContentBlock->renderHtml($testData);
        
        echo "   📄 Logo HTML: " . strip_tags($logoHtml) . "\n";
        echo "   📄 Name HTML: " . strip_tags($nameHtml) . "\n";
    }
    
    // 12. Test URLs
    echo "\n12. Testing URLs...\n";
    
    $builderUrl = "/admin/visual-template-builder?template={$testTemplate->id}";
    echo "   🎨 Visual Builder URL: {$builderUrl}\n";
    
    echo "\n=== Debug Summary ===\n";
    echo "✅ Database structure: OK\n";
    echo "✅ Model fillable: OK\n";
    echo "✅ Model casts: OK\n";
    echo "✅ Direct update: " . ($result ? 'OK' : 'FAILED') . "\n";
    echo "✅ Livewire update: " . ($livewireResult ? 'OK' : 'FAILED') . "\n";
    echo "✅ Block templates: OK\n";
    echo "✅ Content blocks: OK\n";
    
    echo "\n🔍 If section settings still don't work:\n";
    echo "1. Check browser console for JavaScript errors\n";
    echo "2. Check Laravel logs: tail -f storage/logs/laravel.log\n";
    echo "3. Verify wire:model bindings in the form\n";
    echo "4. Test with simple column count change first\n";
    
    echo "\n🚀 Test the Visual Builder: {$builderUrl}\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
