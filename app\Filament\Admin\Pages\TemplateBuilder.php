<?php

namespace App\Filament\Admin\Pages;

use App\Models\InvoiceTemplate;
use App\Models\TemplateComponent;
use App\Services\FontService;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Request;

class TemplateBuilder extends Page implements HasForms, HasActions
{
    use InteractsWithForms, InteractsWithActions;

    protected static ?string $navigationIcon = 'heroicon-o-paint-brush';
    protected static ?string $navigationGroup = 'Template Management';
    protected static ?string $navigationLabel = 'Template Builder';
    protected static ?int $navigationSort = 3;
    protected static string $view = 'filament.admin.pages.template-builder';

    public ?InvoiceTemplate $template = null;
    public array $templateData = [];
    public array $availableComponents = [];
    public array $selectedComponents = [];
    public string $previewMode = 'desktop';
    public string $pageSize = 'legal';

    public function mount(): void
    {
        // Show experimental warning
        Notification::make()
            ->title('🧪 Experimental Feature')
            ->body('This is an experimental template builder. Not recommended for production use. Use at your own risk.')
            ->warning()
            ->persistent()
            ->send();

        // Get template ID from URL parameter
        $templateId = Request::get('template');

        if ($templateId) {
            $this->template = InvoiceTemplate::find($templateId);
            if ($this->template) {
                $this->templateData = $this->template->template_data ?? [];
                $this->pageSize = $this->templateData['page_size'] ?? 'legal';
            }
        }

        $this->loadAvailableComponents();
        $this->initializeSelectedComponents();
    }

    protected function loadAvailableComponents(): void
    {
        $this->availableComponents = TemplateComponent::all()
            ->groupBy('type')
            ->map(function ($components, $type) {
                return [
                    'type' => $type,
                    'label' => ucfirst($type),
                    'components' => $components->map(function ($component) {
                        return [
                            'id' => $component->id,
                            'name' => $component->name,
                            'description' => $component->description,
                            'preview_html' => $component->preview_html,
                            'config_schema' => $component->config_schema,
                        ];
                    })->toArray()
                ];
            })
            ->values()
            ->toArray();
    }

    protected function initializeSelectedComponents(): void
    {
        if ($this->template && isset($this->templateData['sections'])) {
            $this->selectedComponents = $this->templateData['sections'];
        } else {
            // Default empty sections
            $this->selectedComponents = [
                'header' => [],
                'body' => [],
                'footer' => []
            ];
        }
    }

    public function getTitle(): string
    {
        if ($this->template) {
            return '🧪 Template Builder (Experimental): ' . $this->template->name;
        }
        return '🧪 Template Builder (Experimental)';
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('save')
                ->label('Save Template')
                ->icon('heroicon-o-check')
                ->color('success')
                ->action('saveTemplate')
                ->visible(fn () => $this->template !== null),

            Action::make('preview')
                ->label('Preview')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->action('previewTemplate'),

            Action::make('settings')
                ->label('Template Settings')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('gray')
                ->action('openSettings'),
        ];
    }

    public function saveTemplate(): void
    {
        if (!$this->template) {
            Notification::make()
                ->title('Error')
                ->body('No template selected to save.')
                ->danger()
                ->send();
            return;
        }

        try {
            // Update template data with current configuration
            $updatedData = array_merge($this->templateData, [
                'sections' => $this->selectedComponents,
                'last_modified' => now()->toISOString(),
            ]);

            $this->template->update([
                'template_data' => $updatedData
            ]);

            Notification::make()
                ->title('Success')
                ->body('Template saved successfully.')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to save template: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function previewTemplate(): void
    {
        // Generate preview URL
        $previewUrl = route('template.preview', [
            'template' => $this->template?->id,
            'data' => base64_encode(json_encode([
                'sections' => $this->selectedComponents,
                'config' => $this->templateData
            ]))
        ]);

        // Open preview in new tab
        $this->js("window.open('$previewUrl', '_blank')");
    }

    public function openSettings(): void
    {
        // Redirect to template edit page
        if ($this->template) {
            $this->redirect(route('filament.admin.resources.invoice-templates.edit', $this->template));
        }
    }

    // Alpine.js methods for frontend interaction
    public function addComponent(int $componentId, string $section): void
    {
        try {
            $component = TemplateComponent::find($componentId);

            if (!$component) {
                Notification::make()
                    ->title('Error')
                    ->body('Component not found.')
                    ->danger()
                    ->send();
                return;
            }

            // Ensure section exists
            if (!isset($this->selectedComponents[$section])) {
                $this->selectedComponents[$section] = [];
            }

            $newComponent = [
                'id' => uniqid('comp_'),
                'component_id' => $componentId,
                'name' => $component->name,
                'type' => $component->type,
                'config' => is_string($component->default_config) ? json_decode($component->default_config, true) : ($component->default_config ?? []),
                'order' => count($this->selectedComponents[$section]),
            ];

            $this->selectedComponents[$section][] = $newComponent;

            // Show success notification
            Notification::make()
                ->title('Component Added')
                ->body("'{$component->name}' added to {$section} section.")
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to add component: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function removeComponent(string $componentId, string $section): void
    {
        if (isset($this->selectedComponents[$section])) {
            $this->selectedComponents[$section] = array_filter(
                $this->selectedComponents[$section],
                fn($comp) => $comp['id'] !== $componentId
            );

            // Reorder components
            $this->selectedComponents[$section] = array_values($this->selectedComponents[$section]);

            Notification::make()
                ->title('Component Removed')
                ->body('Component removed from template.')
                ->success()
                ->send();
        }
    }

    public function moveComponent(string $componentId, string $fromSection, string $toSection, int $newOrder): void
    {
        // Find and remove component from source section
        $component = null;
        if (isset($this->selectedComponents[$fromSection])) {
            foreach ($this->selectedComponents[$fromSection] as $index => $comp) {
                if ($comp['id'] === $componentId) {
                    $component = $comp;
                    unset($this->selectedComponents[$fromSection][$index]);
                    $this->selectedComponents[$fromSection] = array_values($this->selectedComponents[$fromSection]);
                    break;
                }
            }
        }

        // Add component to target section
        if ($component) {
            $component['order'] = $newOrder;

            if (!isset($this->selectedComponents[$toSection])) {
                $this->selectedComponents[$toSection] = [];
            }

            array_splice($this->selectedComponents[$toSection], $newOrder, 0, [$component]);
        }
    }

    public function updateComponentConfig(string $componentId, string $section, array $config): void
    {
        try {
            if (isset($this->selectedComponents[$section])) {
                foreach ($this->selectedComponents[$section] as &$component) {
                    if ($component['id'] === $componentId) {
                        $component['config'] = array_merge($component['config'] ?? [], $config);

                        Notification::make()
                            ->title('Component Updated')
                            ->body('Component configuration saved successfully.')
                            ->success()
                            ->send();
                        return;
                    }
                }
            }

            Notification::make()
                ->title('Error')
                ->body('Component not found.')
                ->danger()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to update component: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function changePageSize(string $size): void
    {
        $this->pageSize = $size;
        $this->templateData['page_size'] = $size;

        Notification::make()
            ->title('Page Size Changed')
            ->body("Page size changed to {$size}.")
            ->success()
            ->send();
    }

    public function changePreviewMode(string $mode): void
    {
        $this->previewMode = $mode;

        Notification::make()
            ->title('Preview Mode Changed')
            ->body("Preview mode changed to {$mode}.")
            ->success()
            ->send();
    }



    public function resetTemplate(): void
    {
        try {
            $this->selectedComponents = [
                'header' => [],
                'body' => [],
                'footer' => []
            ];

            Notification::make()
                ->title('Template Reset')
                ->body('Template has been reset to empty state.')
                ->warning()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Reset Failed')
                ->body('Failed to reset template: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function duplicateComponent(string $componentId, string $section): void
    {
        try {
            if (!isset($this->selectedComponents[$section])) {
                return;
            }

            foreach ($this->selectedComponents[$section] as $component) {
                if ($component['id'] === $componentId) {
                    $duplicatedComponent = $component;
                    $duplicatedComponent['id'] = uniqid('comp_');
                    $duplicatedComponent['name'] = $component['name'] . ' (Copy)';
                    $duplicatedComponent['order'] = count($this->selectedComponents[$section]);

                    $this->selectedComponents[$section][] = $duplicatedComponent;

                    Notification::make()
                        ->title('Component Duplicated')
                        ->body("'{$component['name']}' has been duplicated.")
                        ->success()
                        ->send();
                    return;
                }
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Duplication Failed')
                ->body('Failed to duplicate component: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    // Get data for frontend
    public function getBuilderData(): array
    {
        return [
            'template' => $this->template?->toArray(),
            'templateData' => $this->templateData,
            'availableComponents' => $this->availableComponents,
            'selectedComponents' => $this->selectedComponents,
            'previewMode' => $this->previewMode,
            'pageSize' => $this->pageSize,
            'fonts' => FontService::getSelectOptions(),
        ];
    }
}
