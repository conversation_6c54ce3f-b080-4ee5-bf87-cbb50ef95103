<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Add table-based layout support to layout_sections
        Schema::table('layout_sections', function (Blueprint $table) {
            // Table structure fields
            $table->integer('table_rows')->default(1)->after('column_count');
            $table->json('table_structure')->nullable()->after('table_rows'); // Store complete table structure
            $table->enum('layout_type', ['flex', 'grid', 'table'])->default('table')->after('column_layout');
        });

        // Add table cell support to content_blocks
        Schema::table('content_blocks', function (Blueprint $table) {
            // Table cell positioning
            $table->integer('row_position')->default(1)->after('column_position');
            $table->integer('rowspan')->default(1)->after('row_position');
            $table->integer('colspan')->default(1)->after('rowspan');
            
            // Cell-specific styling
            $table->enum('vertical_align', ['top', 'middle', 'bottom'])->default('top')->after('alignment');
            $table->json('cell_styling')->nullable()->after('vertical_align'); // Additional cell styling
        });
    }

    public function down(): void
    {
        Schema::table('layout_sections', function (Blueprint $table) {
            $table->dropColumn(['table_rows', 'table_structure', 'layout_type']);
        });

        Schema::table('content_blocks', function (Blueprint $table) {
            $table->dropColumn(['row_position', 'rowspan', 'colspan', 'vertical_align', 'cell_styling']);
        });
    }
};
