<?php

namespace Database\Seeders;

use App\Models\Font;
use Illuminate\Database\Seeder;

class FontSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing fonts
        Font::truncate();

        // Seed DejaVu Fonts (Recommended for PDF)
        $this->seedDejaVuFonts();

        // Seed System Fonts
        $this->seedSystemFonts();

        // Seed Google Fonts (if enabled)
        if (config('services.google_fonts.enabled', false)) {
            $this->seedGoogleFonts();
        }
    }

    /**
     * Seed System Fonts (PDF Compatible - No URL needed)
     */
    private function seedDejaVuFonts(): void
    {
        $systemFonts = [
            [
                'name' => 'DejaVu Sans',
                'source' => null, // System font, no URL needed
                'type' => 'sans-serif'
            ],
            [
                'name' => 'DejaVu Serif',
                'source' => null, // System font, no URL needed
                'type' => 'serif'
            ],
            [
                'name' => 'Arial',
                'source' => null, // System font, no URL needed
                'type' => 'sans-serif'
            ],
            [
                'name' => 'Times New Roman',
                'source' => null, // System font, no URL needed
                'type' => 'serif'
            ],
            [
                'name' => 'Helvetica',
                'source' => null, // System font, no URL needed
                'type' => 'sans-serif'
            ],
            [
                'name' => 'Georgia',
                'source' => null, // System font, no URL needed
                'type' => 'serif'
            ],
        ];

        foreach ($systemFonts as $font) {
            Font::create($font);
        }
    }

    /**
     * Seed Additional System Fonts
     */
    private function seedSystemFonts(): void
    {
        $additionalSystemFonts = [
            [
                'name' => 'Tahoma',
                'source' => null, // System font, no URL needed
                'type' => 'sans-serif'
            ],
            [
                'name' => 'Verdana',
                'source' => null, // System font, no URL needed
                'type' => 'sans-serif'
            ],
            [
                'name' => 'Times',
                'source' => null, // System font, no URL needed
                'type' => 'serif'
            ],
            // Generic Fallbacks
            [
                'name' => 'sans-serif',
                'source' => null, // Generic fallback
                'type' => 'sans-serif'
            ],
            [
                'name' => 'serif',
                'source' => null, // Generic fallback
                'type' => 'serif'
            ],
        ];

        foreach ($additionalSystemFonts as $font) {
            Font::create($font);
        }
    }

    /**
     * Seed Popular Google Fonts (Serif/Sans only) with actual CSS URLs
     */
    private function seedGoogleFonts(): void
    {
        $googleFonts = [
            // Popular Sans-Serif Google Fonts
            [
                'name' => 'Roboto',
                'source' => 'https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap',
                'type' => 'sans-serif'
            ],
            [
                'name' => 'Open Sans',
                'source' => 'https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap',
                'type' => 'sans-serif'
            ],
            [
                'name' => 'Lato',
                'source' => 'https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap',
                'type' => 'sans-serif'
            ],
            [
                'name' => 'Montserrat',
                'source' => 'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap',
                'type' => 'sans-serif'
            ],
            [
                'name' => 'Poppins',
                'source' => 'https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap',
                'type' => 'sans-serif'
            ],
            [
                'name' => 'Inter',
                'source' => 'https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap',
                'type' => 'sans-serif'
            ],

            // Popular Serif Google Fonts
            [
                'name' => 'Playfair Display',
                'source' => 'https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap',
                'type' => 'serif'
            ],
            [
                'name' => 'Merriweather',
                'source' => 'https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&display=swap',
                'type' => 'serif'
            ],
            [
                'name' => 'Lora',
                'source' => 'https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap',
                'type' => 'serif'
            ],
            [
                'name' => 'Crimson Text',
                'source' => 'https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap',
                'type' => 'serif'
            ],
        ];

        foreach ($googleFonts as $font) {
            Font::create($font);
        }
    }
}
