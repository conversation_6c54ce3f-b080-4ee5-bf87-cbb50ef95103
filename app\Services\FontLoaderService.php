<?php

namespace App\Services;

use App\Models\Font;
use Illuminate\Support\Collection;

class FontLoaderService
{
    /**
     * Get all fonts that need to be loaded for a template
     */
    public static function getFontsForTemplate(array $fontNames): Collection
    {
        return Font::whereIn('name', $fontNames)->get();
    }

    /**
     * Generate CSS imports for fonts
     */
    public static function generateCssImports(Collection $fonts): string
    {
        $imports = [];
        
        foreach ($fonts as $font) {
            if ($font->source) {
                $imports[] = $font->getCssImport();
            }
        }
        
        return implode("\n", array_filter($imports));
    }

    /**
     * Generate HTML link tags for fonts
     */
    public static function generateHtmlLinks(Collection $fonts): string
    {
        $links = [];
        
        foreach ($fonts as $font) {
            if ($font->source) {
                $links[] = $font->getHtmlLink();
            }
        }
        
        return implode("\n", array_filter($links));
    }

    /**
     * Get font CSS for template preview
     */
    public static function getPreviewCss(string $fontName): string
    {
        $font = Font::where('name', $fontName)->first();
        
        if (!$font) {
            return FontService::getFontCss($fontName);
        }

        // If it's a Google Font, include the import
        if ($font->isGoogleFont()) {
            $import = $font->getCssImport();
            $stack = $font->getCssStack();
            
            return "{$import}\n.font-{$fontName} { font-family: {$stack}; }";
        }

        // For system fonts, just return the CSS stack
        return ".font-{$fontName} { font-family: {$font->getCssStack()}; }";
    }

    /**
     * Get all Google Fonts that need to be preloaded
     */
    public static function getGoogleFontsForPreload(): Collection
    {
        return Font::where('source', 'like', '%fonts.googleapis.com%')->get();
    }

    /**
     * Generate preload links for Google Fonts
     */
    public static function generatePreloadLinks(Collection $fonts): string
    {
        $preloads = [];
        
        foreach ($fonts as $font) {
            if ($font->isGoogleFont()) {
                $preloads[] = "<link rel=\"preload\" href=\"{$font->source}\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">";
                $preloads[] = "<noscript><link rel=\"stylesheet\" href=\"{$font->source}\"></noscript>";
            }
        }
        
        return implode("\n", $preloads);
    }

    /**
     * Check if font is available in database
     */
    public static function isFontAvailable(string $fontName): bool
    {
        return Font::where('name', $fontName)->exists();
    }

    /**
     * Get font fallback chain
     */
    public static function getFontFallback(string $fontName): string
    {
        $font = Font::where('name', $fontName)->first();
        
        if ($font) {
            return $font->getCssStack();
        }
        
        // Fallback to FontService if not in database
        return FontService::getFontCss($fontName);
    }

    /**
     * Get fonts grouped by type
     */
    public static function getFontsByType(): array
    {
        $fonts = Font::all()->groupBy('type');
        
        return [
            'sans-serif' => $fonts->get('sans-serif', collect()),
            'serif' => $fonts->get('serif', collect()),
            'monospace' => $fonts->get('monospace', collect()),
        ];
    }

    /**
     * Get system fonts only
     */
    public static function getSystemFonts(): Collection
    {
        return Font::whereNull('source')->get();
    }

    /**
     * Get Google Fonts only
     */
    public static function getGoogleFonts(): Collection
    {
        return Font::where('source', 'like', '%fonts.googleapis.com%')->get();
    }
}
