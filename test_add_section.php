<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\TemplateLayout;
use App\Models\LayoutSection;
use App\Models\ContentBlock;
use App\Models\User;

echo "Testing Add Section functionality...\n\n";

try {
    // 1. Create or find a test template
    echo "1. Creating test template...\n";
    
    $template = TemplateLayout::updateOrCreate(
        ['name' => 'Test Add Section Template'],
        [
            'description' => 'Template for testing add section functionality',
            'company_id' => null,
            'version' => 'v2',
            'page_size' => 'legal',
            'orientation' => 'portrait',
            'font_family' => 'DejaVu Sans',
            'font_size' => 12,
            'is_active' => true,
            'created_by' => 1,
        ]
    );
    
    echo "✅ Template created: ID {$template->id}\n";
    
    // 2. Clear existing sections
    echo "2. Clearing existing sections...\n";
    LayoutSection::where('template_layout_id', $template->id)->delete();
    echo "✅ Sections cleared\n";
    
    // 3. Test adding a header section
    echo "3. Testing add header section...\n";
    
    $section = LayoutSection::create([
        'template_layout_id' => $template->id,
        'section_type' => 'header',
        'section_name' => 'Header Section',
        'column_count' => 1,
        'table_rows' => 1,
        'layout_type' => 'table',
        'column_layout' => 'equal',
        'order' => 0,
        'is_active' => true,
        'min_height' => 80,
    ]);
    
    echo "✅ Section created: ID {$section->id}\n";
    
    // 4. Create initial cell
    echo "4. Creating initial cell...\n";
    
    $cell = ContentBlock::create([
        'layout_section_id' => $section->id,
        'block_type' => 'empty',
        'block_name' => 'Cell 1,1',
        'column_position' => 1,
        'row_position' => 1,
        'order' => 1,
        'rowspan' => 1,
        'colspan' => 1,
        'vertical_align' => 'top',
        'content_data' => [],
        'field_mapping' => [],
        'styling' => [],
    ]);
    
    echo "✅ Cell created: ID {$cell->id}\n";
    
    // 5. Verify the structure
    echo "5. Verifying structure...\n";
    
    $template->refresh();
    $sectionsCount = $template->sections()->count();
    $blocksCount = ContentBlock::where('layout_section_id', $section->id)->count();
    
    echo "✅ Template has {$sectionsCount} sections\n";
    echo "✅ Section has {$blocksCount} blocks\n";
    
    // 6. Test the URL
    echo "6. Testing Visual Builder URL...\n";
    $url = "http://localhost/admin/visual-template-builder?template={$template->id}";
    echo "✅ URL: {$url}\n";
    
    echo "\n🎉 Test completed successfully!\n";
    echo "You can now test the Add Section functionality in the browser.\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
