<x-filament-panels::page>
    <div class="invoice-visual-builder" x-data="invoiceBuilder()">

        {{-- Main Header --}}
        <div class="mb-6 bg-white rounded-lg shadow p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h2 class="text-lg font-semibold text-gray-900">Invoice Visual Builder</h2>
                    @if($company)
                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">{{ $company->name }}</span>
                    @endif
                </div>

                <div class="flex items-center space-x-2">
                    <button type="button"
                            class="px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm"
                            wire:click="previewTemplate">
                        <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Preview
                    </button>
                </div>
            </div>
        </div>

        {{-- Section Tabs --}}
        <div class="mb-6 bg-white rounded-lg shadow">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex">
                    <button type="button"
                            class="py-4 px-6 border-b-2 font-medium text-sm"
                            :class="activeSection === 'header' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            @click="activeSection = 'header'">
                        Header Section
                    </button>
                    <button type="button"
                            class="py-4 px-6 border-b-2 font-medium text-sm"
                            :class="activeSection === 'body' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            @click="activeSection = 'body'">
                        Body Section
                    </button>
                    <button type="button"
                            class="py-4 px-6 border-b-2 font-medium text-sm"
                            :class="activeSection === 'footer' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            @click="activeSection = 'footer'">
                        Footer Section
                    </button>
                </nav>
            </div>
        </div>

        {{-- Header Section --}}
        <div x-show="activeSection === 'header'" class="space-y-4">
            @if($headerSection)
                {{-- Header Section Controls --}}
                <div class="bg-white rounded-lg shadow p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-4">
                            <h3 class="text-md font-medium text-gray-900">Header Section</h3>
                            <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                Table {{ $headerSection->rows }}x{{ $headerSection->columns }}
                            </span>
                        </div>

                        <div class="flex items-center space-x-2">
                            <button type="button"
                                    class="p-2 text-gray-400 hover:text-gray-600 rounded"
                                    @click="showSectionSettings = true"
                                    title="Section Settings">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </button>

                            <button type="button"
                                    class="p-2 text-red-400 hover:text-red-600 rounded"
                                    wire:click="deleteHeaderSection"
                                    wire:confirm="Are you sure you want to delete the header section?"
                                    title="Delete Section">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                {{-- Header Table Canvas --}}
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="p-6">
                        <div class="table-container border-2 border-gray-300 rounded-lg overflow-hidden">
                            <table class="w-full border-collapse">
                                @for($row = 0; $row < $headerSection->rows; $row++)
                                    <tr>
                                        @for($col = 0; $col < $headerSection->columns; $col++)
                                            @php
                                                $cell = $headerSection->getCellAt($row, $col);
                                            @endphp

                                            @if($cell)
                                                <td class="border border-gray-300 p-0 relative group min-h-[80px] min-w-[120px]"
                                                    rowspan="{{ $cell->rowspan }}"
                                                    colspan="{{ $cell->colspan }}"
                                                    x-data="{ cellId: {{ $cell->id }}, row: {{ $row }}, col: {{ $col }} }"
                                                    style="width: {{ $cell->width ?? 'auto' }}; height: {{ $cell->height ?? 'auto' }};">

                                                    {{-- Cell Content Area --}}
                                                    <div class="h-full min-h-[80px] p-3 cursor-pointer hover:bg-blue-50 transition-colors"
                                                         @click="selectCell(cellId, row, col)"
                                                         :class="{ 'bg-blue-100 ring-2 ring-blue-500': selectedCells.includes(cellId) }">

                                                        {{-- Cell Content --}}
                                                        <div class="h-full flex items-center justify-center">
                                                            @if($cell->content_type === 'text' && $cell->static_content)
                                                                <span class="text-sm">{{ $cell->static_content }}</span>
                                                            @elseif($cell->content_type === 'field' && $cell->field_path)
                                                                <span class="text-sm text-blue-600 italic">{{ $cell->field_path }}</span>
                                                            @elseif($cell->content_type === 'image')
                                                                <div class="flex items-center text-gray-500">
                                                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                    </svg>
                                                                    <span class="text-xs">{{ $cell->field_path ?: 'Image' }}</span>
                                                                </div>
                                                            @else
                                                                <div class="text-center text-gray-400">
                                                                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                                    </svg>
                                                                    <span class="text-xs">Click to add content</span>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>

                                                    {{-- Cell Controls (Show on Hover) --}}
                                                    <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                        <div class="flex space-x-1">
                                                            <button type="button"
                                                                    class="p-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                                                                    @click.stop="editCell(cellId)"
                                                                    title="Edit Cell">
                                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                                </svg>
                                                            </button>

                                                            <button type="button"
                                                                    class="p-1 bg-green-600 text-white rounded text-xs hover:bg-green-700"
                                                                    @click.stop="showCellControls = cellId"
                                                                    title="Cell Controls">
                                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4a1 1 0 011-1h4M4 16v4a1 1 0 001 1h4m8-16h4a1 1 0 011 1v4m-4 12h4a1 1 0 001-1v-4"></path>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>

                                                    {{-- Cell Position Info --}}
                                                    <div class="absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                        <span class="text-xs bg-gray-800 text-white px-1 py-0.5 rounded">
                                                            {{ $row }},{{ $col }}
                                                            @if($cell->rowspan > 1 || $cell->colspan > 1)
                                                                ({{ $cell->rowspan }}x{{ $cell->colspan }})
                                                            @endif
                                                        </span>
                                                    </div>
                                                </td>
                                            @endif
                                        @endfor
                                    </tr>
                                @endfor
                            </table>
                        </div>

                        {{-- Selection Actions --}}
                        <div class="mt-4 flex items-center space-x-2" x-show="selectedCells.length > 1">
                            <button type="button"
                                    class="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm"
                                    @click="mergeCells()">
                                <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4a1 1 0 011-1h4M4 16v4a1 1 0 001 1h4m8-16h4a1 1 0 011 1v4m-4 12h4a1 1 0 001-1v-4"></path>
                                </svg>
                                Merge Selected Cells
                            </button>

                            <button type="button"
                                    class="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
                                    @click="clearSelection()">
                                Clear Selection
                            </button>

                            <span class="text-sm text-gray-600" x-text="selectedCells.length + ' cells selected'"></span>
                        </div>
                    </div>
                </div>
            @else
                {{-- Empty State - Add Header Section --}}
                <div class="bg-white rounded-lg shadow p-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Create Header Section</h3>
                        <p class="text-gray-500 mb-4">Start building your invoice header with a table layout</p>
                        <button type="button"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                wire:click="createHeaderSection">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Header Section
                        </button>
                    </div>
                </div>
            @endif
        </div>

        {{-- Body Section (Placeholder) --}}
        <div x-show="activeSection === 'body'" class="space-y-4">
            <div class="bg-white rounded-lg shadow p-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Body Section</h3>
                    <p class="text-gray-500 mb-4">Body section will be implemented next</p>
                </div>
            </div>
        </div>

        {{-- Footer Section (Placeholder) --}}
        <div x-show="activeSection === 'footer'" class="space-y-4">
            <div class="bg-white rounded-lg shadow p-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Footer Section</h3>
                    <p class="text-gray-500 mb-4">Footer section will be implemented next</p>
                </div>
            </div>
        </div>

        {{-- Include Cell Editor Component --}}
        <x-invoice-builder.cell-editor />
    </div>

    <script>
        function invoiceBuilder() {
            return {
                selectedCells: [],
                showCellEditor: false,
                showSectionSettings: false,
                showCellControls: null,
                currentCellId: null,
                activeSection: 'header',

                selectCell(cellId, row, col) {
                    if (this.selectedCells.includes(cellId)) {
                        this.selectedCells = this.selectedCells.filter(id => id !== cellId);
                    } else {
                        this.selectedCells.push(cellId);
                    }
                },

                clearSelection() {
                    this.selectedCells = [];
                },

                editCell(cellId) {
                    this.currentCellId = cellId;
                    @this.call('loadCellEditor', cellId).then(() => {
                        this.showCellEditor = true;
                    });
                },

                mergeCells() {
                    if (this.selectedCells.length > 1) {
                        @this.call('mergeCells', this.selectedCells).then(() => {
                            this.clearSelection();
                        });
                    }
                }
            }
        }
    </script>
</x-filament-panels::page>
