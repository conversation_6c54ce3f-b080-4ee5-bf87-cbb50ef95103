{
	"invoice_no": "INV-0001",
	"invoice_date": "2025-05-29",
	"due_date": "2025-06-05",
	"currency": "IDR",
	"invoice_details": [
		{
			"description": "Service A",
			"quantity": 1,
			"unit": "pcs",
			"price": 1000,
			"sub_total": 1000
		},
		{
			"description": "Service B",
			"quantity": 2,
			"unit": "hrs",
			"price": 500
		}
	],
	"invoice_rate": 1,
	"booking_fee": 1000,
	"invoice_amount": 1000,
	"amount_inword": "Satu <PERSON> Rupiah",
	"company": {
		"name": "PT. Adorama",
		"address": "Jl. Raya Cibubur No. 123, Cibubur, Bekasi",
		"phone": "021-*********",
		"email": "<EMAIL>",
		"logo": "https://adorama.com/logo.png",
		"signature": "https://adorama.com/signature.png",
		"signature_name": "PT. Adorama"
	},

	"client": {
		"name": "PT. Client",
		"address": "Jl. Raya Cibubur No. 123, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>",
		"phone": "021-*********",
		"email": "<EMAIL>",
	},

	"bank": {
		"account_name": "PT. Adorama",
		"bank_name": "BCA",
		"account_number": "*********0",
		"bank_address": "Jl. Raya Cibubur No. 123, Cibubur, Bekasi",
		"swift": "BCAIDJ123",
		"routing_number": "*********0",
		"transit": "*********0",
		"tt_charge": "*********0",
		"iban": "*********0",
		"institution": "BCA",
		"bsb": "*********0",
		"branch_code": "*********0",
		"sort_code": "*********0",
		"branch_bank": "BCA",
		"aba": "*********0",
		"ifsc": "*********0",
		"custom_columns": [
			{
				"key": "reference_number",
				"type": "text",
				"value": "BANK-REF-2024-001"
			},
			{
				"key": "bank_code",
				"type": "text",
				"value": "*********0"
			}
		]
	}
}
