<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\InvoiceTemplateResource\Pages;
use App\Models\Company;
use App\Models\InvoiceTemplate;
use App\Services\FontService;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class InvoiceTemplateResource extends Resource
{
    protected static ?string $model = InvoiceTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Template Management';

    protected static ?string $navigationLabel = 'Invoice Templates';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Template Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $context, $state, Forms\Set $set) {
                                if ($context === 'create') {
                                    $set('description', 'Template for ' . $state);
                                }
                            }),

                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('type')
                            ->options([
                                'custom' => 'Custom Template',
                                'legacy' => 'Legacy Template',
                            ])
                            ->default('custom')
                            ->required(),

                        Forms\Components\Select::make('company_id')
                            ->label('Company')
                            ->options(Company::pluck('name', 'id'))
                            ->searchable()
                            ->nullable()
                            ->helperText('Leave empty for global template'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Template Configuration')
                    ->schema([
                        Forms\Components\Select::make('template_data.page_size')
                            ->label('Page Size')
                            ->options([
                                'legal' => 'Legal (8.5" x 14")',
                                'f4' => 'F4/Folio (210mm x 330mm)',
                            ])
                            ->default('legal')
                            ->required(),

                        Forms\Components\Select::make('template_data.orientation')
                            ->label('Orientation')
                            ->options([
                                'portrait' => 'Portrait',
                                'landscape' => 'Landscape',
                            ])
                            ->default('portrait')
                            ->required(),

                        Forms\Components\Select::make('template_data.font_family')
                            ->label('Font Family')
                            ->options(FontService::getSelectOptions())
                            ->default(FontService::getDefaultFont('invoice'))
                            ->required()
                            ->helperText('DejaVu fonts are recommended for best PDF compatibility'),

                        Forms\Components\TextInput::make('template_data.font_size')
                            ->label('Font Size (pt)')
                            ->numeric()
                            ->default(12)
                            ->minValue(8)
                            ->maxValue(18)
                            ->required(),

                        Forms\Components\ColorPicker::make('template_data.colors.primary')
                            ->label('Primary Color')
                            ->default('#000000'),

                        Forms\Components\ColorPicker::make('template_data.colors.secondary')
                            ->label('Secondary Color')
                            ->default('#666666'),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Margins')
                    ->schema([
                        Forms\Components\TextInput::make('template_data.margins.top')
                            ->label('Top (mm)')
                            ->numeric()
                            ->default(15)
                            ->minValue(5)
                            ->maxValue(50),

                        Forms\Components\TextInput::make('template_data.margins.right')
                            ->label('Right (mm)')
                            ->numeric()
                            ->default(15)
                            ->minValue(5)
                            ->maxValue(50),

                        Forms\Components\TextInput::make('template_data.margins.bottom')
                            ->label('Bottom (mm)')
                            ->numeric()
                            ->default(15)
                            ->minValue(5)
                            ->maxValue(50),

                        Forms\Components\TextInput::make('template_data.margins.left')
                            ->label('Left (mm)')
                            ->numeric()
                            ->default(15)
                            ->minValue(5)
                            ->maxValue(50),
                    ])
                    ->columns(4),

                Forms\Components\Hidden::make('created_by')
                    ->default(Auth::user()->id),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'custom' => 'success',
                        'legacy' => 'warning',
                    }),

                Tables\Columns\TextColumn::make('company.name')
                    ->label('Company')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Global Template'),

                Tables\Columns\TextColumn::make('template_data.page_size')
                    ->label('Page Size')
                    ->badge()
                    ->color('info'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'custom' => 'Custom',
                        'legacy' => 'Legacy',
                    ]),

                Tables\Filters\SelectFilter::make('company_id')
                    ->label('Company')
                    ->options(Company::pluck('name', 'id'))
                    ->searchable(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('builder')
                    ->label('Builder')
                    ->icon('heroicon-o-paint-brush')
                    ->color('info')
                    ->url(fn (InvoiceTemplate $record): string =>
                        route('filament.admin.pages.template-builder') . '?template=' . $record->id
                    ),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoiceTemplates::route('/'),
            'create' => Pages\CreateInvoiceTemplate::route('/create'),
            'view' => Pages\ViewInvoiceTemplate::route('/{record}'),
            'edit' => Pages\EditInvoiceTemplate::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
