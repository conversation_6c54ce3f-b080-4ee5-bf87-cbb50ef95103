<?php

namespace App\Http\Controllers;

use App\Models\InvoiceTemplate;
use App\Models\TemplateComponent;
use App\Services\FontLoaderService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class TemplatePreviewController extends Controller
{
    /**
     * Show template preview
     */
    public function show(Request $request, ?InvoiceTemplate $template = null): View
    {
        // Get template data from request or database
        $templateData = $this->getTemplateData($request, $template);
        $sampleData = $this->getSampleInvoiceData();

        // Load fonts for preview
        $fonts = $this->loadRequiredFonts($templateData);

        return view('template.preview', [
            'template' => $template,
            'templateData' => $templateData,
            'sampleData' => $sampleData,
            'fonts' => $fonts,
            'pageSize' => $templateData['config']['page_size'] ?? 'legal',
        ]);
    }

    /**
     * Get template data from request or database
     */
    private function getTemplateData(Request $request, ?InvoiceTemplate $template): array
    {
        // If data is passed via URL parameter (from builder)
        if ($request->has('data')) {
            try {
                $data = json_decode(base64_decode($request->get('data')), true);
                if ($data) {
                    return $data;
                }
            } catch (\Exception) {
                // Fall back to template data
            }
        }

        // Use template data from database
        if ($template) {
            return [
                'sections' => $template->template_data['sections'] ?? [],
                'config' => $template->template_data ?? []
            ];
        }

        // Default empty template
        return [
            'sections' => [
                'header' => [],
                'body' => [],
                'footer' => []
            ],
            'config' => [
                'page_size' => 'legal',
                'orientation' => 'portrait',
                'font_family' => 'DejaVu Sans',
                'font_size' => 12,
            ]
        ];
    }

    /**
     * Get sample invoice data for preview
     */
    private function getSampleInvoiceData(): array
    {
        return [
            'invoice' => [
                'number' => 'INV-2024-001',
                'date' => now()->format('Y-m-d'),
                'due_date' => now()->addDays(30)->format('Y-m-d'),
                'subtotal' => 1500000,
                'tax' => 150000,
                'total' => 1650000,
                'currency' => 'IDR',
                'status' => 'pending',
            ],
            'company' => [
                'name' => 'PT. Sample Company',
                'address' => 'Jl. Sample Street No. 123<br>Jakarta 12345<br>Indonesia',
                'phone' => '+62 21 1234567',
                'email' => '<EMAIL>',
                'logo' => 'logo/sample-logo.png', // Use sample logo path instead of null
            ],
            'client' => [
                'name' => 'PT. Client Company',
                'address' => 'Jl. Client Street No. 456<br>Bandung 40123<br>Indonesia',
                'phone' => '+62 22 7654321',
                'email' => '<EMAIL>',
            ],
            'items' => [
                [
                    'description' => 'Web Development Services',
                    'quantity' => 1,
                    'price' => 800000,
                    'total' => 800000,
                ],
                [
                    'description' => 'UI/UX Design Services',
                    'quantity' => 1,
                    'price' => 500000,
                    'total' => 500000,
                ],
                [
                    'description' => 'Project Management',
                    'quantity' => 1,
                    'price' => 200000,
                    'total' => 200000,
                ],
            ],
            'bank' => [
                'name' => 'Bank Sample',
                'account_number' => '**********',
                'account_name' => 'PT. Sample Company',
                'swift_code' => 'SAMPIDJA',
                'custom_columns' => [
                    'branch' => [
                        'type' => 'text',
                        'value' => 'Jakarta Pusat'
                    ],
                    'notes' => [
                        'type' => 'textarea',
                        'value' => 'Please include invoice number in transfer description'
                    ]
                ]
            ],
            'totals' => [
                'subtotal_words' => 'Satu Juta Lima Ratus Ribu Rupiah',
                'total_words' => 'Satu Juta Enam Ratus Lima Puluh Ribu Rupiah',
            ]
        ];
    }

    /**
     * Load required fonts for template
     */
    private function loadRequiredFonts(array $templateData): array
    {
        $fontNames = [];

        // Get font from template config
        if (isset($templateData['config']['font_family'])) {
            $fontNames[] = $templateData['config']['font_family'];
        }

        // Get fonts from components (if they have font settings)
        foreach ($templateData['sections'] ?? [] as $section) {
            foreach ($section as $component) {
                if (isset($component['config']['font_family'])) {
                    $fontNames[] = $component['config']['font_family'];
                }
            }
        }

        // Remove duplicates and load fonts
        $fontNames = array_unique($fontNames);
        $fonts = FontLoaderService::getFontsForTemplate($fontNames);

        return [
            'fonts' => $fonts,
            'css_imports' => FontLoaderService::generateCssImports($fonts),
            'html_links' => FontLoaderService::generateHtmlLinks($fonts),
            'preload_links' => FontLoaderService::generatePreloadLinks($fonts),
        ];
    }

    /**
     * Render component HTML
     */
    public function renderComponent(array $component, array $data): string
    {
        $templateComponent = TemplateComponent::find($component['component_id']);

        if (!$templateComponent) {
            return '<div class="error">Component not found</div>';
        }

        try {
            // Get component template
            $template = $templateComponent->template_html;

            // Check if template exists
            if (empty($template)) {
                return '<div class="error">Component template is empty or missing</div>';
            }

            // Replace variables with actual data
            $html = $this->replaceVariables($template, $data, $component['config'] ?? []);

            return $html;
        } catch (\Exception $e) {
            return '<div class="error">Error rendering component: ' . $e->getMessage() . '</div>';
        }
    }

    /**
     * Replace template variables with actual data
     */
    private function replaceVariables(string $template, array $data, array $config): string
    {
        // Replace simple variables like {{invoice.number}}
        $template = preg_replace_callback('/\{\{([^}]+)\}\}/', function ($matches) use ($data) {
            $path = trim($matches[1]);
            return $this->getNestedValue($data, $path) ?? $matches[0];
        }, $template);

        // Replace config variables like {{config.alignment}}
        $template = preg_replace_callback('/\{\{config\.([^}]+)\}\}/', function ($matches) use ($config) {
            $key = trim($matches[1]);
            return $config[$key] ?? $matches[0];
        }, $template);

        return $template;
    }

    /**
     * Get nested array value using dot notation
     */
    private function getNestedValue(array $array, string $path)
    {
        $keys = explode('.', $path);
        $value = $array;

        foreach ($keys as $key) {
            if (is_array($value) && array_key_exists($key, $value)) {
                $value = $value[$key];
            } else {
                return null;
            }
        }

        return $value;
    }
}
