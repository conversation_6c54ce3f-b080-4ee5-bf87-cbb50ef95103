<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class TemplateLayout extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'company_id',
        'version',
        'layout_data',
        'settings',
        'is_active',
        'is_default',
        'page_size',
        'orientation',
        'font_family',
        'font_size',
        'created_by',
        'updated_by',
        'last_used_at',
    ];

    protected $casts = [
        'layout_data' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'font_size' => 'integer',
        'last_used_at' => 'datetime',
    ];

    protected $attributes = [
        'version' => 'v2',
        'page_size' => 'legal',
        'orientation' => 'portrait',
        'font_family' => 'DejaVu Sans',
        'font_size' => 12,
        'is_active' => true,
        'is_default' => false,
    ];

    // Relationships
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function tableSections(): HasMany
    {
        return $this->hasMany(TableSection::class)->orderBy('order');
    }

    public function headerSections(): HasMany
    {
        return $this->hasMany(TableSection::class)->where('section_type', 'header')->orderBy('order');
    }

    public function bodySections(): HasMany
    {
        return $this->hasMany(TableSection::class)->where('section_type', 'body')->orderBy('order');
    }

    public function footerSections(): HasMany
    {
        return $this->hasMany(TableSection::class)->where('section_type', 'footer')->orderBy('order');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeVersion($query, $version = 'v2')
    {
        return $query->where('version', $version);
    }

    // Methods
    public function duplicate(string $newName = null): self
    {
        $newTemplate = $this->replicate();
        $newTemplate->name = $newName ?? $this->name . ' (Copy)';
        $newTemplate->is_default = false;
        $newTemplate->created_by = auth()->id();
        $newTemplate->updated_by = null;
        $newTemplate->last_used_at = null;
        $newTemplate->save();

        // Duplicate sections and blocks
        foreach ($this->sections as $section) {
            $section->duplicateToTemplate($newTemplate);
        }

        return $newTemplate;
    }

    public function markAsUsed(): void
    {
        $this->update(['last_used_at' => now()]);
    }

    public function setAsDefault(): void
    {
        // Remove default from other templates for this company
        if ($this->company_id) {
            static::where('company_id', $this->company_id)
                ->where('id', '!=', $this->id)
                ->update(['is_default' => false]);
        }

        $this->update(['is_default' => true]);
    }

    public function getPreviewData(): array
    {
        return [
            'template' => $this->toArray(),
            'sections' => $this->sections->load('blocks')->toArray(),
            'settings' => $this->settings ?? [],
        ];
    }

    public function exportConfiguration(): array
    {
        return [
            'template' => $this->only([
                'name', 'description', 'version', 'layout_data', 'settings',
                'page_size', 'orientation', 'font_family', 'font_size'
            ]),
            'sections' => $this->sections->map(function ($section) {
                return $section->exportConfiguration();
            })->toArray(),
        ];
    }

    public static function importConfiguration(array $config, ?int $companyId = null): self
    {
        $template = static::create(array_merge($config['template'], [
            'company_id' => $companyId,
            'created_by' => auth()->id(),
        ]));

        foreach ($config['sections'] ?? [] as $sectionConfig) {
            TableSection::importConfiguration($sectionConfig, $template->id);
        }

        return $template;
    }
}
