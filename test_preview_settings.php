<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🧪 Testing Visual Template Builder Preview Settings\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // 1. Find a template with blocks
    echo "1. Finding template with blocks...\n";
    $template = App\Models\TemplateLayout::with(['sections.blocks'])->first();
    
    if (!$template) {
        echo "   ❌ No template found\n";
        exit(1);
    }
    
    echo "   ✅ Template found: {$template->name} (ID: {$template->id})\n";
    echo "   📊 Sections: " . $template->sections->count() . "\n";
    
    $totalBlocks = $template->sections->sum(function($section) {
        return $section->blocks->count();
    });
    echo "   📦 Total blocks: {$totalBlocks}\n\n";
    
    // 2. Test block with configuration
    echo "2. Testing block configuration...\n";
    $blockWithConfig = null;
    
    foreach ($template->sections as $section) {
        foreach ($section->blocks as $block) {
            if ($block->configuration && !empty($block->configuration)) {
                $blockWithConfig = $block;
                break 2;
            }
        }
    }
    
    if ($blockWithConfig) {
        echo "   ✅ Block with configuration found: {$blockWithConfig->block_type} (ID: {$blockWithConfig->id})\n";
        echo "   ⚙️  Configuration: " . json_encode($blockWithConfig->configuration, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "   ⚠️  No block with configuration found, creating test configuration...\n";
        
        $firstBlock = $template->sections->first()->blocks->first();
        if ($firstBlock) {
            $firstBlock->update([
                'configuration' => [
                    'alignment' => 'center',
                    'font_size' => 16,
                    'font_weight' => 'bold',
                    'font_color' => '#ff0000'
                ]
            ]);
            
            $blockWithConfig = $firstBlock->fresh();
            echo "   ✅ Test configuration created for block: {$blockWithConfig->block_type} (ID: {$blockWithConfig->id})\n";
            echo "   ⚙️  Configuration: " . json_encode($blockWithConfig->configuration, JSON_PRETTY_PRINT) . "\n";
        }
    }
    
    echo "\n";
    
    // 3. Test block with content_data (image settings)
    echo "3. Testing image block content_data...\n";
    $imageBlock = null;
    
    foreach ($template->sections as $section) {
        foreach ($section->blocks as $block) {
            if (in_array($block->block_type, ['company_logo', 'comp_logo', 'logo'])) {
                $imageBlock = $block;
                break 2;
            }
        }
    }
    
    if ($imageBlock) {
        echo "   ✅ Image block found: {$imageBlock->block_type} (ID: {$imageBlock->id})\n";
        echo "   🖼️  Content data: " . json_encode($imageBlock->content_data, JSON_PRETTY_PRINT) . "\n";
        
        // Update with test image settings
        $imageBlock->update([
            'content_data' => array_merge($imageBlock->content_data ?? [], [
                'width' => '150px',
                'height' => '100px',
                'object_fit' => 'cover',
                'alt_text' => 'Test Logo'
            ])
        ]);
        
        $imageBlock = $imageBlock->fresh();
        echo "   ✅ Test image settings applied\n";
        echo "   🖼️  Updated content data: " . json_encode($imageBlock->content_data, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "   ⚠️  No image block found\n";
    }
    
    echo "\n";
    
    // 4. Test section column layout
    echo "4. Testing section column layout...\n";
    $multiColumnSection = $template->sections->where('column_count', '>', 1)->first();
    
    if ($multiColumnSection) {
        echo "   ✅ Multi-column section found: {$multiColumnSection->section_type} (ID: {$multiColumnSection->id})\n";
        echo "   📊 Columns: {$multiColumnSection->column_count}\n";
        echo "   🎨 Layout: {$multiColumnSection->column_layout}\n";
        echo "   📐 Widths: " . json_encode($multiColumnSection->column_widths) . "\n";
    } else {
        echo "   ⚠️  No multi-column section found, creating test section...\n";
        
        $firstSection = $template->sections->first();
        if ($firstSection) {
            $firstSection->update([
                'column_count' => 2,
                'column_layout' => 'custom',
                'column_widths' => [60, 40]
            ]);
            
            $multiColumnSection = $firstSection->fresh();
            echo "   ✅ Test multi-column section created\n";
            echo "   📊 Columns: {$multiColumnSection->column_count}\n";
            echo "   🎨 Layout: {$multiColumnSection->column_layout}\n";
            echo "   📐 Widths: " . json_encode($multiColumnSection->column_widths) . "\n";
        }
    }
    
    echo "\n";
    
    // 5. Test rendering with sample data
    echo "5. Testing HTML rendering...\n";
    
    $sampleData = [
        'company' => [
            'name' => 'Test Company Ltd.',
            'address' => '123 Test Street, Test City',
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'logo' => 'logos/test-logo.png'
        ],
        'client' => [
            'name' => 'Test Client Inc.',
            'address' => '456 Client Avenue, Client City'
        ],
        'invoice' => [
            'number' => 'INV-2024-001',
            'date' => '2024-01-15',
            'due_date' => '2024-02-15',
            'total' => 1500000
        ]
    ];
    
    if ($blockWithConfig) {
        echo "   🧪 Testing block with configuration...\n";
        $html = $blockWithConfig->renderHtml($sampleData);
        echo "   📄 Rendered HTML length: " . strlen($html) . " characters\n";
        
        // Check if styles are applied
        if (strpos($html, 'style=') !== false) {
            echo "   ✅ Inline styles found in HTML\n";
            
            // Extract style attribute
            preg_match('/style="([^"]*)"/', $html, $matches);
            if (isset($matches[1])) {
                echo "   🎨 Applied styles: {$matches[1]}\n";
            }
        } else {
            echo "   ❌ No inline styles found in HTML\n";
        }
        
        echo "   📄 HTML preview: " . substr(strip_tags($html), 0, 100) . "...\n";
    }
    
    if ($imageBlock) {
        echo "   🧪 Testing image block...\n";
        $html = $imageBlock->renderHtml($sampleData);
        echo "   📄 Rendered HTML length: " . strlen($html) . " characters\n";
        
        // Check if image settings are applied
        if (strpos($html, 'width:') !== false || strpos($html, 'object-fit:') !== false) {
            echo "   ✅ Image styles found in HTML\n";
            
            // Extract style attribute
            preg_match('/style="([^"]*)"/', $html, $matches);
            if (isset($matches[1])) {
                echo "   🖼️  Applied image styles: {$matches[1]}\n";
            }
        } else {
            echo "   ❌ No image styles found in HTML\n";
        }
    }
    
    if ($multiColumnSection) {
        echo "   🧪 Testing section layout...\n";
        $html = $multiColumnSection->renderHtml($sampleData);
        echo "   📄 Rendered HTML length: " . strlen($html) . " characters\n";
        
        // Check if column layout is applied
        if (strpos($html, 'grid-template-columns:') !== false || strpos($html, 'display: flex') !== false) {
            echo "   ✅ Column layout styles found in HTML\n";
        } else {
            echo "   ❌ No column layout styles found in HTML\n";
        }
    }
    
    echo "\n";
    
    // 6. Generate preview URL
    echo "6. Testing preview URL...\n";
    $previewUrl = route('visual-template.preview', [
        'template' => $template->id,
        'mode' => 'desktop',
        'size' => 'legal'
    ]);
    
    echo "   🔗 Preview URL: {$previewUrl}\n";
    echo "   💡 Open this URL in browser to see the preview\n";
    
    echo "\n✅ All tests completed!\n";
    echo "\n📋 Summary:\n";
    echo "   - Template: {$template->name}\n";
    echo "   - Sections: " . $template->sections->count() . "\n";
    echo "   - Total blocks: {$totalBlocks}\n";
    echo "   - Block with config: " . ($blockWithConfig ? "✅ ID {$blockWithConfig->id}" : "❌ None") . "\n";
    echo "   - Image block: " . ($imageBlock ? "✅ ID {$imageBlock->id}" : "❌ None") . "\n";
    echo "   - Multi-column section: " . ($multiColumnSection ? "✅ ID {$multiColumnSection->id}" : "❌ None") . "\n";
    echo "   - Preview URL: {$previewUrl}\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "🔍 Trace:\n" . $e->getTraceAsString() . "\n";
}
