<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_components', function (Blueprint $table) {
            $table->id();
            $table->enum('component_type', [
                'kop',         // Header/Kop surat components
                'billto',      // Bill to components
                'table',       // Table components
                'inwords',     // In words components
                'bankinfo',    // Bank info components
                'text',        // Text components
                'image',       // Image components
                'custom'       // Custom components
            ]);
            $table->string('component_name');
            $table->text('component_code'); // HTML/Blade code untuk component
            $table->string('preview_image')->nullable(); // path ke preview thumbnail
            $table->boolean('is_system')->default(false); // true untuk komponen bawaan
            $table->json('component_config')->nullable(); // JSON configuration
            $table->text('description')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['component_type', 'is_system']);
            $table->index('component_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_components');
    }
};
