<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContentBlock extends Model
{
    use HasFactory;

    protected $fillable = [
        'layout_section_id',
        'block_type',
        'block_name',
        'column_position',
        'column_span',
        'content_data',
        'field_mapping',
        'styling',
        'configuration',
        'order',
        'is_active',
        'is_required',
        'alignment',
        'font_size',
        'font_weight',
        'font_style',
        'text_color',
        'background_color',
        'border',
        'padding',
        'margin',
    ];

    protected $casts = [
        'content_data' => 'array',
        'field_mapping' => 'array',
        'styling' => 'array',
        'configuration' => 'array',
        'is_active' => 'boolean',
        'is_required' => 'boolean',
        'column_position' => 'integer',
        'column_span' => 'integer',
        'order' => 'integer',
        'font_size' => 'integer',
        'border' => 'array',
        'padding' => 'array',
        'margin' => 'array',
    ];

    protected $attributes = [
        'column_position' => 1,
        'column_span' => 1,
        'order' => 0,
        'is_active' => true,
        'is_required' => false,
        'alignment' => 'left',
    ];

    // Relationships
    public function layoutSection(): BelongsTo
    {
        return $this->belongsTo(LayoutSection::class);
    }

    // Note: BlockTemplate relationship will be added when BlockTemplate model is created

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('block_type', $type);
    }

    public function scopeInColumn($query, int $column)
    {
        return $query->where('column_position', $column);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('column_position')->orderBy('order');
    }

    // Methods
    public function duplicateToSection(LayoutSection $section): self
    {
        $newBlock = $this->replicate();
        $newBlock->layout_section_id = $section->id;
        $newBlock->save();

        return $newBlock;
    }

    public function moveToColumn(int $column, int $order = null): void
    {
        $this->update([
            'column_position' => $column,
            'order' => $order ?? $this->layoutSection->getNextBlockOrder($column),
        ]);
    }

    public function updateStyling(array $styling): void
    {
        $currentStyling = $this->styling ?? [];
        $this->update(['styling' => array_merge($currentStyling, $styling)]);
    }

    public function exportConfiguration(): array
    {
        return $this->only([
            'block_type', 'block_name', 'column_position', 'column_span',
            'content_data', 'field_mapping', 'styling', 'configuration',
            'order', 'is_required', 'alignment', 'font_size', 'font_weight',
            'font_style', 'text_color', 'background_color', 'border', 'padding', 'margin'
        ]);
    }

    public static function importConfiguration(array $config, int $layoutSectionId): self
    {
        return static::create(array_merge($config, [
            'layout_section_id' => $layoutSectionId,
        ]));
    }

    public function renderHtml(array $data = []): string
    {
        $html = '<div class="content-block block-' . $this->block_type . '"';

        // Add styling
        $styles = $this->buildInlineStyles();
        if (!empty($styles)) {
            $html .= ' style="' . implode('; ', $styles) . '"';
        }

        $html .= '>';

        // Render content based on block type
        switch ($this->block_type) {
            // Field-based blocks
            case 'logo':
            case 'company_logo':
            case 'comp_logo':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'company.logo', 'image');
                break;
            case 'company_name':
            case 'comp_name':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'company.name', 'text');
                break;
            case 'company_address':
            case 'comp_address':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'company.address', 'html');
                break;
            case 'company_phone':
            case 'comp_phone':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'company.phone', 'text');
                break;
            case 'company_email':
            case 'comp_email':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'company.email', 'email');
                break;
            case 'invoice_number':
            case 'inv_number':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'invoice.invoice_no', 'text');
                break;
            case 'invoice_date':
            case 'inv_date':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'invoice.invoice_date', 'date');
                break;
            case 'invoice_due_date':
            case 'inv_due':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'invoice.invoice_due', 'date');
                break;
            case 'client_name':
            case 'customer_name':
            case 'cust_name':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'customer.name', 'text');
                break;
            case 'client_address':
            case 'customer_address':
            case 'cust_address':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? 'customer.address', 'html');
                break;
            case 'spacer':
                $html .= $this->renderSpacer();
                break;
            case 'custom_text':
                $html .= $this->renderCustomText();
                break;
            case 'free_text':
                $html .= $this->renderFreeText();
                break;
            case 'company_info_combined':
                $html .= $this->renderMultiFieldContent($data, 'company_info');
                break;
            case 'contact_info_combined':
                $html .= $this->renderMultiFieldContent($data, 'contact_info');
                break;
            case 'invoice_details_combined':
                $html .= $this->renderMultiFieldContent($data, 'invoice_details');
                break;
            case 'client_info_combined':
                $html .= $this->renderMultiFieldContent($data, 'client_info');
                break;
            case 'horizontal_line':
                $html .= $this->renderHorizontalLine();
                break;
            case 'company_signature':
                $html .= $this->renderCompanySignature($data);
                break;
            case 'company_website':
            case 'company_fax':
            case 'customer_phone':
            case 'customer_email':
            case 'invoice_amount':
            case 'invoice_remarks':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? '', $this->content_data['format'] ?? 'text');
                break;
            case 'company_signature_image':
                $html .= $this->renderSignatureImage($data);
                break;
            case 'signature_name':
                $html .= $this->renderSignatureName($data);
                break;

            // Combined blocks
            case 'company_contact_combined':
                $html .= $this->renderCombinedFields($data, 'company_contact');
                break;
            case 'company_header_combined':
                $html .= $this->renderCompanyHeader($data);
                break;
            case 'invoice_info_combined':
                $html .= $this->renderCombinedFields($data, 'invoice_info');
                break;
            case 'customer_info_combined':
                $html .= $this->renderCombinedFields($data, 'customer_info');
                break;

            // New individual field components (shortened block_type names)
            // Note: comp_logo already handled above in line 149
            case 'comp_name':
            case 'comp_address':
            case 'comp_phone':
            case 'comp_email':
            case 'comp_website':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? '', $this->content_data['format'] ?? 'text');
                break;
            case 'inv_number':
            case 'inv_date':
            case 'inv_due':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? '', $this->content_data['format'] ?? 'text');
                break;
            case 'cust_name':
            case 'cust_address':
                $html .= $this->renderFieldBasedContent($data, $this->content_data['field_path'] ?? '', $this->content_data['format'] ?? 'text');
                break;

            // Legacy blocks (for backward compatibility)
            case 'text':
                $html .= $this->renderText($data);
                break;
            case 'field':
                $html .= $this->renderField($data);
                break;
            case 'table':
                $html .= $this->renderTable($data);
                break;
            case 'company_info':
                $html .= $this->renderCompanyInfo($data);
                break;
            case 'client_info':
                $html .= $this->renderClientInfo($data);
                break;
            case 'invoice_details':
                $html .= $this->renderInvoiceDetails($data);
                break;
            case 'bank_info':
                $html .= $this->renderBankInfo($data);
                break;
            case 'signature':
                $html .= $this->renderSignature($data);
                break;
            case 'terms':
                $html .= $this->renderTerms($data);
                break;
            case 'calculations':
                $html .= $this->renderCalculations($data);
                break;
            case 'custom':
                $html .= $this->renderCustom($data);
                break;
            default:
                $html .= '<div>Unknown block type: ' . $this->block_type . '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    private function buildInlineStyles(): array
    {
        $styles = [];

        // Get configuration settings (from Visual Builder)
        $config = $this->configuration ?? [];

        // Check alignment (from configuration or direct field)
        $alignment = $config['alignment'] ?? $this->alignment;
        if ($alignment && $alignment !== 'left') {
            $styles[] = 'text-align: ' . $alignment;
        }

        // Check font size (from configuration or direct field)
        $fontSize = $config['font_size'] ?? $this->font_size;
        if ($fontSize) {
            $styles[] = 'font-size: ' . $fontSize . 'pt';
        }

        // Check font weight (from configuration or direct field)
        $fontWeight = $config['font_weight'] ?? $this->font_weight;
        if ($fontWeight && $fontWeight !== 'normal') {
            $styles[] = 'font-weight: ' . $fontWeight;
        }

        // Check font style (from configuration or direct field)
        $fontStyle = $config['font_style'] ?? $this->font_style;
        if ($fontStyle && $fontStyle !== 'normal') {
            $styles[] = 'font-style: ' . $fontStyle;
        }

        // Check text color (from configuration or direct field)
        $textColor = $config['font_color'] ?? $this->text_color;
        if ($textColor) {
            $styles[] = 'color: ' . $textColor;
        }

        // Check background color (from configuration or direct field)
        $backgroundColor = $config['background_color'] ?? $this->background_color;
        if ($backgroundColor) {
            $styles[] = 'background-color: ' . $backgroundColor;
        }

        // Check padding (from configuration or direct field)
        $padding = $config['padding'] ?? $this->padding;
        if ($padding) {
            $styles[] = sprintf('padding: %dpx %dpx %dpx %dpx',
                $padding['top'] ?? 0, $padding['right'] ?? 0,
                $padding['bottom'] ?? 0, $padding['left'] ?? 0);
        }

        // Check margin (from configuration or direct field)
        $margin = $config['margin'] ?? $this->margin;
        if ($margin) {
            $styles[] = sprintf('margin: %dpx %dpx %dpx %dpx',
                $margin['top'] ?? 0, $margin['right'] ?? 0,
                $margin['bottom'] ?? 0, $margin['left'] ?? 0);
        }

        return $styles;
    }

    private function renderLogo(array $data): string
    {
        $logoUrl = $this->content_data['logo_url'] ?? $data['company']['logo'] ?? '';
        $altText = $this->content_data['alt_text'] ?? 'Company Logo';
        $width = $this->content_data['width'] ?? 'auto';
        $height = $this->content_data['height'] ?? 'auto';

        if (empty($logoUrl)) {
            return '<div class="logo-placeholder">[Company Logo]</div>';
        }

        return sprintf('<img src="%s" alt="%s" style="width: %s; height: %s;">',
            $logoUrl, $altText, $width, $height);
    }

    private function renderText(array $data): string
    {
        return $this->content_data['text'] ?? '';
    }

    private function renderField(array $data): string
    {
        $fieldPath = $this->field_mapping['field'] ?? '';
        $format = $this->field_mapping['format'] ?? 'text';
        $prefix = $this->content_data['prefix'] ?? '';
        $suffix = $this->content_data['suffix'] ?? '';

        $value = data_get($data, $fieldPath, '');

        // Apply formatting
        switch ($format) {
            case 'currency':
                $value = 'Rp ' . number_format($value, 0, ',', '.');
                break;
            case 'date':
                $value = date('d/m/Y', strtotime($value));
                break;
            case 'datetime':
                $value = date('d/m/Y H:i', strtotime($value));
                break;
        }

        return $prefix . $value . $suffix;
    }

    private function renderTable(array $data): string
    {
        // This will be implemented with advanced table builder
        return '<table class="invoice-table"><tr><td>Table content will be rendered here</td></tr></table>';
    }

    private function renderCompanyInfo(array $data): string
    {
        $company = $data['company'] ?? [];
        $fields = $this->configuration['fields'] ?? ['name', 'address', 'phone', 'email'];

        $html = '';
        foreach ($fields as $field) {
            if (isset($company[$field]) && !empty($company[$field])) {
                $html .= '<div class="company-' . $field . '">' . $company[$field] . '</div>';
            }
        }

        return $html;
    }

    private function renderClientInfo(array $data): string
    {
        $client = $data['client'] ?? [];
        $fields = $this->configuration['fields'] ?? ['name', 'address', 'phone', 'email'];

        $html = '<strong>Bill To:</strong><br>';
        foreach ($fields as $field) {
            if (isset($client[$field]) && !empty($client[$field])) {
                $html .= '<div class="client-' . $field . '">' . $client[$field] . '</div>';
            }
        }

        return $html;
    }

    private function renderInvoiceDetails(array $data): string
    {
        $invoice = $data['invoice'] ?? [];
        $fields = $this->configuration['fields'] ?? ['number', 'date', 'due_date'];

        $html = '';
        foreach ($fields as $field) {
            if (isset($invoice[$field]) && !empty($invoice[$field])) {
                $label = ucfirst(str_replace('_', ' ', $field));
                $value = $invoice[$field];

                if (in_array($field, ['date', 'due_date'])) {
                    $value = date('d/m/Y', strtotime($value));
                }

                $html .= '<div class="invoice-' . $field . '"><strong>' . $label . ':</strong> ' . $value . '</div>';
            }
        }

        return $html;
    }

    private function renderBankInfo(array $data): string
    {
        $bank = $data['bank'] ?? [];
        $layout = $this->configuration['layout'] ?? 'simple';

        $html = '<strong>Bank Details:</strong><br>';
        $html .= 'Bank: ' . ($bank['name'] ?? 'Sample Bank') . '<br>';
        $html .= 'Account: ' . ($bank['account_number'] ?? '**********') . '<br>';
        $html .= 'Account Name: ' . ($bank['account_name'] ?? 'Company Name');

        return $html;
    }

    private function renderSignature(array $data): string
    {
        $signatureUrl = $this->content_data['signature_url'] ?? '';
        $signatureName = $this->content_data['signature_name'] ?? 'Authorized Signature';

        $html = '<div class="signature-block">';
        if ($signatureUrl) {
            $html .= '<img src="' . $signatureUrl . '" alt="Signature" style="max-width: 200px; max-height: 100px;">';
        } else {
            $html .= '<div style="height: 60px; border-bottom: 1px solid #000; margin-bottom: 5px;"></div>';
        }
        $html .= '<div class="signature-name">' . $signatureName . '</div>';
        $html .= '</div>';

        return $html;
    }

    private function renderTerms(array $data): string
    {
        return $this->content_data['terms'] ?? 'Terms and conditions will be displayed here.';
    }

    private function renderCalculations(array $data): string
    {
        $totals = $data['totals'] ?? [];
        $showSubtotal = $this->configuration['show_subtotal'] ?? true;
        $showTax = $this->configuration['show_tax'] ?? true;
        $showTotal = $this->configuration['show_total'] ?? true;

        $html = '<div class="calculations">';

        if ($showSubtotal && isset($totals['subtotal'])) {
            $html .= '<div class="calc-line"><span>Subtotal:</span> <span>Rp ' . number_format($totals['subtotal'], 0, ',', '.') . '</span></div>';
        }

        if ($showTax && isset($totals['tax'])) {
            $html .= '<div class="calc-line"><span>Tax:</span> <span>Rp ' . number_format($totals['tax'], 0, ',', '.') . '</span></div>';
        }

        if ($showTotal && isset($totals['total'])) {
            $html .= '<div class="calc-line total"><span><strong>Total:</strong></span> <span><strong>Rp ' . number_format($totals['total'], 0, ',', '.') . '</strong></span></div>';
        }

        $html .= '</div>';

        return $html;
    }

    private function renderCustom(array $data): string
    {
        return $this->content_data['html'] ?? '';
    }

    /**
     * Render field-based content with proper formatting (compatible with existing templates)
     */
    private function renderFieldBasedContent(array $data, string $fieldPath, string $format = 'text'): string
    {
        $value = data_get($data, $fieldPath, '');
        $prefix = $this->content_data['prefix'] ?? '';
        $suffix = $this->content_data['suffix'] ?? '';

        // Handle empty values
        if (empty($value)) {
            return '<span class="text-gray-400 italic">[' . str_replace('.', ' ', $fieldPath) . ']</span>';
        }

        // Apply formatting based on type (compatible with existing templates)
        switch ($format) {
            case 'image':
                $width = $this->content_data['width'] ?? '100px';
                $height = $this->content_data['height'] ?? 'auto';
                $objectFit = $this->content_data['object_fit'] ?? 'contain';
                $altText = $this->content_data['alt_text'] ?? 'Image';

                // If logo is null/empty, return empty (no image)
                if (empty($value)) {
                    return '';
                }

                // Simple fix: remove leading slash from value to avoid double slash
                if (strpos($value, 'http') === 0) {
                    $imageSrc = $value;
                } else {
                    // Remove leading slash if exists to prevent double slash
                    $cleanValue = ltrim($value, '/');
                    $imageSrc = url('storage/' . $cleanValue);
                }

                return sprintf('<img src="%s" alt="%s" style="width: %s; height: %s; object-fit: %s;" class="mx-2 profile-image">',
                    $imageSrc, $altText, $width, $height, $objectFit);

            case 'email':
                return $prefix . '<a href="mailto:' . $value . '">' . $value . '</a>' . $suffix;

            case 'date':
                $dateFormat = $this->content_data['date_format'] ?? 'd/m/Y';
                $formattedDate = date($dateFormat, strtotime($value));
                return $prefix . $formattedDate . $suffix;

            case 'html':
                // Handle HTML content (like customer address)
                $allowHtml = $this->content_data['allow_html'] ?? false;
                $stripTags = $this->content_data['strip_tags'] ?? false;

                if ($stripTags) {
                    $value = strip_tags($value);
                } elseif (!$allowHtml) {
                    $value = htmlspecialchars($value);
                }

                return $prefix . $value . $suffix;

            case 'currency':
                $symbol = data_get($data, 'invoice.currency.symbol', 'Rp');
                $formattedValue = $symbol . ' ' . number_format($value, 0, ',', '.');
                return $prefix . $formattedValue . $suffix;

            case 'url':
                if (empty($value)) {
                    return $prefix . '[Website URL]' . $suffix;
                }
                // Add http:// if not present
                $url = (strpos($value, 'http') !== 0) ? 'http://' . $value : $value;
                return $prefix . '<a href="' . $url . '" target="_blank">' . $value . '</a>' . $suffix;

            case 'text':
            default:
                // Handle text color and heading size (like company name)
                $textColorPath = $this->content_data['text_color_path'] ?? null;
                $headingSizePath = $this->content_data['heading_size_path'] ?? null;

                $textColor = $textColorPath ? data_get($data, $textColorPath, '') : '';
                $headingSize = $headingSizePath ? data_get($data, $headingSizePath, '') : '';

                $style = '';
                if ($textColor) {
                    $style .= "color: {$textColor};";
                }

                $class = '';
                if ($headingSize) {
                    $class = $headingSize . ' fw-bold';
                } elseif (isset($this->content_data['style_class'])) {
                    $class = $this->content_data['style_class'];
                }

                $html = $prefix . $value . $suffix;

                if ($class || $style) {
                    $html = sprintf('<span class="%s" style="%s">%s</span>', $class, $style, $html);
                }

                return $html;
        }
    }

    /**
     * Render spacer block
     */
    private function renderSpacer(): string
    {
        $height = $this->content_data['height'] ?? '20px';
        return '<div style="height: ' . $height . ';"></div>';
    }

    /**
     * Render custom text block
     */
    private function renderCustomText(): string
    {
        return $this->content_data['text'] ?? 'Custom text content';
    }

    /**
     * Render free text block (editable)
     */
    private function renderFreeText(): string
    {
        $text = $this->content_data['text'] ?? 'Enter your custom text here';
        $allowHtml = $this->content_data['allow_html'] ?? true;

        if ($allowHtml) {
            return $text;
        } else {
            return htmlspecialchars($text);
        }
    }

    /**
     * Render multi-field content blocks (compatible with existing template structure)
     */
    private function renderMultiFieldContent(array $data, string $blockType): string
    {
        $fields = $this->content_data['fields'] ?? [];
        $layout = $this->content_data['layout'] ?? 'vertical';
        $templateStyle = $this->content_data['template_style'] ?? 'default';

        if (empty($fields)) {
            return '<span class="text-gray-400 italic">[Multi-field block - no fields configured]</span>';
        }

        $fieldOutputs = [];

        foreach ($fields as $field) {
            $fieldPath = $field['path'] ?? '';
            $format = $field['format'] ?? 'text';
            $prefix = $field['prefix'] ?? '';
            $suffix = $field['suffix'] ?? '';
            $style = $field['style'] ?? '';
            $styleClass = $field['style_class'] ?? '';
            $condition = $field['condition'] ?? 'always';

            if (empty($fieldPath)) {
                continue;
            }

            $value = data_get($data, $fieldPath, '');

            // Handle conditions
            if ($condition === 'if_not_empty' && empty($value)) {
                continue;
            }

            if (empty($value) && $condition !== 'always') {
                continue;
            }

            // Format value based on type
            $formattedValue = $this->formatMultiFieldValue($value, $format, $field, $data);

            if (empty($formattedValue) && $condition !== 'always') {
                continue;
            }

            $fieldHtml = $prefix . $formattedValue . $suffix;

            // Apply styling
            if ($styleClass || $style) {
                $fieldHtml = sprintf('<span class="%s" style="%s">%s</span>', $styleClass, $style, $fieldHtml);
            }

            $fieldOutputs[] = $fieldHtml;
        }

        if (empty($fieldOutputs)) {
            return '<span class="text-gray-400 italic">[' . str_replace('_', ' ', $blockType) . ' - no data]</span>';
        }

        // Render based on template style (like existing templates)
        return $this->renderMultiFieldByStyle($fieldOutputs, $templateStyle, $layout);
    }

    /**
     * Render multi-field content by template style
     */
    private function renderMultiFieldByStyle(array $fieldOutputs, string $templateStyle, string $layout): string
    {
        switch ($templateStyle) {
            case 'kop':
                // Like template/kop/*.blade.php
                $html = '<div>';
                foreach ($fieldOutputs as $index => $output) {
                    if ($index === 0) {
                        // First field is company name with heading
                        $html .= '<div class="' . (data_get($this->content_data, 'fields.0.default_heading', 'h3')) . ' fw-bold">' . $output . '</div>';
                    } else {
                        // Other fields in list
                        if ($index === 1) {
                            $html .= '<ul class="list-unstyled mt-0" style="font-size: 11pt">';
                        }
                        $html .= '<li' . ($index === 1 ? ' class="mb-0"' : '') . '>' . $output . '</li>';
                    }
                }
                if (count($fieldOutputs) > 1) {
                    $html .= '</ul>';
                }
                $html .= '</div>';
                return $html;

            case 'billto':
                // Like template/billto/*.blade.php
                $html = '<div>';
                foreach ($fieldOutputs as $index => $output) {
                    if ($index === 0) {
                        $html .= '<span class="d-flex justify-content-between fw-bold">' . $output . '</span>';
                    } else {
                        $html .= '<span class="d-flex justify-content-between">' . $output . '</span>';
                    }
                }
                $html .= '</div>';
                return $html;

            case 'signature':
                // Company signature style
                $html = '<div class="text-center">';
                foreach ($fieldOutputs as $output) {
                    $html .= $output;
                }
                $html .= '</div>';
                return $html;

            default:
                // Default vertical or horizontal layout
                $separator = $layout === 'horizontal' ? ' | ' : '<br>';
                return implode($separator, $fieldOutputs);
        }
    }

    /**
     * Format field value for multi-field blocks
     */
    private function formatMultiFieldValue($value, string $format, array $field, array $data): string
    {
        switch ($format) {
            case 'date':
                $dateFormat = $field['date_format'] ?? 'd/m/Y';
                return date($dateFormat, strtotime($value));

            case 'email':
                return '<a href="mailto:' . $value . '">' . $value . '</a>';

            case 'html':
                $allowHtml = $field['allow_html'] ?? false;
                $stripTags = $field['strip_tags'] ?? false;

                if ($stripTags) {
                    return strip_tags($value);
                } elseif (!$allowHtml) {
                    return htmlspecialchars($value);
                }
                return $value;

            case 'text':
            default:
                // Handle dynamic styling from data
                $textColorPath = $field['text_color_path'] ?? null;
                $headingSizePath = $field['heading_size_path'] ?? null;

                if ($textColorPath || $headingSizePath) {
                    $textColor = $textColorPath ? data_get($data, $textColorPath, '') : '';
                    $headingSize = $headingSizePath ? data_get($data, $headingSizePath, $field['default_heading'] ?? '') : '';

                    $style = $textColor ? "color: {$textColor};" : '';
                    $class = $headingSize ? $headingSize . ' fw-bold' : '';

                    if ($class || $style) {
                        return sprintf('<span class="%s" style="%s">%s</span>', $class, $style, $value);
                    }
                }

                return $value;
        }
    }

    /**
     * Format field value based on type
     */
    private function formatFieldValue($value, string $format): string
    {
        switch ($format) {
            case 'email':
                return '<a href="mailto:' . $value . '">' . $value . '</a>';
            case 'url':
                return '<a href="' . $value . '" target="_blank">' . $value . '</a>';
            case 'date':
                return date('d/m/Y', strtotime($value));
            case 'currency':
                return 'Rp ' . number_format($value, 0, ',', '.');
            case 'text':
            default:
                return $value;
        }
    }

    /**
     * Render horizontal line
     */
    private function renderHorizontalLine(): string
    {
        $thickness = $this->content_data['thickness'] ?? '1px';
        $color = $this->content_data['color'] ?? '#cccccc';
        $style = $this->content_data['style'] ?? 'solid';

        return '<hr style="border: none; border-top: ' . $thickness . ' ' . $style . ' ' . $color . '; margin: 10px 0;">';
    }

    /**
     * Render company signature (compatible with existing template)
     */
    private function renderCompanySignature(array $data): string
    {
        $signaturePath = $this->content_data['signature_path'] ?? 'company.signature';
        $signatureNamePath = $this->content_data['signature_name_path'] ?? 'company.signature_name';
        $maxHeight = $this->content_data['max_height'] ?? '5rem';

        $signature = data_get($data, $signaturePath, '');
        $signatureName = data_get($data, $signatureNamePath, '');

        $html = '<div class="text-center">';

        // Signature image
        if ($signature) {
            // Handle Laravel storage path properly
            $signatureUrl = '';

            if (strpos($signature, 'http') === 0) {
                $signatureUrl = $signature;
            } elseif (strpos($signature, '/storage/') === 0) {
                $signatureUrl = url($signature);
            } elseif (strpos($signature, 'storage/') === 0) {
                $signatureUrl = url('/' . $signature);
            } else {
                $cleanValue = ltrim($signature, '/');
                $signatureUrl = url('/storage/' . $cleanValue);
            }

            $html .= '<img src="' . $signatureUrl . '" class="mx-2 mt-1 profile-image ml-auto" style="max-height:' . $maxHeight . '"><br>';
        }

        // Signature name
        if ($signatureName) {
            $html .= '<u>' . $signatureName . '</u>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Render signature image only
     */
    private function renderSignatureImage(array $data): string
    {
        $fieldPath = $this->content_data['field_path'] ?? 'company.signature';
        $maxHeight = $this->content_data['max_height'] ?? '5rem';

        $signature = data_get($data, $fieldPath, '');

        if (empty($signature)) {
            return '<div class="text-center"><div class="w-20 h-16 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500">[SIGNATURE]</div></div>';
        }

        // Use same logic as existing Blade templates
        $signatureUrl = '';

        if (strpos($signature, 'http') === 0) {
            $signatureUrl = $signature;
        } else {
            $signatureUrl = url('storage/' . $signature);
        }

        return '<div class="text-center"><img src="' . $signatureUrl . '" class="mx-2 mt-1 profile-image ml-auto" style="max-height:' . $maxHeight . '"></div>';
    }

    /**
     * Render signature name only
     */
    private function renderSignatureName(array $data): string
    {
        $fieldPath = $this->content_data['field_path'] ?? 'company.signature_name';
        $prefix = $this->content_data['prefix'] ?? '';
        $suffix = $this->content_data['suffix'] ?? '';
        $underline = $this->content_data['underline'] ?? true;

        $signatureName = data_get($data, $fieldPath, '');

        if (empty($signatureName)) {
            return '<span class="text-gray-400 italic">[Signature Name]</span>';
        }

        $html = $prefix . $signatureName . $suffix;

        if ($underline) {
            $html = '<u>' . $html . '</u>';
        }

        return $html;
    }

    /**
     * Render combined fields (multiple fields in one block)
     */
    private function renderCombinedFields(array $data, string $blockType): string
    {
        $fields = $this->content_data['fields'] ?? [];
        $layout = $this->content_data['layout'] ?? 'vertical';
        $spacing = $this->content_data['spacing'] ?? 'normal';

        $html = '<div class="combined-fields ' . $blockType . '">';

        // Sort fields by order
        uasort($fields, function($a, $b) {
            return ($a['order'] ?? 0) <=> ($b['order'] ?? 0);
        });

        foreach ($fields as $fieldKey => $fieldConfig) {
            $fieldPath = $fieldConfig['field_path'] ?? '';
            $format = $fieldConfig['format'] ?? 'text';
            $prefix = $fieldConfig['prefix'] ?? '';
            $suffix = $fieldConfig['suffix'] ?? '';
            $style = $fieldConfig['style'] ?? '';

            $value = data_get($data, $fieldPath, '');

            // Skip empty fields unless it's a required field
            if (empty($value) && !($fieldConfig['show_empty'] ?? false)) {
                continue;
            }

            $fieldHtml = $this->formatFieldValue($value, $format, $data, $prefix, $suffix);

            // Apply field-specific styling
            if ($style === 'bold') {
                $fieldHtml = '<strong>' . $fieldHtml . '</strong>';
            }

            // Add field wrapper
            $fieldClass = 'field-' . $fieldKey;
            if ($spacing === 'compact') {
                $fieldClass .= ' mb-1';
            } else {
                $fieldClass .= ' mb-2';
            }

            $html .= '<div class="' . $fieldClass . '">' . $fieldHtml . '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Render company header (logo + name side by side)
     */
    private function renderCompanyHeader(array $data): string
    {
        $layout = $this->content_data['layout'] ?? 'horizontal';
        $spacing = $this->content_data['spacing'] ?? '15px';

        $logoConfig = $this->content_data['logo'] ?? [];
        $nameConfig = $this->content_data['name'] ?? [];

        $html = '<div class="company-header flex items-center" style="gap: ' . $spacing . ';">';

        // Render logo
        $logoPath = $logoConfig['field_path'] ?? 'company.logo';
        $logoValue = data_get($data, $logoPath, '');

        if (!empty($logoValue)) {
            $logoWidth = $logoConfig['width'] ?? '80px';
            $logoHeight = $logoConfig['height'] ?? 'auto';

            // Use same image rendering logic as individual logo blocks
            $logoSrc = '';
            if (strpos($logoValue, 'http') === 0) {
                $logoSrc = $logoValue;
            } else {
                $cleanValue = ltrim($logoValue, '/');
                $logoSrc = url('storage/' . $cleanValue);
            }

            $html .= '<div class="company-logo">';
            $html .= '<img src="' . $logoSrc . '" alt="Company Logo" style="width: ' . $logoWidth . '; height: ' . $logoHeight . ';" class="profile-image">';
            $html .= '</div>';
        }

        // Render company name
        $namePath = $nameConfig['field_path'] ?? 'company.name';
        $nameValue = data_get($data, $namePath, '');

        if (!empty($nameValue)) {
            $textColorPath = $nameConfig['text_color_path'] ?? null;
            $headingSizePath = $nameConfig['heading_size_path'] ?? null;

            $textColor = $textColorPath ? data_get($data, $textColorPath, '') : '';
            $headingSize = $headingSizePath ? data_get($data, $headingSizePath, '') : '';

            $style = '';
            if ($textColor) {
                $style .= "color: {$textColor};";
            }

            $class = 'company-name font-bold';
            if ($headingSize) {
                $class .= ' ' . $headingSize;
            } else {
                $class .= ' text-lg';
            }

            $html .= '<div class="' . $class . '"';
            if ($style) {
                $html .= ' style="' . $style . '"';
            }
            $html .= '>' . $nameValue . '</div>';
        }

        $html .= '</div>';

        return $html;
    }
}
