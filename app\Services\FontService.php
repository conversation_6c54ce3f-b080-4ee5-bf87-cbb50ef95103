<?php

namespace App\Services;

use App\Models\Font;

class FontService
{
    /**
     * Get available fonts for PDF generation
     */
    public static function getAvailableFonts(): array
    {
        return [
            // DejaVu Fonts (Recommended for DomPDF)
            'DejaVu Sans' => [
                'name' => 'DejaVu Sans (Recommended)',
                'category' => 'sans-serif',
                'pdf_compatible' => true,
                'unicode_support' => true,
                'description' => 'Clean, modern sans-serif font with excellent Unicode support'
            ],
            'DejaVu Serif' => [
                'name' => 'DejaVu Serif',
                'category' => 'serif',
                'pdf_compatible' => true,
                'unicode_support' => true,
                'description' => 'Traditional serif font for formal documents'
            ],
            'DejaVu Sans Mono' => [
                'name' => 'DejaVu Sans Mono',
                'category' => 'monospace',
                'pdf_compatible' => true,
                'unicode_support' => true,
                'description' => 'Monospace font ideal for numbers and codes'
            ],

            // Standard System Fonts - Sans Serif
            'Arial' => [
                'name' => 'Arial',
                'category' => 'sans-serif',
                'pdf_compatible' => true,
                'unicode_support' => false,
                'description' => 'Widely supported sans-serif font'
            ],
            'Helvetica' => [
                'name' => 'Helvetica',
                'category' => 'sans-serif',
                'pdf_compatible' => true,
                'unicode_support' => false,
                'description' => 'Clean, modern sans-serif font'
            ],
            'Tahoma' => [
                'name' => 'Tahoma',
                'category' => 'sans-serif',
                'pdf_compatible' => true,
                'unicode_support' => false,
                'description' => 'Readable sans-serif font with good screen display'
            ],
            'Verdana' => [
                'name' => 'Verdana',
                'category' => 'sans-serif',
                'pdf_compatible' => true,
                'unicode_support' => false,
                'description' => 'Highly readable sans-serif font'
            ],

            // Standard System Fonts - Serif
            'Times New Roman' => [
                'name' => 'Times New Roman',
                'category' => 'serif',
                'pdf_compatible' => true,
                'unicode_support' => false,
                'description' => 'Classic serif font for professional documents'
            ],
            'Times' => [
                'name' => 'Times',
                'category' => 'serif',
                'pdf_compatible' => true,
                'unicode_support' => false,
                'description' => 'Traditional serif font'
            ],
            'Georgia' => [
                'name' => 'Georgia',
                'category' => 'serif',
                'pdf_compatible' => true,
                'unicode_support' => false,
                'description' => 'Readable serif font designed for screen'
            ],

            // Generic Fallbacks
            'sans-serif' => [
                'name' => 'Sans Serif (Generic)',
                'category' => 'sans-serif',
                'pdf_compatible' => true,
                'unicode_support' => true,
                'description' => 'System default sans-serif font'
            ],
            'serif' => [
                'name' => 'Serif (Generic)',
                'category' => 'serif',
                'pdf_compatible' => true,
                'unicode_support' => true,
                'description' => 'System default serif font'
            ],
            'monospace' => [
                'name' => 'Monospace (Generic)',
                'category' => 'monospace',
                'pdf_compatible' => true,
                'unicode_support' => true,
                'description' => 'System default monospace font'
            ],
        ];
    }

    /**
     * Get font options for Filament select component (Database + Static)
     */
    public static function getSelectOptions(): array
    {
        $options = [];

        // Group 1: System Fonts (From Database - No URL)
        $systemFonts = Font::whereNull('source')->get();
        if ($systemFonts->isNotEmpty()) {
            $options['--- System Fonts (PDF Compatible) ---'] = '--- System Fonts (PDF Compatible) ---';
            foreach ($systemFonts as $font) {
                $options[$font->name] = $font->name . ' (System)';
            }
        }

        // Group 2: Google Fonts (From Database - With URL)
        $googleFonts = Font::where('source', 'like', '%fonts.googleapis.com%')->get();
        if ($googleFonts->isNotEmpty()) {
            $options['--- Google Fonts (Preview) ---'] = '--- Google Fonts (Preview) ---';
            foreach ($googleFonts as $font) {
                $options[$font->name] = $font->name . ' (Google)';
            }
        }

        // Group 3: Generic Fallbacks (From Database)
        $genericFonts = Font::whereIn('name', ['sans-serif', 'serif'])->get();
        if ($genericFonts->isNotEmpty()) {
            $options['--- Generic Fallbacks ---'] = '--- Generic Fallbacks ---';
            foreach ($genericFonts as $font) {
                $options[$font->name] = $font->name . ' (Generic)';
            }
        }

        return $options;
    }

    /**
     * Get font options for existing Company form (Font Model only)
     */
    public static function getDatabaseFontOptions(): array
    {
        return Font::all()->mapWithKeys(function ($font) {
            return [$font->id => $font->name];
        })->toArray();
    }

    /**
     * Get recommended fonts for PDF
     */
    public static function getRecommendedFonts(): array
    {
        return array_filter(self::getAvailableFonts(), function ($font) {
            return $font['pdf_compatible'] && $font['unicode_support'];
        });
    }

    /**
     * Get font by category
     */
    public static function getFontsByCategory(string $category): array
    {
        return array_filter(self::getAvailableFonts(), function ($font) use ($category) {
            return $font['category'] === $category;
        });
    }

    /**
     * Validate if font is suitable for PDF generation
     */
    public static function isPdfCompatible(string $fontFamily): bool
    {
        $fonts = self::getAvailableFonts();
        return isset($fonts[$fontFamily]) && $fonts[$fontFamily]['pdf_compatible'];
    }

    /**
     * Get font CSS for preview
     */
    public static function getFontCss(string $fontFamily): string
    {
        // Map font families to CSS font stacks
        $fontStacks = [
            // DejaVu Fonts
            'DejaVu Sans' => '"DejaVu Sans", Arial, Helvetica, sans-serif',
            'DejaVu Serif' => '"DejaVu Serif", "Times New Roman", Times, serif',
            'DejaVu Sans Mono' => '"DejaVu Sans Mono", "Courier New", monospace',

            // System Sans-Serif Fonts
            'Arial' => 'Arial, Helvetica, sans-serif',
            'Helvetica' => 'Helvetica, Arial, sans-serif',
            'Tahoma' => 'Tahoma, Arial, sans-serif',
            'Verdana' => 'Verdana, Arial, sans-serif',

            // System Serif Fonts
            'Times New Roman' => '"Times New Roman", Times, serif',
            'Times' => 'Times, "Times New Roman", serif',
            'Georgia' => 'Georgia, "Times New Roman", serif',

            // Google Fonts (with fallbacks)
            'Roboto' => 'Roboto, Arial, sans-serif',
            'Open Sans' => '"Open Sans", Arial, sans-serif',
            'Lato' => 'Lato, Arial, sans-serif',
            'Montserrat' => 'Montserrat, Arial, sans-serif',
            'Source Sans Pro' => '"Source Sans Pro", Arial, sans-serif',
            'Nunito' => 'Nunito, Arial, sans-serif',
            'Ubuntu' => 'Ubuntu, Arial, sans-serif',
            'Raleway' => 'Raleway, Arial, sans-serif',
            'Poppins' => 'Poppins, Arial, sans-serif',
            'Inter' => 'Inter, Arial, sans-serif',

            'Playfair Display' => '"Playfair Display", Georgia, serif',
            'Merriweather' => 'Merriweather, Georgia, serif',
            'Lora' => 'Lora, Georgia, serif',
            'Source Serif Pro' => '"Source Serif Pro", Georgia, serif',
            'Crimson Text' => '"Crimson Text", Georgia, serif',
            'Libre Baskerville' => '"Libre Baskerville", Georgia, serif',
            'Cormorant Garamond' => '"Cormorant Garamond", Georgia, serif',
            'EB Garamond' => '"EB Garamond", Georgia, serif',

            // Generic Fallbacks
            'sans-serif' => 'sans-serif',
            'serif' => 'serif',
            'monospace' => 'monospace',
        ];

        return $fontStacks[$fontFamily] ?? 'sans-serif';
    }

    /**
     * Get default font for template type
     */
    public static function getDefaultFont(string $templateType = 'invoice'): string
    {
        $defaults = [
            'invoice' => 'DejaVu Sans',
            'receipt' => 'DejaVu Sans',
            'formal' => 'DejaVu Serif',
            'technical' => 'DejaVu Sans Mono',
        ];

        return $defaults[$templateType] ?? 'DejaVu Sans';
    }

    /**
     * Get font size recommendations
     */
    public static function getFontSizeRecommendations(): array
    {
        return [
            'small' => [
                'size' => 10,
                'description' => 'Small text, footnotes'
            ],
            'normal' => [
                'size' => 12,
                'description' => 'Standard body text'
            ],
            'large' => [
                'size' => 14,
                'description' => 'Headers, emphasis'
            ],
            'xlarge' => [
                'size' => 16,
                'description' => 'Main titles'
            ],
        ];
    }
}
