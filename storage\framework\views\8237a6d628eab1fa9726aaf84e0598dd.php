<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="invoice-visual-builder" x-data="invoiceBuilder()" x-init="init()">

        
        <div class="mb-6 bg-white rounded-lg shadow p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h2 class="text-lg font-semibold text-gray-900">Invoice Visual Builder</h2>
                    <span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">Method 3: Table-Based</span>
                    <!--[if BLOCK]><![endif]--><?php if($company): ?>
                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded"><?php echo e($company->name); ?></span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <div class="flex items-center space-x-2">
                    <button type="button"
                            class="px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm"
                            wire:click="previewTemplate">
                        <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Preview
                    </button>
                </div>
            </div>
        </div>

        
        <div class="space-y-4">
            <!--[if BLOCK]><![endif]--><?php if($headerSection): ?>
                
                <div class="bg-white rounded-lg shadow p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-4">
                            <h3 class="text-md font-medium text-gray-900">Header Section</h3>
                            <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                Table <?php echo e($headerSection->rows); ?>x<?php echo e($headerSection->columns); ?>

                            </span>
                        </div>

                        <div class="flex items-center space-x-2">
                            <button type="button"
                                    class="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                                    wire:click="addRow">
                                <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Row
                            </button>

                            <button type="button"
                                    class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                                    wire:click="addColumn">
                                <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Column
                            </button>

                            <button type="button"
                                    class="p-2 text-gray-400 hover:text-gray-600 rounded"
                                    @click="showSectionSettings = true"
                                    title="Section Settings">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </button>

                            <button type="button"
                                    class="p-2 text-red-400 hover:text-red-600 rounded"
                                    wire:click="deleteHeaderSection"
                                    wire:confirm="Are you sure you want to delete the header section?"
                                    title="Delete Section">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="p-6">
                        <div class="table-container border-2 border-gray-300 rounded-lg overflow-hidden">
                            <table class="w-full border-collapse">
                                <!--[if BLOCK]><![endif]--><?php for($row = 0; $row < $headerSection->rows; $row++): ?>
                                    <tr>
                                        <!--[if BLOCK]><![endif]--><?php for($col = 0; $col < $headerSection->columns; $col++): ?>
                                            <?php
                                                $cell = $headerSection->getCellAt($row, $col);
                                            ?>

                                            <!--[if BLOCK]><![endif]--><?php if($cell): ?>
                                                <td class="border border-gray-300 p-0 relative group min-h-[80px] min-w-[120px]"
                                                    rowspan="<?php echo e($cell->rowspan); ?>"
                                                    colspan="<?php echo e($cell->colspan); ?>"
                                                    x-data="{ cellId: <?php echo e($cell->id); ?>, row: <?php echo e($row); ?>, col: <?php echo e($col); ?> }"
                                                    style="width: <?php echo e($cell->width ?? 'auto'); ?>; height: <?php echo e($cell->height ?? 'auto'); ?>;">

                                                    
                                                    <div class="h-full min-h-[80px] p-3 cursor-pointer hover:bg-blue-50 transition-colors"
                                                         @click="selectCell(cellId, row, col)"
                                                         :class="{ 'bg-blue-100 ring-2 ring-blue-500': selectedCells.includes(cellId) }">

                                                        
                                                        <div class="h-full flex items-center justify-center">
                                                            <!--[if BLOCK]><![endif]--><?php if($cell->content_type === 'text' && $cell->static_content): ?>
                                                                <span class="text-sm"><?php echo e($cell->static_content); ?></span>
                                                            <?php elseif($cell->content_type === 'field' && $cell->field_path): ?>
                                                                <span class="text-sm text-blue-600 italic"><?php echo e($cell->field_path); ?></span>
                                                            <?php elseif($cell->content_type === 'image'): ?>
                                                                <div class="flex items-center text-gray-500">
                                                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                    </svg>
                                                                    <span class="text-xs"><?php echo e($cell->field_path ?: 'Image'); ?></span>
                                                                </div>
                                                            <?php else: ?>
                                                                <div class="text-center text-gray-400 cursor-pointer hover:text-blue-600 transition-colors"
                                                                     @click="openBlockSelector(cellId)">
                                                                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                                    </svg>
                                                                    <span class="text-xs">Click to add content</span>
                                                                </div>
                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                        </div>
                                                    </div>

                                                    
                                                    <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                        <div class="flex space-x-1">
                                                            <button type="button"
                                                                    class="p-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                                                                    @click.stop="editCell(cellId)"
                                                                    title="Edit Cell">
                                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                                </svg>
                                                            </button>

                                                            <button type="button"
                                                                    class="p-1 bg-green-600 text-white rounded text-xs hover:bg-green-700"
                                                                    @click.stop="showCellControls = cellId"
                                                                    title="Cell Controls">
                                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4a1 1 0 011-1h4M4 16v4a1 1 0 001 1h4m8-16h4a1 1 0 011 1v4m-4 12h4a1 1 0 001-1v-4"></path>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>

                                                    
                                                    <div class="absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                        <span class="text-xs bg-gray-800 text-white px-1 py-0.5 rounded">
                                                            <?php echo e($row); ?>,<?php echo e($col); ?>

                                                            <!--[if BLOCK]><![endif]--><?php if($cell->rowspan > 1 || $cell->colspan > 1): ?>
                                                                (<?php echo e($cell->rowspan); ?>x<?php echo e($cell->colspan); ?>)
                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                        </span>
                                                    </div>
                                                </td>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                    </tr>
                                <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                            </table>
                        </div>

                        
                        <div class="mt-4 flex items-center space-x-2" x-show="selectedCells.length > 1">
                            <button type="button"
                                    class="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm"
                                    @click="mergeCells()">
                                <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4a1 1 0 011-1h4M4 16v4a1 1 0 001 1h4m8-16h4a1 1 0 011 1v4m-4 12h4a1 1 0 001-1v-4"></path>
                                </svg>
                                Merge Selected Cells
                            </button>

                            <button type="button"
                                    class="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
                                    @click="clearSelection()">
                                Clear Selection
                            </button>

                            <span class="text-sm text-gray-600" x-text="selectedCells.length + ' cells selected'"></span>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                
                <div class="bg-white rounded-lg shadow p-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Create Header Section</h3>
                        <p class="text-gray-500 mb-4">Start building your invoice header with a table layout</p>
                        <button type="button"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                wire:click="createHeaderSection">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Header Section
                        </button>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        
        <div x-show="showBlockSelector"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95"
             class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-75"
             @click="if ($event.target === $event.currentTarget) showBlockSelector = false"
             @keydown.escape.window="showBlockSelector = false"
             x-trap.inert.noscroll="showBlockSelector"
             style="display: none;"
             x-cloak>

            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden"
                 @click.stop>
                
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Add Content to Cell</h3>
                        <button type="button"
                                class="p-2 text-gray-400 hover:text-gray-600 rounded"
                                @click="showBlockSelector = false">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

                        
                        <div class="category-group">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Company Information</h4>
                            <div class="space-y-2">
                                <div class="block-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                     @click="addContentToCell('field', 'company.name')">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Company Name</div>
                                            <div class="text-xs text-gray-500">Display company name</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="block-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                     @click="addContentToCell('field', 'company.address')">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Company Address</div>
                                            <div class="text-xs text-gray-500">Display company address</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="block-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                     @click="addContentToCell('image', 'company.logo')">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Company Logo</div>
                                            <div class="text-xs text-gray-500">Display company logo</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        
                        <div class="category-group">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Invoice Information</h4>
                            <div class="space-y-2">
                                <div class="block-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                     @click="addContentToCell('field', 'invoice_no')">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Invoice Number</div>
                                            <div class="text-xs text-gray-500">Display invoice number</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="block-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                     @click="addContentToCell('field', 'invoice_date')">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Invoice Date</div>
                                            <div class="text-xs text-gray-500">Display invoice date</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        
                        <div class="category-group">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Custom Content</h4>
                            <div class="space-y-2">
                                <div class="block-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                     @click="addContentToCell('text', 'INVOICE')">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                                        </svg>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Custom Text</div>
                                            <div class="text-xs text-gray-500">Add custom text content</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal99024701c08649002d6bf35e8e1a8638 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal99024701c08649002d6bf35e8e1a8638 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.invoice-builder.cell-editor','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('invoice-builder.cell-editor'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal99024701c08649002d6bf35e8e1a8638)): ?>
<?php $attributes = $__attributesOriginal99024701c08649002d6bf35e8e1a8638; ?>
<?php unset($__attributesOriginal99024701c08649002d6bf35e8e1a8638); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal99024701c08649002d6bf35e8e1a8638)): ?>
<?php $component = $__componentOriginal99024701c08649002d6bf35e8e1a8638; ?>
<?php unset($__componentOriginal99024701c08649002d6bf35e8e1a8638); ?>
<?php endif; ?>
    </div>

    <script>
        function invoiceBuilder() {
            return {
                selectedCells: [],
                showCellEditor: false,
                showBlockSelector: false,
                showSectionSettings: false,
                showCellControls: null,
                currentCellId: null,

                selectCell(cellId, row, col) {
                    if (this.selectedCells.includes(cellId)) {
                        this.selectedCells = this.selectedCells.filter(id => id !== cellId);
                    } else {
                        this.selectedCells.push(cellId);
                    }
                },

                clearSelection() {
                    this.selectedCells = [];
                },

                // Open block selector modal (like method 2.0)
                openBlockSelector(cellId) {
                    console.log('Opening block selector for cell:', cellId);
                    this.currentCellId = cellId;
                    this.showBlockSelector = true;
                },

                // Add content to cell (like method 2.0)
                addContentToCell(contentType, value) {
                    console.log('Adding content to cell:', this.currentCellId, contentType, value);

                    if (!this.currentCellId) {
                        console.error('No cell selected');
                        return;
                    }

                    // Call Livewire method to update cell content
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('updateCellContent', this.currentCellId, contentType, value).then(() => {
                        console.log('✅ Cell content updated successfully');
                        this.showBlockSelector = false;
                        this.currentCellId = null;
                    }).catch((error) => {
                        console.error('❌ Failed to update cell content:', error);
                    });
                },

                editCell(cellId) {
                    this.currentCellId = cellId;
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('loadCellEditor', cellId).then(() => {
                        this.showCellEditor = true;
                    });
                },

                mergeCells() {
                    if (this.selectedCells.length > 1) {
                        window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('mergeCells', this.selectedCells).then(() => {
                            this.clearSelection();
                        });
                    }
                },

                // Initialize like method 2.0
                init() {
                    console.log('🚀 Invoice Visual Builder v3.0 - Table-Based System initialized');
                    console.log('📋 Features: Click to Add + Table Layout + Auto-save');

                    // Listen for Livewire events
                    this.$wire.on('cell-updated', (data) => {
                        console.log('✅ Cell auto-saved:', data);
                    });

                    this.$wire.on('cell-update-failed', (data) => {
                        console.error('❌ Cell auto-save failed:', data);
                    });

                    // Add keyboard shortcuts
                    document.addEventListener('keydown', (e) => {
                        // ESC to close modals
                        if (e.key === 'Escape') {
                            if (this.showBlockSelector) {
                                this.showBlockSelector = false;
                            }
                            if (this.showCellEditor) {
                                this.showCellEditor = false;
                            }
                        }
                    });
                }
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\sites\starterkit\web_starter\resources\views/filament/admin/pages/invoice-visual-builder.blade.php ENDPATH**/ ?>