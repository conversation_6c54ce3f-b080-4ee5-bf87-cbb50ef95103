<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="invoice-visual-builder" x-data="invoiceBuilder()">
        
        
        <div class="mb-6 bg-white rounded-lg shadow p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h2 class="text-lg font-semibold text-gray-900">Invoice Visual Builder</h2>
                    <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">Header Section</span>
                    <!--[if BLOCK]><![endif]--><?php if($company): ?>
                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded"><?php echo e($company->name); ?></span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                
                <div class="flex items-center space-x-2">
                    
                    <button type="button"
                            class="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                            wire:click="addRow">
                        <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Row
                    </button>
                    
                    
                    <button type="button"
                            class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                            wire:click="addColumn">
                        <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Column
                    </button>
                    
                    
                    <button type="button"
                            class="px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm"
                            wire:click="previewTemplate">
                        <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Preview
                    </button>
                </div>
            </div>
        </div>

        
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-sm font-medium text-gray-700">Header Table Layout</h3>
                <p class="text-xs text-gray-500 mt-1">Click cells to edit content, drag to select multiple cells for merging</p>
            </div>
            
            <div class="p-6">
                <!--[if BLOCK]><![endif]--><?php if($headerSection): ?>
                    <div class="table-container border border-gray-300 rounded-lg overflow-hidden">
                        <table class="w-full border-collapse">
                            <!--[if BLOCK]><![endif]--><?php for($row = 0; $row < $headerSection->rows; $row++): ?>
                                <tr>
                                    <!--[if BLOCK]><![endif]--><?php for($col = 0; $col < $headerSection->columns; $col++): ?>
                                        <?php
                                            $cell = $headerSection->getCellAt($row, $col);
                                        ?>
                                        
                                        <!--[if BLOCK]><![endif]--><?php if($cell): ?>
                                            <td class="border border-gray-300 p-2 min-h-[60px] relative group cursor-pointer hover:bg-blue-50 transition-colors"
                                                rowspan="<?php echo e($cell->rowspan); ?>"
                                                colspan="<?php echo e($cell->colspan); ?>"
                                                x-data="{ cellId: <?php echo e($cell->id); ?>, row: <?php echo e($row); ?>, col: <?php echo e($col); ?> }"
                                                @click="selectCell(cellId, row, col)"
                                                @dblclick="editCell(cellId)"
                                                :class="{ 'bg-blue-100 ring-2 ring-blue-500': selectedCells.includes(cellId) }">
                                                
                                                
                                                <div class="min-h-[40px] flex items-center">
                                                    <!--[if BLOCK]><![endif]--><?php switch($cell->content_type):
                                                        case ('text'): ?>
                                                            <span class="text-sm"><?php echo e($cell->static_content ?: 'Click to edit'); ?></span>
                                                            <?php break; ?>
                                                        <?php case ('field'): ?>
                                                            <span class="text-sm text-blue-600 italic"><?php echo e($cell->field_path ?: 'Select field'); ?></span>
                                                            <?php break; ?>
                                                        <?php case ('image'): ?>
                                                            <div class="flex items-center text-gray-500">
                                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                </svg>
                                                                <span class="text-xs"><?php echo e($cell->field_path ?: 'Image'); ?></span>
                                                            </div>
                                                            <?php break; ?>
                                                        <?php default: ?>
                                                            <span class="text-sm text-gray-400"><?php echo e(ucfirst($cell->content_type)); ?></span>
                                                    <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                </div>
                                                
                                                
                                                <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <span class="text-xs bg-gray-800 text-white px-1 py-0.5 rounded">
                                                        <?php echo e($row); ?>,<?php echo e($col); ?>

                                                        <!--[if BLOCK]><![endif]--><?php if($cell->rowspan > 1 || $cell->colspan > 1): ?>
                                                            (<?php echo e($cell->rowspan); ?>x<?php echo e($cell->colspan); ?>)
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </span>
                                                </div>
                                                
                                                
                                                <div class="absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <div class="flex space-x-1">
                                                        <button type="button"
                                                                class="p-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                                                                @click.stop="editCell(cellId)"
                                                                title="Edit Cell">
                                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                            </svg>
                                                        </button>
                                                        
                                                        <!--[if BLOCK]><![endif]--><?php if($cell->rowspan > 1 || $cell->colspan > 1): ?>
                                                            <button type="button"
                                                                    class="p-1 bg-orange-600 text-white rounded text-xs hover:bg-orange-700"
                                                                    wire:click="splitCell(<?php echo e($row); ?>, <?php echo e($col); ?>)"
                                                                    title="Split Cell">
                                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                                                </svg>
                                                            </button>
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </div>
                                                </div>
                                            </td>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                </tr>
                            <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                        </table>
                    </div>
                    
                    
                    <div class="mt-4 flex items-center space-x-2" x-show="selectedCells.length > 1">
                        <button type="button"
                                class="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm"
                                @click="mergeCells()">
                            <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4a1 1 0 011-1h4M4 16v4a1 1 0 001 1h4m8-16h4a1 1 0 011 1v4m-4 12h4a1 1 0 001-1v-4"></path>
                            </svg>
                            Merge Selected Cells
                        </button>
                        
                        <button type="button"
                                class="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
                                @click="clearSelection()">
                            Clear Selection
                        </button>
                        
                        <span class="text-sm text-gray-600" x-text="selectedCells.length + ' cells selected'"></span>
                    </div>
                <?php else: ?>
                    
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Create Header Table</h3>
                        <p class="text-gray-500 mb-4">Start building your invoice header layout with a table structure</p>
                        <button type="button"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                wire:click="createHeaderSection">
                            Create Header Table
                        </button>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>

        
        <div x-show="showCellEditor"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95"
             class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-75"
             @click="if ($event.target === $event.currentTarget) showCellEditor = false"
             @keydown.escape.window="showCellEditor = false"
             x-trap.inert.noscroll="showCellEditor"
             style="display: none;"
             x-cloak>

            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden"
                 @click.stop>
                
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Edit Cell Content</h3>
                        <button type="button"
                                class="p-2 text-gray-400 hover:text-gray-600 rounded"
                                @click="showCellEditor = false">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                
                <div class="p-4 overflow-y-auto max-h-96">
                    <div class="space-y-4">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Content Type</label>
                            <select wire:model.live="cellEditor.content_type" 
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="text">Static Text</option>
                                <option value="field">Dynamic Field</option>
                                <option value="image">Image</option>
                            </select>
                        </div>

                        
                        <div x-show="$wire.cellEditor.content_type === 'text'">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Text Content</label>
                            <textarea wire:model.live="cellEditor.static_content"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                      rows="3"
                                      placeholder="Enter your text content"></textarea>
                        </div>

                        <div x-show="$wire.cellEditor.content_type === 'field'">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Field Path</label>
                            <select wire:model.live="cellEditor.field_path"
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">Select field...</option>
                                <option value="company.name">Company Name</option>
                                <option value="company.address">Company Address</option>
                                <option value="company.phone">Company Phone</option>
                                <option value="company.email">Company Email</option>
                                <option value="invoice_no">Invoice Number</option>
                                <option value="invoice_date">Invoice Date</option>
                                <option value="due_date">Due Date</option>
                                <option value="client.name">Client Name</option>
                                <option value="client.address">Client Address</option>
                            </select>
                        </div>

                        <div x-show="$wire.cellEditor.content_type === 'image'">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Image Source</label>
                            <select wire:model.live="cellEditor.field_path"
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">Select image field...</option>
                                <option value="company.logo">Company Logo</option>
                                <option value="company.signature">Company Signature</option>
                            </select>
                        </div>

                        
                        <div class="border-t pt-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Cell Styling</h4>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Text Align</label>
                                    <select wire:model.live="cellEditor.text_align"
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="left">Left</option>
                                        <option value="center">Center</option>
                                        <option value="right">Right</option>
                                        <option value="justify">Justify</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Vertical Align</label>
                                    <select wire:model.live="cellEditor.vertical_align"
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="top">Top</option>
                                        <option value="middle">Middle</option>
                                        <option value="bottom">Bottom</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
                                    <input type="number"
                                           wire:model.live="cellEditor.font_size"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           min="8" max="72" placeholder="12">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Font Weight</label>
                                    <select wire:model.live="cellEditor.font_weight"
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="normal">Normal</option>
                                        <option value="bold">Bold</option>
                                        <option value="lighter">Light</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Font Color</label>
                                <div class="flex items-center space-x-2">
                                    <input type="color"
                                           wire:model.live="cellEditor.font_color"
                                           class="w-12 h-10 border border-gray-300 rounded-md">
                                    <input type="text"
                                           wire:model.live="cellEditor.font_color"
                                           class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           placeholder="#000000">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                
                <div class="p-4 border-t border-gray-200 bg-gray-50">
                    <div class="flex justify-between items-center">
                        <div class="text-xs text-gray-500">
                            Changes are saved automatically
                        </div>
                        <div class="flex space-x-2">
                            <button type="button"
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm"
                                    @click="showCellEditor = false">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function invoiceBuilder() {
            return {
                selectedCells: [],
                showCellEditor: false,
                currentCellId: null,

                selectCell(cellId, row, col) {
                    if (this.selectedCells.includes(cellId)) {
                        this.selectedCells = this.selectedCells.filter(id => id !== cellId);
                    } else {
                        this.selectedCells.push(cellId);
                    }
                },

                clearSelection() {
                    this.selectedCells = [];
                },

                editCell(cellId) {
                    this.currentCellId = cellId;
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('loadCellEditor', cellId).then(() => {
                        this.showCellEditor = true;
                    });
                },

                mergeCells() {
                    if (this.selectedCells.length > 1) {
                        window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('mergeCells', this.selectedCells).then(() => {
                            this.clearSelection();
                        });
                    }
                }
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\sites\starterkit\web_starter\resources\views/filament/admin/pages/invoice-visual-builder.blade.php ENDPATH**/ ?>