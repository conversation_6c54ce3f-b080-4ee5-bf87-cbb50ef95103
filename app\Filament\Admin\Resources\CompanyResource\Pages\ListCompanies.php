<?php

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use App\Models\Company;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\ListRecords\Tab;
use Filament\Tables\Actions\{BulkActionGroup, DeleteBulkAction, EditAction, ViewAction};
use Filament\Tables\Columns\{TextColumn};
use Filament\Tables\Table;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;

class ListCompanies extends ListRecords
{
    protected static string $resource = CompanyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }


    public function getDefaultActiveTab(): string
    {
        return 'All';
    }

    protected function paginateTableQuery(Builder $query): Paginator
    {
        return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
    }

    public function getTabs(): array
    {
        return [
			'All' => Tab::make('All')->badge(Company::query()->count()),
            '1' => Tab::make('Clients')->modifyQueryUsing(function (Builder $query){
                $query->where('type', '1');
            })->badge(Company::query()->where('type', '1')->count()),
            '2' => Tab::make('Internal')->modifyQueryUsing(function (Builder $query){
                $query->where('type', '2');
            })->badge(Company::query()->where('type', '2')->count()),
        ];
    }

	public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
					->searchable(),
                TextColumn::make('type')->formatStateUsing(fn ($state) => $state == 1 ? 'Client' : 'Internal')
                    ->searchable()
					->badge()
					->color(fn ($state) => $state == 1 ? 'warning' : 'success'),
                TextColumn::make('template')
					->formatStateUsing(fn (?string $state): string => $state ? 'Attached (' . $state . ')' : 'No Template')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                // ViewAction::make(),
                \Filament\Tables\Actions\Action::make('table_builder')
                    ->label('Table Builder')
                    ->icon('heroicon-o-table-cells')
                    ->color('primary')
                    ->iconButton()
                    ->tooltip('Invoice Table Builder (Method 3)')
                    ->url(fn (Company $record): string =>
                        "/admin/invoice-visual-builder?company={$record->id}"
                    ),
                EditAction::make()->iconButton()->tooltip('Edit'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
