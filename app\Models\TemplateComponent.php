<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TemplateComponent extends Model
{
    use HasFactory;

    protected $fillable = [
        'component_type',
        'component_name',
        'component_code',
        'preview_image',
        'is_system',
        'component_config',
        'description',
    ];

    protected $casts = [
        'component_config' => 'array',
        'is_system' => 'boolean',
    ];

    /**
     * Scope for system components
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * Scope for custom components
     */
    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * Scope for components by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('component_type', $type);
    }

    /**
     * Get component configuration value
     */
    public function getConfig($key, $default = null)
    {
        return data_get($this->component_config, $key, $default);
    }

    /**
     * Set component configuration value
     */
    public function setConfig($key, $value)
    {
        $config = $this->component_config ?? [];
        data_set($config, $key, $value);
        $this->component_config = $config;
        return $this;
    }

    /**
     * Check if component is system component
     */
    public function isSystem(): bool
    {
        return $this->is_system;
    }

    /**
     * Check if component is kop type
     */
    public function isKop(): bool
    {
        return $this->component_type === 'kop';
    }

    /**
     * Check if component is billto type
     */
    public function isBillTo(): bool
    {
        return $this->component_type === 'billto';
    }

    /**
     * Check if component is table type
     */
    public function isTable(): bool
    {
        return $this->component_type === 'table';
    }

    /**
     * Check if component is inwords type
     */
    public function isInWords(): bool
    {
        return $this->component_type === 'inwords';
    }

    /**
     * Check if component is bankinfo type
     */
    public function isBankInfo(): bool
    {
        return $this->component_type === 'bankinfo';
    }

    /**
     * Check if component is text type
     */
    public function isText(): bool
    {
        return $this->component_type === 'text';
    }

    /**
     * Check if component is image type
     */
    public function isImage(): bool
    {
        return $this->component_type === 'image';
    }

    /**
     * Check if component is custom type
     */
    public function isCustomType(): bool
    {
        return $this->component_type === 'custom';
    }
}
