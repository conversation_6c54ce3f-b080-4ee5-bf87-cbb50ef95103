<?php

/**
 * Complete Template Builder Testing Script
 * Run via: php artisan tinker
 */

echo "=== Complete Template Builder Test ===\n";

try {
    // 1. Setup test data
    echo "1. Setting up test data...\n";
    
    $user = App\Models\User::first();
    if (!$user) {
        $user = App\Models\User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ]);
        echo "   ✅ Created test user\n";
    }
    
    $company = App\Models\Company::first();
    if (!$company) {
        $company = App\Models\Company::create([
            'name' => 'PT. Test Company',
            'address' => 'Jl. Test No. 123',
            'phone' => '021-1234567',
            'email' => '<EMAIL>'
        ]);
        echo "   ✅ Created test company\n";
    }
    
    // 2. Create template components
    echo "\n2. Creating template components...\n";
    
    $components = [
        [
            'name' => 'Company Header',
            'type' => 'kop',
            'description' => 'Company logo and information',
            'preview_html' => '<div class="text-center"><h2>{{company.name}}</h2><p>{{company.address}}</p></div>',
            'template_html' => '<div class="kop-section text-{{config.alignment}} text-{{config.font_size}}"><h2>{{company.name}}</h2><p>{{company.address}}</p></div>',
            'config_schema' => json_encode([
                'alignment' => ['type' => 'select', 'options' => ['left', 'center', 'right'], 'default' => 'center'],
                'font_size' => ['type' => 'select', 'options' => [12, 14, 16, 18], 'default' => 14]
            ]),
            'default_config' => json_encode(['alignment' => 'center', 'font_size' => 14])
        ],
        [
            'name' => 'Invoice Items Table',
            'type' => 'table',
            'description' => 'Table of invoice items',
            'preview_html' => '<table border="1"><tr><th>Description</th><th>Qty</th><th>Price</th><th>Total</th></tr><tr><td>Sample Item</td><td>1</td><td>$100</td><td>$100</td></tr></table>',
            'template_html' => '<table class="invoice-table {{config.show_borders ? \'bordered\' : \'\'}}"><thead><tr class="{{config.header_style}}"><th>Description</th><th>Quantity</th><th>Price</th><th>Total</th></tr></thead><tbody>{{#each items}}<tr><td>{{description}}</td><td>{{quantity}}</td><td>{{price}}</td><td>{{total}}</td></tr>{{/each}}</tbody></table>',
            'config_schema' => json_encode([
                'show_borders' => ['type' => 'checkbox', 'default' => true],
                'header_style' => ['type' => 'select', 'options' => ['normal', 'bold', 'colored'], 'default' => 'bold']
            ]),
            'default_config' => json_encode(['show_borders' => true, 'header_style' => 'bold'])
        ],
        [
            'name' => 'Bank Information',
            'type' => 'bankinfo',
            'description' => 'Payment and bank details',
            'preview_html' => '<div><strong>Bank Details:</strong><br>Bank: Sample Bank<br>Account: **********</div>',
            'template_html' => '<div class="bank-info {{config.layout}}"><strong>Bank Details:</strong><br>Bank: {{bank.name}}<br>Account: {{bank.account_number}}</div>',
            'config_schema' => json_encode([
                'layout' => ['type' => 'select', 'options' => ['simple', 'boxed', 'table'], 'default' => 'simple']
            ]),
            'default_config' => json_encode(['layout' => 'simple'])
        ]
    ];
    
    foreach ($components as $componentData) {
        $existing = App\Models\TemplateComponent::where('name', $componentData['name'])->first();
        if (!$existing) {
            App\Models\TemplateComponent::create($componentData);
            echo "   ✅ Created: {$componentData['name']}\n";
        } else {
            echo "   ⚠️  Already exists: {$componentData['name']}\n";
        }
    }
    
    // 3. Create test template
    echo "\n3. Creating test template...\n";
    
    $template = App\Models\InvoiceTemplate::firstOrCreate([
        'name' => 'Complete Test Template'
    ], [
        'description' => 'Template for complete testing',
        'type' => 'custom',
        'company_id' => $company->id,
        'is_active' => true,
        'created_by' => $user->id,
        'template_data' => [
            'page_size' => 'legal',
            'orientation' => 'portrait',
            'font_family' => 'DejaVu Sans',
            'font_size' => 12,
            'sections' => [
                'header' => [],
                'body' => [],
                'footer' => []
            ]
        ]
    ]);
    
    echo "   ✅ Test template ready (ID: {$template->id})\n";
    
    // 4. Test URLs
    echo "\n4. Testing URLs...\n";
    
    try {
        $builderUrl = route('filament.admin.pages.template-builder') . '?template=' . $template->id;
        echo "   ✅ Builder URL: {$builderUrl}\n";
    } catch (\Exception $e) {
        echo "   ❌ Builder URL failed: " . $e->getMessage() . "\n";
    }
    
    try {
        $previewUrl = route('template.preview', ['template' => $template->id]);
        echo "   ✅ Preview URL: {$previewUrl}\n";
    } catch (\Exception $e) {
        echo "   ❌ Preview URL failed: " . $e->getMessage() . "\n";
    }
    
    // 5. Test Livewire component
    echo "\n5. Testing Livewire component...\n";
    
    try {
        $builderComponent = new App\Filament\Admin\Pages\TemplateBuilder();
        $builderComponent->mount(['template' => $template->id]);
        echo "   ✅ TemplateBuilder component mounts successfully\n";
    } catch (\Exception $e) {
        echo "   ❌ TemplateBuilder component failed: " . $e->getMessage() . "\n";
    }
    
    // 6. Test component addition
    echo "\n6. Testing component functionality...\n";
    
    try {
        $builderComponent = new App\Filament\Admin\Pages\TemplateBuilder();
        $builderComponent->mount(['template' => $template->id]);
        
        $testComponent = App\Models\TemplateComponent::where('type', 'kop')->first();
        if ($testComponent) {
            $builderComponent->addComponent($testComponent->id, 'header');
            echo "   ✅ Component addition works\n";
        } else {
            echo "   ⚠️  No test component found\n";
        }
    } catch (\Exception $e) {
        echo "   ❌ Component addition failed: " . $e->getMessage() . "\n";
    }
    
    // 7. Test fonts
    echo "\n7. Testing font system...\n";
    
    try {
        $fontOptions = App\Services\FontService::getSelectOptions();
        echo "   ✅ Font service works, " . count($fontOptions) . " fonts available\n";
    } catch (\Exception $e) {
        echo "   ❌ Font service failed: " . $e->getMessage() . "\n";
    }
    
    // 8. Test preview controller
    echo "\n8. Testing preview controller...\n";
    
    try {
        $controller = new App\Http\Controllers\TemplatePreviewController();
        $request = new Illuminate\Http\Request();
        $response = $controller->show($request, $template);
        echo "   ✅ Preview controller works\n";
    } catch (\Exception $e) {
        echo "   ❌ Preview controller failed: " . $e->getMessage() . "\n";
    }
    
    // 9. Feature checklist
    echo "\n9. Feature Checklist:\n";
    
    $features = [
        'Template Components' => App\Models\TemplateComponent::count() >= 3,
        'Template Builder Page' => class_exists('App\Filament\Admin\Pages\TemplateBuilder'),
        'Preview Controller' => class_exists('App\Http\Controllers\TemplatePreviewController'),
        'Font Service' => class_exists('App\Services\FontService'),
        'Template Model' => class_exists('App\Models\InvoiceTemplate'),
        'Component Model' => class_exists('App\Models\TemplateComponent'),
        'Font Model' => class_exists('App\Models\Font'),
    ];
    
    foreach ($features as $feature => $status) {
        $icon = $status ? '✅' : '❌';
        echo "   {$icon} {$feature}\n";
    }
    
    // 10. Final summary
    echo "\n=== Test Summary ===\n";
    echo "✅ Template Builder is ready for testing!\n";
    echo "\n📋 Next Steps:\n";
    echo "1. Open browser and go to: {$builderUrl}\n";
    echo "2. Test drag & drop functionality\n";
    echo "3. Add components to sections\n";
    echo "4. Configure component properties\n";
    echo "5. Save template\n";
    echo "6. Test preview functionality\n";
    echo "7. Test print functionality\n";
    
    echo "\n🎯 Testing Checklist:\n";
    echo "□ Page loads without errors\n";
    echo "□ Components appear in palette\n";
    echo "□ Drag & drop works smoothly\n";
    echo "□ Drop zones highlight correctly\n";
    echo "□ Components add to sections\n";
    echo "□ Properties panel opens and works\n";
    echo "□ Component configuration saves\n";
    echo "□ Template save functionality works\n";
    echo "□ Preview opens in new tab\n";
    echo "□ Preview renders correctly\n";
    echo "□ Print functionality works\n";
    echo "□ Responsive design works on mobile\n";
    
    echo "\n🚀 Template Builder is COMPLETE!\n";
    
} catch (\Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
