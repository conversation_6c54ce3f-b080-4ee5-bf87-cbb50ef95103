<?php

/**
 * Test Logo Storage Fix
 * Run via: php artisan tinker
 */

echo "=== Testing Logo Storage Fix ===\n";

try {
    // 1. Test data with actual logo path
    echo "1. Creating test data with actual logo path...\n";
    
    $testData = [
        'company' => (object) [
            'name' => 'FAIR DEAL INTERNATIONAL INC',
            'logo' => 'logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png', // Actual path from database
            'address' => 'Jl. Business Center No. 123<br>Jakarta Pusat 10220<br>Indonesia',
            'phone' => '+62 21 5555 1234',
            'email' => '<EMAIL>',
            'website' => 'www.fairdeal.co.id',
        ],
        'invoice' => (object) [
            'invoice_no' => 'INV-2024-001',
            'invoice_date' => '2024-01-20',
            'invoice_due' => '2024-02-20',
        ],
        'customer' => (object) [
            'name' => 'PT. Sample Client Corp',
            'address' => 'Jl. Client Avenue No. 456<br>Surabaya 60123<br>East Java',
        ]
    ];
    
    echo "   ✅ Test data created with logo path: {$testData['company']->logo}\n";
    
    // 2. Test different logo path formats
    echo "\n2. Testing different logo path formats...\n";
    
    $logoPathTests = [
        'relative' => 'logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png',
        'storage_prefix' => 'storage/logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png',
        'absolute_storage' => '/storage/logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png',
        'full_url' => 'http://localhost/storage/logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png',
    ];
    
    foreach ($logoPathTests as $testName => $logoPath) {
        echo "   🧪 Testing {$testName}: {$logoPath}\n";
        
        // Create test data with this logo path
        $testDataVariant = $testData;
        $testDataVariant['company']->logo = $logoPath;
        
        // Create temporary content block for testing
        $testSection = App\Models\LayoutSection::first();
        
        if ($testSection) {
            $tempBlock = App\Models\ContentBlock::create([
                'layout_section_id' => $testSection->id,
                'block_type' => 'company_logo',
                'block_name' => 'Test Logo Block',
                'column_position' => 1,
                'order' => 999,
                'content_data' => [
                    'field_path' => 'company.logo',
                    'width' => '120px',
                    'height' => 'auto',
                    'alt_text' => 'Company Logo'
                ]
            ]);
            
            try {
                $renderedHtml = $tempBlock->renderHtml($testDataVariant);
                
                // Extract src attribute from img tag
                if (preg_match('/src="([^"]*)"/', $renderedHtml, $matches)) {
                    $generatedSrc = $matches[1];
                    echo "      📄 Generated src: {$generatedSrc}\n";
                    
                    // Check if URL looks correct
                    if (strpos($generatedSrc, '/storage/logo/') !== false) {
                        echo "      ✅ Correct storage URL format\n";
                    } else {
                        echo "      ⚠️  Unexpected URL format\n";
                    }
                } else {
                    echo "      ❌ No img tag found in rendered HTML\n";
                }
                
            } catch (\Exception $e) {
                echo "      ❌ Error rendering: " . $e->getMessage() . "\n";
            }
            
            // Clean up
            $tempBlock->delete();
        }
    }
    
    // 3. Test actual company logo blocks
    echo "\n3. Testing actual company logo blocks...\n";
    
    $logoBlocks = App\Models\ContentBlock::where('block_type', 'company_logo')->get();
    echo "   📊 Found {$logoBlocks->count()} company_logo blocks\n";
    
    if ($logoBlocks->count() > 0) {
        foreach ($logoBlocks->take(3) as $block) { // Test first 3 blocks
            echo "   🧪 Testing block ID: {$block->id}\n";
            
            try {
                $renderedHtml = $block->renderHtml($testData);
                
                if (strpos($renderedHtml, 'Unknown block type') !== false) {
                    echo "      ❌ Still shows 'Unknown block type'\n";
                } elseif (preg_match('/src="([^"]*)"/', $renderedHtml, $matches)) {
                    $generatedSrc = $matches[1];
                    echo "      📄 Generated src: {$generatedSrc}\n";
                    echo "      ✅ Logo renders correctly\n";
                } else {
                    echo "      ⚠️  No img tag found, might be placeholder\n";
                }
                
            } catch (\Exception $e) {
                echo "      ❌ Error: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // 4. Test storage URL generation
    echo "\n4. Testing storage URL generation methods...\n";
    
    $logoPath = 'logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png';
    
    $urlMethods = [
        'asset()' => asset('storage/' . $logoPath),
        'url()' => url('storage/' . $logoPath),
        'Storage::url()' => \Storage::url($logoPath),
    ];
    
    foreach ($urlMethods as $method => $generatedUrl) {
        echo "   📋 {$method}: {$generatedUrl}\n";
    }
    
    // 5. Check if storage link exists
    echo "\n5. Checking storage link...\n";
    
    $publicStoragePath = public_path('storage');
    $storageAppPublicPath = storage_path('app/public');
    
    if (is_link($publicStoragePath)) {
        echo "   ✅ Storage link exists: {$publicStoragePath} -> " . readlink($publicStoragePath) . "\n";
    } elseif (is_dir($publicStoragePath)) {
        echo "   ✅ Storage directory exists: {$publicStoragePath}\n";
    } else {
        echo "   ❌ Storage link/directory missing: {$publicStoragePath}\n";
        echo "   🔧 Run: php artisan storage:link\n";
    }
    
    // Check if actual logo file exists
    $actualLogoPath = storage_path('app/public/' . $logoPath);
    if (file_exists($actualLogoPath)) {
        echo "   ✅ Logo file exists: {$actualLogoPath}\n";
        echo "   📊 File size: " . number_format(filesize($actualLogoPath)) . " bytes\n";
    } else {
        echo "   ❌ Logo file not found: {$actualLogoPath}\n";
    }
    
    // 6. Test with empty logo
    echo "\n6. Testing with empty logo...\n";
    
    $emptyLogoData = $testData;
    $emptyLogoData['company']->logo = '';
    
    $testSection = App\Models\LayoutSection::first();
    if ($testSection) {
        $tempBlock = App\Models\ContentBlock::create([
            'layout_section_id' => $testSection->id,
            'block_type' => 'company_logo',
            'block_name' => 'Test Empty Logo',
            'column_position' => 1,
            'order' => 999,
            'content_data' => [
                'field_path' => 'company.logo',
                'width' => '120px',
                'height' => 'auto',
                'alt_text' => 'Company Logo'
            ]
        ]);
        
        try {
            $renderedHtml = $tempBlock->renderHtml($emptyLogoData);
            
            if (strpos($renderedHtml, '[LOGO]') !== false) {
                echo "   ✅ Empty logo shows placeholder correctly\n";
            } else {
                echo "   ❌ Empty logo doesn't show placeholder\n";
            }
            
        } catch (\Exception $e) {
            echo "   ❌ Error with empty logo: " . $e->getMessage() . "\n";
        }
        
        $tempBlock->delete();
    }
    
    // 7. Create updated block template with correct storage handling
    echo "\n7. Creating updated block template...\n";
    
    try {
        $logoTemplate = App\Models\BlockTemplate::updateOrCreate(
            ['block_type' => 'company_logo_fixed'],
            [
                'name' => 'Company Logo (Storage Fixed)',
                'description' => 'Company logo with proper Laravel storage handling',
                'category' => 'company',
                'template_data' => [
                    'field_path' => 'company.logo',
                    'alt_text' => 'Company Logo',
                    'width' => '120px',
                    'height' => 'auto'
                ],
                'field_mappings' => [
                    'field' => 'company.logo',
                    'format' => 'image'
                ],
                'default_styling' => [
                    'alignment' => 'left'
                ],
                'preview_html' => '<div class="w-16 h-12 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500 rounded">[LOGO]</div>',
                'is_system' => true,
                'is_active' => true,
                'tags' => ['company', 'logo', 'image', 'storage'],
                'usage_count' => 0
            ]
        );
        
        echo "   ✅ Created/updated logo template: {$logoTemplate->name} (ID: {$logoTemplate->id})\n";
        
    } catch (\Exception $e) {
        echo "   ❌ Failed to create logo template: " . $e->getMessage() . "\n";
    }
    
    // 8. Summary and recommendations
    echo "\n=== Logo Storage Fix Summary ===\n";
    
    echo "📋 Storage Path Handling:\n";
    echo "✅ Database value: logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png\n";
    echo "✅ Storage location: storage/app/public/logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png\n";
    echo "✅ Public URL: /storage/logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png\n";
    echo "✅ Generated with: asset('storage/' . \$logoPath)\n";
    
    echo "\n🔧 Recommendations:\n";
    echo "1. Ensure storage link exists: php artisan storage:link\n";
    echo "2. Verify logo files exist in storage/app/public/logo/\n";
    echo "3. Test template preview to see if logo displays correctly\n";
    echo "4. Check browser network tab for 404 errors on logo URLs\n";
    
    echo "\n🚀 Next Steps:\n";
    echo "1. Refresh template preview page\n";
    echo "2. Check if logo displays instead of 'Unknown block type'\n";
    echo "3. Test with different companies that have logos\n";
    echo "4. Verify logo URLs are accessible in browser\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
