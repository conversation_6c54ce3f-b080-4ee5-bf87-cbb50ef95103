<?php

namespace App\Http\Controllers;

use App\Models\TemplateLayout;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;

class VisualTemplateController extends Controller
{
    /**
     * Preview template with sample data
     */
    public function preview(Request $request, TemplateLayout $template): View
    {
        // Force fresh load of template with all relationships to get latest data
        $template = TemplateLayout::with(['sections.blocks', 'company'])->find($template->id);

        // Get preview mode and page size from request
        $mode = $request->get('mode', 'desktop');
        $size = $request->get('size', $template->page_size);

        // Generate sample data for preview
        $sampleData = $this->generateSampleData($template);

        return view('visual-template.preview', [
            'template' => $template,
            'sampleData' => $sampleData,
            'previewMode' => $mode,
            'pageSize' => $size,
        ]);
    }

    /**
     * Render template as HTML for PDF generation or printing
     */
    public function render(Request $request, TemplateLayout $template): Response
    {
        // Load template with all relationships
        $template->load(['sections.blocks', 'company']);

        // Get actual data from request or use sample data
        $data = $request->has('data')
            ? json_decode(base64_decode($request->get('data')), true)
            : $this->generateSampleData($template);

        // Render template HTML
        $html = $this->renderTemplateHtml($template, $data);

        return response($html, 200, [
            'Content-Type' => 'text/html',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    /**
     * Generate sample data for template preview
     */
    private function generateSampleData(TemplateLayout $template): array
    {
        $company = $template->company ?? new Company([
            'name' => 'Sample Company Ltd.',
            'address' => 'Jl. Sample Street No. 123, Jakarta 12345',
            'phone' => '+62 21 1234 5678',
            'email' => '<EMAIL>',
        ]);

        return [
            'company' => [
                'name' => $company->name,
                'address' => $company->address,
                'phone' => $company->phone,
                'email' => $company->email,
                'logo' => $company->logo, // Use actual company logo from database
            ],
            'client' => [
                'name' => 'Sample Client Corporation',
                'address' => 'Jl. Client Avenue No. 456, Surabaya 60123',
                'phone' => '+62 31 9876 5432',
                'email' => '<EMAIL>',
            ],
            'invoice' => [
                'number' => 'INV-2024-001',
                'date' => now()->format('Y-m-d'),
                'due_date' => now()->addDays(30)->format('Y-m-d'),
                'reference' => 'REF-2024-001',
                'terms' => 'Net 30 days',
            ],
            'items' => [
                [
                    'description' => 'Professional Services - Consultation',
                    'quantity' => 10,
                    'price' => 150000,
                    'total' => 1500000,
                ],
                [
                    'description' => 'Software Development - Custom Module',
                    'quantity' => 1,
                    'price' => 5000000,
                    'total' => 5000000,
                ],
                [
                    'description' => 'Training and Documentation',
                    'quantity' => 5,
                    'price' => 200000,
                    'total' => 1000000,
                ],
            ],
            'totals' => [
                'subtotal' => 7500000,
                'tax_rate' => 11,
                'tax' => 825000,
                'total' => 8325000,
                'total_words' => 'Eight Million Three Hundred Twenty Five Thousand Rupiah',
            ],
            'bank' => [
                'name' => 'Bank Sample Indonesia',
                'account_number' => '**********',
                'account_name' => $company->name,
                'swift_code' => 'SAMPIDJA',
            ],
            'signature' => [
                'name' => 'John Doe',
                'title' => 'Finance Manager',
                'image_url' => null, // No signature image for sample
            ],
            'notes' => 'Thank you for your business. Payment is due within 30 days of invoice date.',
            'terms' => 'All payments should be made in Indonesian Rupiah. Late payments may incur additional charges.',
        ];
    }

    /**
     * Render complete template HTML
     */
    private function renderTemplateHtml(TemplateLayout $template, array $data): string
    {
        $html = '<!DOCTYPE html>';
        $html .= '<html lang="en">';
        $html .= '<head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        $html .= '<title>Invoice Template - ' . $template->name . '</title>';
        $html .= $this->getTemplateStyles($template);
        $html .= '</head>';
        $html .= '<body>';

        $html .= '<div class="template-container">';

        // Render sections in order
        $sections = $template->sections->sortBy('order')->groupBy('section_type');

        foreach (['header', 'body', 'footer'] as $sectionType) {
            if (isset($sections[$sectionType])) {
                foreach ($sections[$sectionType] as $section) {
                    $html .= $section->renderHtml($data);
                }
            }
        }

        $html .= '</div>';
        $html .= '</body>';
        $html .= '</html>';

        return $html;
    }

    /**
     * Generate CSS styles for template
     */
    private function getTemplateStyles(TemplateLayout $template): string
    {
        $pageSize = $template->page_size === 'f4' ? 'A4' : 'legal';
        $fontFamily = $template->font_family;
        $fontSize = $template->font_size;

        return "
        <style>
            @page {
                size: {$pageSize};
                margin: 1cm;
            }

            body {
                font-family: '{$fontFamily}', sans-serif;
                font-size: {$fontSize}pt;
                line-height: 1.4;
                margin: 0;
                padding: 0;
                color: #333;
            }

            .template-container {
                width: 100%;
                max-width: 100%;
            }

            .layout-section {
                margin-bottom: 20px;
                page-break-inside: avoid;
            }

            .section-columns {
                display: flex;
                gap: 15px;
            }

            .section-column {
                flex: 1;
            }

            .content-block {
                margin-bottom: 10px;
            }

            .invoice-table {
                width: 100%;
                border-collapse: collapse;
                margin: 10px 0;
            }

            .invoice-table th,
            .invoice-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }

            .invoice-table th {
                background-color: #f5f5f5;
                font-weight: bold;
            }

            .calculations {
                margin-top: 20px;
            }

            .calc-line {
                display: flex;
                justify-content: space-between;
                margin-bottom: 5px;
                padding: 2px 0;
            }

            .calc-line.total {
                border-top: 2px solid #333;
                padding-top: 8px;
                margin-top: 10px;
            }

            .signature-block {
                margin-top: 30px;
                text-align: center;
            }

            .logo-placeholder {
                background-color: #f0f0f0;
                border: 2px dashed #ccc;
                padding: 20px;
                text-align: center;
                color: #666;
            }

            /* Responsive adjustments */
            @media print {
                .template-container {
                    width: 100% !important;
                    max-width: none !important;
                }

                .layout-section {
                    page-break-inside: avoid;
                }
            }
        </style>";
    }

    /**
     * Preview table template layout
     */
    public function tablePreview(Request $request, TemplateLayout $template): View
    {
        // Force fresh load of template with table sections
        $template = TemplateLayout::with(['tableSections.cells', 'company'])->find($template->id);

        // Get preview mode and page size from request
        $mode = $request->get('mode', 'desktop');
        $size = $request->get('size', $template->page_size);

        // Generate sample data for preview
        $sampleData = $this->generateSampleData($template);

        return view('table-template.preview', [
            'template' => $template,
            'sampleData' => $sampleData,
            'previewMode' => $mode,
            'pageSize' => $size,
        ]);
    }
}
