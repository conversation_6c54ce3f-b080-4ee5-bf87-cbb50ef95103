<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Template Preview - {{ $company->name }}</title>
    
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12pt;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .template-container {
            width: 100%;
            max-width: 100%;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .invoice-table.section-header {
            margin-bottom: 30px;
        }

        .invoice-table.section-body {
            margin-bottom: 20px;
        }

        .invoice-table.section-footer {
            margin-top: 30px;
        }

        .invoice-cell {
            border: 1px solid #ddd;
            padding: 8px;
            vertical-align: top;
        }

        .invoice-cell.content-text {
            /* Text field styling */
        }

        .invoice-cell.content-image {
            text-align: center;
        }

        .invoice-cell.content-field {
            /* Field styling */
        }

        .invoice-cell img {
            max-width: 100%;
            height: auto;
        }

        /* Preview mode specific styles */
        @media screen {
            body {
                background-color: #f5f5f5;
                padding: 20px;
            }

            .template-container {
                background-color: white;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                padding: 40px;
                max-width: {{ $previewMode === 'mobile' ? '400px' : '800px' }};
                margin: 0 auto;
            }

            .preview-header {
                background-color: #1f2937;
                color: white;
                padding: 15px 20px;
                border-radius: 8px 8px 0 0;
                margin: -20px -20px 20px -20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .preview-title {
                font-size: 18px;
                font-weight: bold;
            }

            .preview-info {
                font-size: 12px;
                opacity: 0.8;
            }
        }

        /* Print styles */
        @media print {
            body {
                background-color: white !important;
                padding: 0 !important;
            }

            .template-container {
                box-shadow: none !important;
                border-radius: 0 !important;
                padding: 0 !important;
                max-width: none !important;
                margin: 0 !important;
            }

            .preview-header {
                display: none !important;
            }

            .invoice-table {
                page-break-inside: avoid;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .template-container {
                padding: 20px;
                margin: 10px;
                max-width: calc(100% - 20px);
            }

            .invoice-table {
                font-size: 11pt;
            }

            .invoice-cell {
                padding: 6px;
            }
        }
    </style>
</head>
<body>
    <div class="template-container">
        @if($previewMode !== 'print')
            <div class="preview-header">
                <div>
                    <div class="preview-title">{{ $company->name }} - Invoice Template</div>
                    <div class="preview-info">Table Layout Preview • {{ ucfirst($previewMode) }} Mode</div>
                </div>
                <div>
                    <button onclick="window.print()" style="background: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        Print Preview
                    </button>
                </div>
            </div>
        @endif

        {{-- Render Invoice Template Sections --}}
        @foreach(['header', 'body', 'footer'] as $sectionType)
            @php
                $sections = $company->invoiceTemplateSections->where('section_type', $sectionType)->sortBy('order');
            @endphp
            
            @foreach($sections as $section)
                {!! $section->renderHtml($sampleData) !!}
            @endforeach
        @endforeach

        {{-- Fallback if no sections exist --}}
        @if($company->invoiceTemplateSections->isEmpty())
            <div style="text-align: center; padding: 40px; color: #666; border: 2px dashed #ddd; border-radius: 8px;">
                <h3 style="margin: 0 0 10px 0; color: #999;">No Invoice Template Found</h3>
                <p style="margin: 0; font-size: 14px;">
                    This company doesn't have any invoice template sections yet.<br>
                    Use the Invoice Visual Builder to create your layout.
                </p>
                <div style="margin-top: 20px;">
                    <a href="{{ route('filament.admin.pages.invoice-visual-builder', ['company' => $company->id]) }}" 
                       style="background: #10b981; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">
                        Open Invoice Builder
                    </a>
                </div>
            </div>
        @endif
    </div>

    <script>
        // Auto-refresh preview when in development
        @if(config('app.debug'))
            setTimeout(() => {
                if (document.hidden === false) {
                    // Only refresh if tab is active
                    location.reload();
                }
            }, 30000); // Refresh every 30 seconds
        @endif

        // Print functionality
        function printPreview() {
            window.print();
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printPreview();
            }
            if (e.key === 'F5') {
                e.preventDefault();
                location.reload();
            }
        });
    </script>
</body>
</html>
