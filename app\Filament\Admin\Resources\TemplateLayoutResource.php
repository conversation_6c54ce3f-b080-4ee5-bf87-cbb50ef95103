<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TemplateLayoutResource\Pages;
use App\Models\TemplateLayout;
use App\Models\Company;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TemplateLayoutResource extends Resource
{
    protected static ?string $model = TemplateLayout::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Template Layouts';
    protected static ?string $navigationGroup = 'Template Management';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Template Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn (string $context, $state, callable $set) =>
                                $context === 'create' ? $set('description', "Template for {$state}") : null
                            ),

                        Forms\Components\Textarea::make('description')
                            ->maxLength(500)
                            ->rows(3),

                        Forms\Components\Select::make('company_id')
                            ->label('Company')
                            ->options(Company::pluck('name', 'id'))
                            ->searchable()
                            ->nullable()
                            ->helperText('Leave empty for global template'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),

                        Forms\Components\Toggle::make('is_default')
                            ->label('Default Template')
                            ->helperText('Set as default template for the selected company'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Page Settings')
                    ->schema([
                        Forms\Components\Select::make('page_size')
                            ->label('Page Size')
                            ->options([
                                'legal' => 'Legal (8.5" x 14")',
                                'f4' => 'F4/Folio (210mm x 330mm)',
                            ])
                            ->default('legal')
                            ->required(),

                        Forms\Components\Select::make('orientation')
                            ->options([
                                'portrait' => 'Portrait',
                                'landscape' => 'Landscape',
                            ])
                            ->default('portrait')
                            ->required(),

                        Forms\Components\Select::make('font_family')
                            ->label('Font Family')
                            ->options([
                                'DejaVu Sans' => 'DejaVu Sans',
                                'DejaVu Serif' => 'DejaVu Serif',
                                'Arial' => 'Arial',
                                'Helvetica' => 'Helvetica',
                                'Times' => 'Times',
                                'Courier' => 'Courier',
                            ])
                            ->default('DejaVu Sans')
                            ->required(),

                        Forms\Components\TextInput::make('font_size')
                            ->label('Base Font Size (pt)')
                            ->numeric()
                            ->default(12)
                            ->minValue(8)
                            ->maxValue(24)
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Advanced Settings')
                    ->schema([
                        Forms\Components\KeyValue::make('settings')
                            ->label('Custom Settings')
                            ->keyLabel('Setting Name')
                            ->valueLabel('Setting Value')
                            ->addActionLabel('Add Setting')
                            ->helperText('Additional template configuration options'),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('company.name')
                    ->label('Company')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Global Template')
                    ->badge()
                    ->color('gray'),

                Tables\Columns\TextColumn::make('version')
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('page_size')
                    ->label('Page Size')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'legal' => 'info',
                        'f4' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_default')
                    ->label('Default')
                    ->boolean(),

                Tables\Columns\TextColumn::make('sections_count')
                    ->label('Sections')
                    ->counts('sections')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('last_used_at')
                    ->label('Last Used')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Never used'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('company_id')
                    ->label('Company')
                    ->options(Company::pluck('name', 'id'))
                    ->placeholder('All Companies'),

                Tables\Filters\SelectFilter::make('page_size')
                    ->options([
                        'legal' => 'Legal',
                        'f4' => 'F4/Folio',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),

                Tables\Filters\TernaryFilter::make('is_default')
                    ->label('Default Templates'),
            ])
            ->actions([
                Tables\Actions\Action::make('table_builder')
                    ->label('Table Builder')
                    ->icon('heroicon-o-table-cells')
                    ->color('primary')
                    ->size('sm')
                    ->url(fn (TemplateLayout $record): string =>
                        route('filament.admin.pages.table-visual-builder', ['template' => $record->id])
                    ),

                Tables\Actions\Action::make('preview')
                    ->label('Preview')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->size('sm')
                    ->url(fn (TemplateLayout $record): string =>
                        route('table-template.preview', ['template' => $record->id])
                    )
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('duplicate')
                    ->label('Duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('gray')
                    ->action(function (TemplateLayout $record) {
                        $newTemplate = $record->duplicate();

                        Notification::make()
                            ->title('Template Duplicated')
                            ->body("Template '{$record->name}' has been duplicated as '{$newTemplate->name}'.")
                            ->success()
                            ->send();
                    }),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => true]);
                        }),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => false]);
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTemplateLayouts::route('/'),
            'create' => Pages\CreateTemplateLayout::route('/create'),
            'edit' => Pages\EditTemplateLayout::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['company']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'description', 'company.name'];
    }
}
