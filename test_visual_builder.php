<?php

/**
 * Test Visual Builder v2.0 Functionality
 * Run via: php artisan tinker
 */

echo "=== Testing Visual Builder v2.0 Functionality ===\n";

try {
    // 1. Test database connections
    echo "1. Testing database connections...\n";
    
    $templateCount = App\Models\TemplateLayout::count();
    $sectionCount = App\Models\LayoutSection::count();
    $blockCount = App\Models\ContentBlock::count();
    $blockTemplateCount = App\Models\BlockTemplate::count();
    
    echo "   ✅ TemplateLayout: {$templateCount} records\n";
    echo "   ✅ LayoutSection: {$sectionCount} records\n";
    echo "   ✅ ContentBlock: {$blockCount} records\n";
    echo "   ✅ BlockTemplate: {$blockTemplateCount} records\n";
    
    // 2. Test model relationships
    echo "\n2. Testing model relationships...\n";
    
    $template = App\Models\TemplateLayout::with(['sections.blocks'])->first();
    if ($template) {
        echo "   ✅ Template '{$template->name}' loaded\n";
        echo "   ✅ Sections: {$template->sections->count()}\n";
        echo "   ✅ Total blocks: {$template->sections->sum(fn($s) => $s->blocks->count())}\n";
    } else {
        echo "   ⚠️  No templates found\n";
    }
    
    // 3. Test block template functionality
    echo "\n3. Testing block template functionality...\n";
    
    $logoBlock = App\Models\BlockTemplate::where('block_type', 'logo')->first();
    if ($logoBlock) {
        echo "   ✅ Logo block template found: {$logoBlock->name}\n";
        echo "   ✅ Preview HTML: " . (strlen($logoBlock->getPreviewHtml()) > 0 ? 'Generated' : 'Empty') . "\n";
    }
    
    // 4. Test content block rendering
    echo "\n4. Testing content block rendering...\n";
    
    $contentBlock = App\Models\ContentBlock::first();
    if ($contentBlock) {
        $sampleData = [
            'company' => ['name' => 'Test Company', 'address' => 'Test Address'],
            'client' => ['name' => 'Test Client'],
            'invoice' => ['number' => 'INV-001', 'date' => '2024-01-01']
        ];
        
        $html = $contentBlock->renderHtml($sampleData);
        echo "   ✅ Content block rendered: " . (strlen($html) > 0 ? 'Success' : 'Empty') . "\n";
        echo "   📄 HTML length: " . strlen($html) . " characters\n";
    }
    
    // 5. Test section rendering
    echo "\n5. Testing section rendering...\n";
    
    $section = App\Models\LayoutSection::with('blocks')->first();
    if ($section) {
        $sampleData = [
            'company' => ['name' => 'Test Company', 'address' => 'Test Address'],
            'client' => ['name' => 'Test Client'],
            'invoice' => ['number' => 'INV-001', 'date' => '2024-01-01']
        ];
        
        $html = $section->renderHtml($sampleData);
        echo "   ✅ Section rendered: " . (strlen($html) > 0 ? 'Success' : 'Empty') . "\n";
        echo "   📄 HTML length: " . strlen($html) . " characters\n";
    }
    
    // 6. Test template export/import
    echo "\n6. Testing template export functionality...\n";
    
    if ($template) {
        $config = $template->exportConfiguration();
        echo "   ✅ Template exported: " . (count($config) > 0 ? 'Success' : 'Empty') . "\n";
        echo "   📊 Config sections: " . count($config['sections'] ?? []) . "\n";
    }
    
    // 7. Test route accessibility
    echo "\n7. Testing route accessibility...\n";
    
    try {
        $visualBuilderRoute = route('filament.admin.pages.visual-template-builder');
        echo "   ✅ Visual Builder route: {$visualBuilderRoute}\n";
    } catch (\Exception $e) {
        echo "   ❌ Visual Builder route error: " . $e->getMessage() . "\n";
    }
    
    try {
        $templateLayoutsRoute = route('filament.admin.resources.template-layouts.index');
        echo "   ✅ Template Layouts route: {$templateLayoutsRoute}\n";
    } catch (\Exception $e) {
        echo "   ❌ Template Layouts route error: " . $e->getMessage() . "\n";
    }
    
    if ($template) {
        try {
            $previewRoute = route('visual-template.preview', $template->id);
            echo "   ✅ Preview route: {$previewRoute}\n";
        } catch (\Exception $e) {
            echo "   ❌ Preview route error: " . $e->getMessage() . "\n";
        }
    }
    
    // 8. Test drag & drop data structure
    echo "\n8. Testing drag & drop data structure...\n";
    
    $blockTemplates = App\Models\BlockTemplate::active()->get();
    $groupedBlocks = $blockTemplates->groupBy('category');
    
    echo "   ✅ Block categories: " . $groupedBlocks->keys()->implode(', ') . "\n";
    foreach ($groupedBlocks as $category => $blocks) {
        echo "   📦 {$category}: {$blocks->count()} blocks\n";
    }
    
    // 9. Test sample data generation
    echo "\n9. Testing sample data generation...\n";
    
    $controller = new App\Http\Controllers\VisualTemplateController();
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('generateSampleData');
    $method->setAccessible(true);
    
    if ($template) {
        $sampleData = $method->invoke($controller, $template);
        echo "   ✅ Sample data generated\n";
        echo "   📊 Data keys: " . implode(', ', array_keys($sampleData)) . "\n";
        echo "   💰 Sample total: Rp " . number_format($sampleData['totals']['total'] ?? 0, 0, ',', '.') . "\n";
    }
    
    // 10. Performance test
    echo "\n10. Performance testing...\n";
    
    $start = microtime(true);
    
    // Test multiple operations
    for ($i = 0; $i < 10; $i++) {
        App\Models\BlockTemplate::active()->get();
    }
    
    $end = microtime(true);
    $duration = round(($end - $start) * 1000, 2);
    
    echo "   ⚡ 10x BlockTemplate queries: {$duration}ms\n";
    echo "   ✅ Performance: " . ($duration < 100 ? 'Good' : 'Needs optimization') . "\n";
    
    // 11. Summary and recommendations
    echo "\n=== Test Summary ===\n";
    
    $issues = [];
    $successes = [];
    
    if ($templateCount === 0) {
        $issues[] = "No templates found - run setup script";
    } else {
        $successes[] = "Templates available";
    }
    
    if ($blockTemplateCount === 0) {
        $issues[] = "No block templates found - run setup script";
    } else {
        $successes[] = "Block templates available";
    }
    
    if (count($issues) > 0) {
        echo "❌ Issues found:\n";
        foreach ($issues as $issue) {
            echo "   - {$issue}\n";
        }
    }
    
    if (count($successes) > 0) {
        echo "✅ Working features:\n";
        foreach ($successes as $success) {
            echo "   - {$success}\n";
        }
    }
    
    // 12. Next steps
    echo "\n🎯 Next Steps:\n";
    
    if ($templateCount === 0 || $blockTemplateCount === 0) {
        echo "1. Run setup script: include 'setup_visual_builder.php'\n";
    }
    
    echo "2. Access Visual Builder: /admin/visual-template-builder\n";
    echo "3. Test drag & drop functionality\n";
    echo "4. Test section and block settings\n";
    echo "5. Test preview functionality\n";
    
    echo "\n🚀 Visual Builder v2.0 testing complete!\n";
    
} catch (\Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
