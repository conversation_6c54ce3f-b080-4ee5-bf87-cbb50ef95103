<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Table Sections (replaces layout_sections)
        Schema::create('table_sections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('template_layout_id')->constrained()->cascadeOnDelete();
            $table->string('section_type')->default('header'); // header, body, footer
            $table->string('section_name')->nullable();
            $table->integer('rows')->default(1); // Number of rows in table
            $table->integer('columns')->default(1); // Number of columns in table
            $table->json('table_structure')->nullable(); // Store table structure
            $table->json('styling')->nullable(); // Table-level styling
            $table->integer('order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['template_layout_id', 'section_type', 'order']);
        });

        // Table Cells (replaces content_blocks)
        Schema::create('table_cells', function (Blueprint $table) {
            $table->id();
            $table->foreignId('table_section_id')->constrained()->cascadeOnDelete();
            $table->integer('row_index'); // 0-based row index
            $table->integer('col_index'); // 0-based column index
            $table->integer('rowspan')->default(1); // How many rows this cell spans
            $table->integer('colspan')->default(1); // How many columns this cell spans
            
            // Content
            $table->string('field_type')->default('text'); // text, image, currency, date, field
            $table->string('field_path')->nullable(); // Path to data field (e.g., 'company.name')
            $table->text('static_content')->nullable(); // Static text content
            $table->json('field_config')->nullable(); // Field-specific configuration
            
            // Styling
            $table->string('text_align')->default('left'); // left, center, right, justify
            $table->string('vertical_align')->default('top'); // top, middle, bottom
            $table->integer('font_size')->default(12);
            $table->string('font_weight')->default('normal'); // normal, bold, lighter
            $table->string('font_color')->default('#000000');
            $table->string('background_color')->nullable();
            
            // Spacing
            $table->json('padding')->nullable(); // {top, right, bottom, left}
            $table->json('margin')->nullable(); // {top, right, bottom, left}
            $table->json('border')->nullable(); // {width, style, color}
            
            // Image-specific
            $table->string('image_width')->nullable(); // e.g., '100px', '50%'
            $table->string('image_height')->nullable(); // e.g., 'auto', '100px'
            $table->string('image_fit')->default('contain'); // contain, cover, fill
            
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['table_section_id', 'row_index', 'col_index']);
            $table->unique(['table_section_id', 'row_index', 'col_index'], 'unique_cell_position');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('table_cells');
        Schema::dropIfExists('table_sections');
    }
};
