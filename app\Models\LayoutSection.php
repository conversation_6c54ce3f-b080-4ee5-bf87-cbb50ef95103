<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LayoutSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'template_layout_id',
        'section_type',
        'section_name',
        'column_count',
        'column_layout',
        'column_widths',
        'settings',
        'styling',
        'order',
        'is_active',
        'min_height',
        'background_color',
        'border_style',
        'padding',
        'margin',
    ];

    protected $casts = [
        'column_widths' => 'array',
        'settings' => 'array',
        'styling' => 'array',
        'is_active' => 'boolean',
        'column_count' => 'integer',
        'order' => 'integer',
        'min_height' => 'integer',
        'padding' => 'array',
        'margin' => 'array',
    ];

    protected $attributes = [
        'column_count' => 1,
        'column_layout' => 'equal',
        'is_active' => true,
        'order' => 0,
        'min_height' => 50,
    ];

    // Relationships
    public function templateLayout(): BelongsTo
    {
        return $this->belongsTo(TemplateLayout::class);
    }

    public function blocks(): HasMany
    {
        return $this->hasMany(ContentBlock::class)->orderBy('column_position')->orderBy('order');
    }

    public function blocksInColumn(int $column): HasMany
    {
        return $this->hasMany(ContentBlock::class)
            ->where('column_position', $column)
            ->orderBy('order');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('section_type', $type);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    // Methods
    public function addBlock(array $blockData): ContentBlock
    {
        $blockData['layout_section_id'] = $this->id;
        $blockData['order'] = $blockData['order'] ?? $this->getNextBlockOrder($blockData['column_position'] ?? 1);

        return ContentBlock::create($blockData);
    }

    public function getNextBlockOrder(int $column = 1): int
    {
        return $this->blocksInColumn($column)->max('order') + 1;
    }

    public function updateColumnLayout(int $columnCount, array $widths = null): void
    {
        $this->update([
            'column_count' => $columnCount,
            'column_layout' => $widths ? 'custom' : 'equal',
            'column_widths' => $widths,
        ]);

        // Adjust blocks that exceed new column count
        $this->blocks()->where('column_position', '>', $columnCount)
            ->update(['column_position' => $columnCount]);
    }

    public function getColumnWidth(int $column): string
    {
        if ($this->column_layout === 'custom' && $this->column_widths) {
            return ($this->column_widths[$column - 1] ?? (100 / $this->column_count)) . '%';
        }

        return (100 / $this->column_count) . '%';
    }

    public function getColumnClasses(int $column): string
    {
        $baseClasses = 'section-column';

        if ($this->column_count > 1) {
            $baseClasses .= ' column-' . $column . ' columns-' . $this->column_count;
        }

        return $baseClasses;
    }

    public function getColumnLayoutStyle(): string
    {
        switch ($this->column_layout) {
            case 'flex':
                return 'display: flex; gap: 1rem;';
            case 'custom':
                if ($this->column_widths) {
                    $gridColumns = implode(' ', array_map(fn($w) => $w.'%', $this->column_widths));
                    return 'display: grid; grid-template-columns: ' . $gridColumns . '; gap: 1rem;';
                }
                // fallback to equal if custom widths not set
                return 'display: grid; grid-template-columns: repeat(' . $this->column_count . ', 1fr); gap: 1rem;';
            case 'equal':
            default:
                return 'display: grid; grid-template-columns: repeat(' . $this->column_count . ', 1fr); gap: 1rem;';
        }
    }

    public function getColumnStyle(int $column): string
    {
        if ($this->column_layout === 'flex') {
            return 'flex: 1;';
        }

        // For grid layouts, no additional column style needed
        return '';
    }

    public function duplicateToTemplate(TemplateLayout $template): self
    {
        $newSection = $this->replicate();
        $newSection->template_layout_id = $template->id;
        $newSection->save();

        // Duplicate blocks
        foreach ($this->blocks as $block) {
            $block->duplicateToSection($newSection);
        }

        return $newSection;
    }

    public function exportConfiguration(): array
    {
        return [
            'section' => $this->only([
                'section_type', 'section_name', 'column_count', 'column_layout',
                'column_widths', 'settings', 'styling', 'order', 'min_height',
                'background_color', 'border_style', 'padding', 'margin'
            ]),
            'blocks' => $this->blocks->map(function ($block) {
                return $block->exportConfiguration();
            })->toArray(),
        ];
    }

    public static function importConfiguration(array $config, int $templateLayoutId): self
    {
        $section = static::create(array_merge($config['section'], [
            'template_layout_id' => $templateLayoutId,
        ]));

        foreach ($config['blocks'] ?? [] as $blockConfig) {
            ContentBlock::importConfiguration($blockConfig, $section->id);
        }

        return $section;
    }

    public function renderHtml(array $data = []): string
    {
        $html = '<div class="layout-section section-' . $this->section_type . '"';

        // Add styling
        if ($this->styling || $this->background_color || $this->padding || $this->margin) {
            $styles = [];

            if ($this->background_color) {
                $styles[] = 'background-color: ' . $this->background_color;
            }

            if ($this->padding) {
                $padding = $this->padding;
                $styles[] = sprintf('padding: %dpx %dpx %dpx %dpx',
                    $padding['top'] ?? 0, $padding['right'] ?? 0,
                    $padding['bottom'] ?? 0, $padding['left'] ?? 0);
            }

            if ($this->margin) {
                $margin = $this->margin;
                $styles[] = sprintf('margin: %dpx %dpx %dpx %dpx',
                    $margin['top'] ?? 0, $margin['right'] ?? 0,
                    $margin['bottom'] ?? 0, $margin['left'] ?? 0);
            }

            if ($this->min_height) {
                $styles[] = 'min-height: ' . $this->min_height . 'px';
            }

            if (!empty($styles)) {
                $html .= ' style="' . implode('; ', $styles) . '"';
            }
        }

        $html .= '>';

        // Add columns
        if ($this->column_count > 1) {
            // Determine layout type
            $layoutStyle = $this->getColumnLayoutStyle();
            $html .= '<div class="section-columns" style="' . $layoutStyle . '">';

            for ($i = 1; $i <= $this->column_count; $i++) {
                $columnStyle = $this->getColumnStyle($i);
                $html .= '<div class="' . $this->getColumnClasses($i) . '" style="' . $columnStyle . '">';

                foreach ($this->blocksInColumn($i)->get() as $block) {
                    $html .= $block->renderHtml($data);
                }

                $html .= '</div>';
            }

            $html .= '</div>';
        } else {
            // Single column
            foreach ($this->blocks as $block) {
                $html .= $block->renderHtml($data);
            }
        }

        $html .= '</div>';

        return $html;
    }
}
