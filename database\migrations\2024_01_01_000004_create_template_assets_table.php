<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_assets', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('template_id');
            $table->enum('asset_type', [
                'image',       // Images (logos, backgrounds, etc.)
                'font',        // Custom fonts
                'css',         // Custom CSS files
                'preview'      // Preview images
            ]);
            $table->string('file_path');
            $table->string('file_name');
            $table->string('original_name');
            $table->string('mime_type');
            $table->unsignedBigInteger('file_size');
            $table->json('metadata')->nullable(); // Additional metadata
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('template_id')->references('id')->on('invoice_templates')->onDelete('cascade');

            // Indexes
            $table->index(['template_id', 'asset_type']);
            $table->index('asset_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_assets');
    }
};
