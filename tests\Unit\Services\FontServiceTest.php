<?php

namespace Tests\Unit\Services;

use App\Services\FontService;
use Tests\TestCase;

class FontServiceTest extends TestCase
{
    /** @test */
    public function it_returns_available_fonts()
    {
        $fonts = FontService::getAvailableFonts();

        $this->assertIsArray($fonts);
        $this->assertNotEmpty($fonts);

        // Check if DejaVu Sans is available (recommended font)
        $this->assertArrayHasKey('DejaVu Sans', $fonts);

        // Check font structure
        $dejaVuSans = $fonts['DejaVu Sans'];
        $this->assertArrayHasKey('name', $dejaVuSans);
        $this->assertArrayHasKey('category', $dejaVuSans);
        $this->assertArrayHasKey('pdf_compatible', $dejaVuSans);
        $this->assertArrayHasKey('unicode_support', $dejaVuSans);
        $this->assertArrayHas<PERSON>ey('description', $dejaVuSans);
    }

    /** @test */
    public function it_returns_select_options_for_filament()
    {
        $options = FontService::getSelectOptions();

        $this->assertIsArray($options);
        $this->assertNotEmpty($options);

        // Check if it's in key => value format suitable for Filament select
        foreach ($options as $key => $value) {
            $this->assertIsString($key);
            $this->assertIsString($value);
        }

        // Check if recommended font is included
        $this->assertArrayHasKey('DejaVu Sans', $options);

        // Check if groups are present
        $this->assertArrayHasKey('--- PDF Compatible (Recommended) ---', $options);
        $this->assertArrayHasKey('--- Generic Fallbacks ---', $options);
    }

    /** @test */
    public function it_filters_only_serif_and_sans_serif_fonts()
    {
        $options = FontService::getSelectOptions(false);

        // Should not contain monospace fonts in main options
        $this->assertArrayNotHasKey('DejaVu Sans Mono', $options);

        // Should contain serif and sans-serif fonts
        $this->assertArrayHasKey('DejaVu Sans', $options);
        $this->assertArrayHasKey('DejaVu Serif', $options);
        $this->assertArrayHasKey('Arial', $options);
        $this->assertArrayHasKey('Times New Roman', $options);
    }

    /** @test */
    public function it_returns_recommended_fonts()
    {
        $recommendedFonts = FontService::getRecommendedFonts();

        $this->assertIsArray($recommendedFonts);
        $this->assertNotEmpty($recommendedFonts);

        // All recommended fonts should be PDF compatible and have Unicode support
        foreach ($recommendedFonts as $font) {
            $this->assertTrue($font['pdf_compatible']);
            $this->assertTrue($font['unicode_support']);
        }

        // DejaVu Sans should be in recommended fonts
        $this->assertArrayHasKey('DejaVu Sans', $recommendedFonts);
    }

    /** @test */
    public function it_returns_fonts_by_category()
    {
        $sansSerifFonts = FontService::getFontsByCategory('sans-serif');
        $serifFonts = FontService::getFontsByCategory('serif');
        $monospaceFonts = FontService::getFontsByCategory('monospace');

        $this->assertIsArray($sansSerifFonts);
        $this->assertIsArray($serifFonts);
        $this->assertIsArray($monospaceFonts);

        // Check if fonts are correctly categorized
        foreach ($sansSerifFonts as $font) {
            $this->assertEquals('sans-serif', $font['category']);
        }

        foreach ($serifFonts as $font) {
            $this->assertEquals('serif', $font['category']);
        }

        foreach ($monospaceFonts as $font) {
            $this->assertEquals('monospace', $font['category']);
        }
    }

    /** @test */
    public function it_validates_pdf_compatibility()
    {
        // Test PDF compatible fonts
        $this->assertTrue(FontService::isPdfCompatible('DejaVu Sans'));
        $this->assertTrue(FontService::isPdfCompatible('Arial'));
        $this->assertTrue(FontService::isPdfCompatible('Times New Roman'));

        // Test non-existent font
        $this->assertFalse(FontService::isPdfCompatible('NonExistentFont'));
    }

    /** @test */
    public function it_generates_font_css()
    {
        $css = FontService::getFontCss('DejaVu Sans');
        $this->assertStringContainsString('DejaVu Sans', $css);
        $this->assertStringContainsString('Arial', $css); // fallback
        $this->assertStringContainsString('sans-serif', $css); // generic fallback

        $css = FontService::getFontCss('Times New Roman');
        $this->assertStringContainsString('Times New Roman', $css);
        $this->assertStringContainsString('serif', $css);

        // Test non-existent font returns default
        $css = FontService::getFontCss('NonExistentFont');
        $this->assertEquals('sans-serif', $css);
    }

    /** @test */
    public function it_returns_default_font_for_template_types()
    {
        $invoiceFont = FontService::getDefaultFont('invoice');
        $this->assertEquals('DejaVu Sans', $invoiceFont);

        $formalFont = FontService::getDefaultFont('formal');
        $this->assertEquals('DejaVu Serif', $formalFont);

        $technicalFont = FontService::getDefaultFont('technical');
        $this->assertEquals('DejaVu Sans Mono', $technicalFont);

        // Test unknown type returns default
        $unknownFont = FontService::getDefaultFont('unknown');
        $this->assertEquals('DejaVu Sans', $unknownFont);
    }

    /** @test */
    public function it_returns_font_size_recommendations()
    {
        $recommendations = FontService::getFontSizeRecommendations();

        $this->assertIsArray($recommendations);
        $this->assertArrayHasKey('small', $recommendations);
        $this->assertArrayHasKey('normal', $recommendations);
        $this->assertArrayHasKey('large', $recommendations);
        $this->assertArrayHasKey('xlarge', $recommendations);

        // Check structure
        foreach ($recommendations as $size => $config) {
            $this->assertArrayHasKey('size', $config);
            $this->assertArrayHasKey('description', $config);
            $this->assertIsInt($config['size']);
            $this->assertIsString($config['description']);
        }

        // Check normal size is 12pt
        $this->assertEquals(12, $recommendations['normal']['size']);
    }

    /** @test */
    public function it_has_all_required_font_categories()
    {
        $fonts = FontService::getAvailableFonts();
        $categories = array_unique(array_column($fonts, 'category'));

        $this->assertContains('sans-serif', $categories);
        $this->assertContains('serif', $categories);
        $this->assertContains('monospace', $categories);
    }

    /** @test */
    public function it_has_dejavu_fonts_as_recommended()
    {
        $fonts = FontService::getAvailableFonts();

        // All DejaVu fonts should be PDF compatible and have Unicode support
        $dejaVuFonts = array_filter($fonts, function ($font, $key) {
            return strpos($key, 'DejaVu') === 0;
        }, ARRAY_FILTER_USE_BOTH);

        $this->assertNotEmpty($dejaVuFonts);

        foreach ($dejaVuFonts as $font) {
            $this->assertTrue($font['pdf_compatible']);
            $this->assertTrue($font['unicode_support']);
        }
    }
}
