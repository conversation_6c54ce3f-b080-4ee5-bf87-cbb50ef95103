<?php

namespace App\Filament\Admin\Resources\InvoiceTemplateResource\Pages;

use App\Filament\Admin\Resources\InvoiceTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewInvoiceTemplate extends ViewRecord
{
    protected static string $resource = InvoiceTemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function getTitle(): string
    {
        return 'View Template: ' . $this->record->name;
    }
}
