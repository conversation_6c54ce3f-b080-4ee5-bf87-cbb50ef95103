<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('content_blocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('layout_section_id')->constrained()->onDelete('cascade');
            $table->enum('block_type', [
                'logo', 'text', 'field', 'table', 'custom', 
                'company_info', 'client_info', 'invoice_details',
                'bank_info', 'signature', 'terms', 'calculations'
            ]);
            $table->string('block_name')->nullable(); // Custom block names
            $table->integer('column_position')->default(1); // Which column (1-12)
            $table->integer('column_span')->default(1); // How many columns to span
            $table->json('content_data')->nullable(); // Block-specific content
            $table->json('field_mapping')->nullable(); // Dynamic field mappings
            $table->json('styling')->nullable(); // CSS styling options
            $table->json('configuration')->nullable(); // Block configuration
            $table->integer('order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_required')->default(false);
            $table->string('alignment')->default('left'); // left, center, right, justify
            $table->integer('font_size')->nullable();
            $table->string('font_weight')->nullable(); // normal, bold
            $table->string('font_style')->nullable(); // normal, italic
            $table->string('text_color')->nullable();
            $table->string('background_color')->nullable();
            $table->json('border')->nullable(); // Border styling
            $table->json('padding')->nullable();
            $table->json('margin')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['layout_section_id', 'block_type']);
            $table->index(['layout_section_id', 'column_position', 'order']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('content_blocks');
    }
};
