<?php

/**
 * Simple script to create combined blocks
 * Run via: php run_combined_blocks.php
 */

// Include Laravel bootstrap
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Creating Combined Company Info Block Templates ===\n";

try {
    // 1. Create combined company contact block
    echo "1. Creating combined company contact block...\n";
    
    $combinedContactBlock = App\Models\BlockTemplate::updateOrCreate(
        ['block_type' => 'company_contact_combined'],
        [
            'name' => 'Company Contact Info (Combined)',
            'description' => 'Company address, phone, email, website, and fax in one block',
            'category' => 'company',
            'template_data' => [
                'fields' => [
                    'address' => [
                        'field_path' => 'company.address',
                        'label' => 'Address',
                        'format' => 'html',
                        'show_label' => false,
                        'order' => 1
                    ],
                    'phone' => [
                        'field_path' => 'company.phone',
                        'label' => 'Phone',
                        'format' => 'text',
                        'prefix' => 'Phone: ',
                        'show_label' => false,
                        'order' => 2
                    ],
                    'email' => [
                        'field_path' => 'company.email',
                        'label' => 'Email',
                        'format' => 'email',
                        'prefix' => 'Email: ',
                        'show_label' => false,
                        'order' => 3
                    ],
                    'website' => [
                        'field_path' => 'company.website',
                        'label' => 'Website',
                        'format' => 'url',
                        'prefix' => 'Website: ',
                        'show_label' => false,
                        'order' => 4
                    ],
                    'fax' => [
                        'field_path' => 'company.fax',
                        'label' => 'Fax',
                        'format' => 'text',
                        'prefix' => 'Fax: ',
                        'show_label' => false,
                        'order' => 5
                    ]
                ],
                'layout' => 'vertical',
                'spacing' => 'compact'
            ],
            'field_mappings' => [
                'fields' => ['company.address', 'company.phone', 'company.email', 'company.website', 'company.fax'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 11,
                'line_height' => 1.4
            ],
            'preview_html' => '<div class="text-sm text-gray-600 space-y-1">
                <div>Jl. Sample Street No. 123<br>Jakarta Pusat 10220</div>
                <div>Phone: +62 21 1234 5678</div>
                <div>Email: <span class="text-blue-600"><EMAIL></span></div>
                <div>Website: <span class="text-blue-600">www.company.com</span></div>
                <div>Fax: +62 21 1234 5679</div>
            </div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'contact', 'combined', 'address', 'phone', 'email', 'website', 'fax'],
            'usage_count' => 0
        ]
    );
    
    echo "   ✅ Created: {$combinedContactBlock->name}\n";
    
    // 2. Create company header block (logo + name + tagline)
    echo "\n2. Creating company header block...\n";
    
    $companyHeaderBlock = App\Models\BlockTemplate::updateOrCreate(
        ['block_type' => 'company_header_combined'],
        [
            'name' => 'Company Header (Logo + Name)',
            'description' => 'Company logo and name side by side',
            'category' => 'company',
            'template_data' => [
                'layout' => 'horizontal',
                'logo' => [
                    'field_path' => 'company.logo',
                    'width' => '80px',
                    'height' => 'auto',
                    'position' => 'left'
                ],
                'name' => [
                    'field_path' => 'company.name',
                    'heading_size_path' => 'company.heading_size',
                    'text_color_path' => 'company.text_color',
                    'position' => 'right',
                    'vertical_align' => 'middle'
                ],
                'spacing' => '15px'
            ],
            'field_mappings' => [
                'fields' => ['company.logo', 'company.name'],
                'format' => 'header_layout'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'display' => 'flex',
                'align_items' => 'center'
            ],
            'preview_html' => '<div class="flex items-center space-x-4">
                <div class="w-16 h-12 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500 rounded">[LOGO]</div>
                <div class="text-lg font-bold text-gray-800">PT. Sample Company</div>
            </div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'header', 'logo', 'name', 'combined'],
            'usage_count' => 0
        ]
    );
    
    echo "   ✅ Created: {$companyHeaderBlock->name}\n";
    
    echo "\n=== Combined Block Templates Created Successfully ===\n";
    echo "📊 Total combined blocks created: 2\n";
    echo "✅ Company Contact Info (Combined)\n";
    echo "✅ Company Header (Logo + Name)\n";
    
    echo "\n🎯 Benefits:\n";
    echo "✅ Faster template building\n";
    echo "✅ Consistent spacing and alignment\n";
    echo "✅ Pre-configured field relationships\n";
    echo "✅ Professional layouts out of the box\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🚀 Next: Test the combined blocks in Visual Builder!\n";
