<?php

/**
 * Test Layout Fix - Visual Builder v2.0
 * Run via: php artisan tinker
 */

echo "=== Testing Layout Fix ===\n";

try {
    // 1. Ensure we have system blocks
    echo "1. Checking system blocks...\n";
    
    $blockCount = App\Models\BlockTemplate::where('is_system', true)->count();
    if ($blockCount === 0) {
        echo "   Creating system blocks...\n";
        App\Models\BlockTemplate::createSystemTemplates();
        $blockCount = App\Models\BlockTemplate::where('is_system', true)->count();
    }
    echo "   ✅ System blocks: {$blockCount}\n";
    
    // 2. Ensure we have a test template
    echo "\n2. Checking test template...\n";
    
    $testTemplate = App\Models\TemplateLayout::where('name', 'Test Visual Builder Template')->first();
    if (!$testTemplate) {
        echo "   Creating test template...\n";
        $testTemplate = App\Models\TemplateLayout::create([
            'name' => 'Test Visual Builder Template',
            'description' => 'Test template for layout testing',
            'company_id' => null,
            'version' => 'v2',
            'page_size' => 'legal',
            'orientation' => 'portrait',
            'font_family' => 'DejaVu Sans',
            'font_size' => 12,
            'is_active' => true,
            'is_default' => false,
            'created_by' => 1,
        ]);
    }
    echo "   ✅ Test template: {$testTemplate->name} (ID: {$testTemplate->id})\n";
    
    // 3. Create sections if they don't exist
    echo "\n3. Checking sections...\n";
    
    $headerSection = App\Models\LayoutSection::firstOrCreate([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'header'
    ], [
        'section_name' => 'Header Section',
        'column_count' => 3,
        'column_layout' => 'equal',
        'order' => 0,
        'is_active' => true,
        'min_height' => 100,
    ]);
    
    $bodySection = App\Models\LayoutSection::firstOrCreate([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'body'
    ], [
        'section_name' => 'Body Section',
        'column_count' => 1,
        'column_layout' => 'equal',
        'order' => 0,
        'is_active' => true,
        'min_height' => 200,
    ]);
    
    echo "   ✅ Header section: {$headerSection->id}\n";
    echo "   ✅ Body section: {$bodySection->id}\n";
    
    // 4. Add some blocks for testing
    echo "\n4. Adding test blocks...\n";
    
    $logoBlock = App\Models\BlockTemplate::where('block_type', 'logo')->where('is_system', true)->first();
    $companyInfoBlock = App\Models\BlockTemplate::where('block_type', 'company_info')->where('is_system', true)->first();
    $tableBlock = App\Models\BlockTemplate::where('block_type', 'table')->where('is_system', true)->first();
    
    // Clear existing blocks first
    App\Models\ContentBlock::where('layout_section_id', $headerSection->id)->delete();
    App\Models\ContentBlock::where('layout_section_id', $bodySection->id)->delete();
    
    if ($logoBlock) {
        $logoBlock->createContentBlock($headerSection->id, ['column_position' => 1]);
        echo "   ✅ Logo block added to header column 1\n";
    }
    
    if ($companyInfoBlock) {
        $companyInfoBlock->createContentBlock($headerSection->id, ['column_position' => 2]);
        echo "   ✅ Company info block added to header column 2\n";
    }
    
    if ($tableBlock) {
        $tableBlock->createContentBlock($bodySection->id, ['column_position' => 1]);
        echo "   ✅ Table block added to body\n";
    }
    
    // 5. Test drag & drop data structure
    echo "\n5. Testing drag & drop data structure...\n";
    
    $availableBlocks = App\Models\BlockTemplate::active()
        ->orderBy('category')
        ->orderBy('usage_count', 'desc')
        ->get()
        ->groupBy('category')
        ->map(function ($blocks, $category) {
            return [
                'category' => $category,
                'label' => ucfirst($category),
                'blocks' => $blocks->map(function ($block) {
                    return [
                        'id' => $block->id,
                        'name' => $block->name,
                        'description' => $block->description,
                        'block_type' => $block->block_type,
                        'preview_html' => $block->getPreviewHtml(),
                        'tags' => $block->tags ?? [],
                    ];
                })->toArray()
            ];
        })
        ->values()
        ->toArray();
    
    echo "   ✅ Available block categories: " . count($availableBlocks) . "\n";
    foreach ($availableBlocks as $category) {
        echo "      - {$category['label']}: " . count($category['blocks']) . " blocks\n";
    }
    
    // 6. Test URLs
    echo "\n6. Testing URLs...\n";
    
    $visualBuilderUrl = "/admin/visual-template-builder?template={$testTemplate->id}";
    $templateManagementUrl = "/admin/template-layouts";
    $previewUrl = "/visual-template/preview/{$testTemplate->id}";
    
    echo "   🎨 Visual Builder: {$visualBuilderUrl}\n";
    echo "   📋 Template Management: {$templateManagementUrl}\n";
    echo "   👁️ Preview: {$previewUrl}\n";
    
    // 7. Layout improvements summary
    echo "\n=== Layout Improvements Summary ===\n";
    echo "✅ Content Blocks column: Reduced to w-64 (256px)\n";
    echo "✅ Canvas area: Now flex-1 (takes remaining space)\n";
    echo "✅ Block items: More compact with smaller padding\n";
    echo "✅ Category headers: Smaller and uppercase\n";
    echo "✅ Spacing: Reduced gaps for better use of space\n";
    echo "✅ Heights: Proper flex layout for full height usage\n";
    
    // 8. Drag & drop testing instructions
    echo "\n=== Drag & Drop Testing Instructions ===\n";
    echo "1. Open Visual Builder: {$visualBuilderUrl}\n";
    echo "2. You should see:\n";
    echo "   - Left: Compact content blocks palette (256px width)\n";
    echo "   - Right: Large canvas area (remaining space)\n";
    echo "3. Try dragging blocks from left to canvas sections\n";
    echo "4. Distance should be much shorter now\n";
    echo "5. Test section settings and block settings buttons\n";
    echo "6. Test preview functionality\n";
    
    // 9. Performance check
    echo "\n9. Performance check...\n";
    
    $start = microtime(true);
    $template = App\Models\TemplateLayout::with(['sections.blocks'])->find($testTemplate->id);
    $end = microtime(true);
    $loadTime = round(($end - $start) * 1000, 2);
    
    echo "   ⚡ Template load time: {$loadTime}ms\n";
    echo "   📊 Sections: " . $template->sections->count() . "\n";
    echo "   📊 Total blocks: " . $template->sections->sum(fn($s) => $s->blocks->count()) . "\n";
    
    echo "\n=== Layout Fix Complete ===\n";
    echo "🎯 Visual Builder now has optimal layout:\n";
    echo "   - Compact blocks palette (left)\n";
    echo "   - Dominant canvas area (right)\n";
    echo "   - Shorter drag distance\n";
    echo "   - Better space utilization\n";
    
    echo "\n🚀 Ready for testing!\n";
    echo "Access: {$visualBuilderUrl}\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
