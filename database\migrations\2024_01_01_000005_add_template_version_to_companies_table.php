<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->enum('template_version', ['v1', 'v2'])->default('v1')->after('email');
            $table->enum('migration_status', ['pending', 'in_progress', 'completed'])->default('pending')->after('template_version');
            $table->json('legacy_backup')->nullable()->after('migration_status');
            $table->foreignId('default_template_layout_id')->nullable()->constrained('template_layouts')->onDelete('set null')->after('legacy_backup');
            $table->timestamp('template_migrated_at')->nullable()->after('default_template_layout_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropForeign(['default_template_layout_id']);
            $table->dropColumn([
                'template_version',
                'migration_status', 
                'legacy_backup',
                'default_template_layout_id',
                'template_migrated_at'
            ]);
        });
    }
};
