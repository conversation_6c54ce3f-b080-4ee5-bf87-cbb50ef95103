<?php

/**
 * Quick Fix Components Script
 * Run via: php artisan tinker
 */

echo "=== Quick Fix Components ===\n";

// Delete all existing broken components
App\Models\TemplateComponent::truncate();
echo "✅ Cleared existing components\n";

// Create fresh components with proper data
$components = [
    [
        'name' => 'Company Header',
        'type' => 'kop',
        'description' => 'Company logo and information',
        'preview_html' => '<div class="text-center"><h2>PT. Sample Company</h2><p>Jl. Sample Address No. 123</p></div>',
        'template_html' => '<div class="kop-section text-{{config.alignment}}"><h2>{{company.name}}</h2><p>{{company.address}}</p></div>',
        'config_schema' => json_encode([
            'alignment' => ['type' => 'select', 'options' => ['left', 'center', 'right'], 'default' => 'center'],
            'font_size' => ['type' => 'select', 'options' => [12, 14, 16, 18], 'default' => 14]
        ]),
        'default_config' => json_encode(['alignment' => 'center', 'font_size' => 14])
    ],
    [
        'name' => 'Bill To Information',
        'type' => 'billto',
        'description' => 'Client billing information',
        'preview_html' => '<div><strong>Bill To:</strong><br>Client Name<br>Client Address</div>',
        'template_html' => '<div class="billto-section"><strong>Bill To:</strong><br>{{client.name}}<br>{{client.address}}</div>',
        'config_schema' => json_encode([
            'show_title' => ['type' => 'checkbox', 'default' => true],
            'title_text' => ['type' => 'text', 'default' => 'Bill To:']
        ]),
        'default_config' => json_encode(['show_title' => true, 'title_text' => 'Bill To:'])
    ],
    [
        'name' => 'Invoice Items Table',
        'type' => 'table',
        'description' => 'Table of invoice items',
        'preview_html' => '<table border="1" style="width:100%; border-collapse: collapse;"><tr><th>Description</th><th>Qty</th><th>Price</th><th>Total</th></tr><tr><td>Sample Item</td><td>1</td><td>$100.00</td><td>$100.00</td></tr></table>',
        'template_html' => '<table class="invoice-table" style="width:100%; border-collapse: collapse;"><thead><tr><th>Description</th><th>Quantity</th><th>Price</th><th>Total</th></tr></thead><tbody><tr><td>Sample Service</td><td>1</td><td>$100.00</td><td>$100.00</td></tr></tbody></table>',
        'config_schema' => json_encode([
            'show_borders' => ['type' => 'checkbox', 'default' => true],
            'header_style' => ['type' => 'select', 'options' => ['normal', 'bold', 'colored'], 'default' => 'bold']
        ]),
        'default_config' => json_encode(['show_borders' => true, 'header_style' => 'bold'])
    ],
    [
        'name' => 'Amount in Words',
        'type' => 'inwords',
        'description' => 'Total amount spelled out in words',
        'preview_html' => '<div style="font-style: italic;"><em>Amount in words: One Hundred Dollars</em></div>',
        'template_html' => '<div class="amount-words" style="font-style: italic;"><em>Amount in words: One Hundred Dollars</em></div>',
        'config_schema' => json_encode([
            'prefix' => ['type' => 'text', 'default' => 'Amount in words: '],
            'style' => ['type' => 'select', 'options' => ['italic', 'normal', 'bold'], 'default' => 'italic']
        ]),
        'default_config' => json_encode(['prefix' => 'Amount in words: ', 'style' => 'italic'])
    ],
    [
        'name' => 'Bank Information',
        'type' => 'bankinfo',
        'description' => 'Payment and bank details',
        'preview_html' => '<div><strong>Bank Details:</strong><br>Bank: Sample Bank<br>Account: **********<br>Account Name: PT. Sample Company</div>',
        'template_html' => '<div class="bank-info"><strong>Bank Details:</strong><br>Bank: Sample Bank<br>Account: **********<br>Account Name: PT. Sample Company</div>',
        'config_schema' => json_encode([
            'layout' => ['type' => 'select', 'options' => ['simple', 'boxed', 'table'], 'default' => 'simple']
        ]),
        'default_config' => json_encode(['layout' => 'simple'])
    ]
];

foreach ($components as $componentData) {
    $component = App\Models\TemplateComponent::create($componentData);
    echo "✅ Created: {$component->name} (ID: {$component->id})\n";
}

echo "\n=== Verification ===\n";
$totalComponents = App\Models\TemplateComponent::count();
echo "Total components: {$totalComponents}\n";

$componentsWithTemplate = App\Models\TemplateComponent::whereNotNull('template_html')->where('template_html', '!=', '')->count();
echo "Components with template_html: {$componentsWithTemplate}\n";

if ($totalComponents === $componentsWithTemplate) {
    echo "✅ All components have valid template_html\n";
} else {
    echo "❌ Some components missing template_html\n";
}

echo "\n=== Quick Fix Complete ===\n";
echo "🎯 Now try the Template Builder again!\n";
