<?php

namespace Tests\Unit\Models;

use App\Models\InvoiceTemplate;
use App\Models\TemplateSection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TemplateSectionTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_belongs_to_a_template()
    {
        $template = InvoiceTemplate::factory()->create();
        $section = TemplateSection::factory()->create(['template_id' => $template->id]);

        $this->assertInstanceOf(InvoiceTemplate::class, $section->template);
        $this->assertEquals($template->id, $section->template->id);
    }

    /** @test */
    public function it_can_scope_active_sections()
    {
        TemplateSection::factory()->count(3)->create(['is_active' => true]);
        TemplateSection::factory()->count(2)->create(['is_active' => false]);

        $activeSections = TemplateSection::active()->get();

        $this->assertCount(3, $activeSections);
        $activeSections->each(function ($section) {
            $this->assertTrue($section->is_active);
        });
    }

    /** @test */
    public function it_can_scope_sections_by_type()
    {
        TemplateSection::factory()->count(2)->create(['section_type' => 'header']);
        TemplateSection::factory()->count(3)->create(['section_type' => 'table']);
        TemplateSection::factory()->count(1)->create(['section_type' => 'footer']);

        $headerSections = TemplateSection::byType('header')->get();
        $tableSections = TemplateSection::byType('table')->get();

        $this->assertCount(2, $headerSections);
        $this->assertCount(3, $tableSections);
        
        $headerSections->each(function ($section) {
            $this->assertEquals('header', $section->section_type);
        });
    }

    /** @test */
    public function it_can_scope_ordered_sections()
    {
        $template = InvoiceTemplate::factory()->create();
        
        TemplateSection::factory()->create([
            'template_id' => $template->id,
            'sort_order' => 3
        ]);
        TemplateSection::factory()->create([
            'template_id' => $template->id,
            'sort_order' => 1
        ]);
        TemplateSection::factory()->create([
            'template_id' => $template->id,
            'sort_order' => 2
        ]);

        $orderedSections = TemplateSection::ordered()->get();

        $this->assertEquals(1, $orderedSections->first()->sort_order);
        $this->assertEquals(3, $orderedSections->last()->sort_order);
    }

    /** @test */
    public function it_can_get_and_set_config_values()
    {
        $section = TemplateSection::factory()->create([
            'section_config' => [
                'height' => 100,
                'padding' => ['top' => 10, 'bottom' => 10]
            ]
        ]);

        // Test getting config values
        $this->assertEquals(100, $section->getConfig('height'));
        $this->assertEquals(10, $section->getConfig('padding.top'));
        $this->assertNull($section->getConfig('nonexistent'));
        $this->assertEquals('default', $section->getConfig('nonexistent', 'default'));

        // Test setting config values
        $section->setConfig('width', 200);
        $section->setConfig('padding.left', 15);

        $this->assertEquals(200, $section->getConfig('width'));
        $this->assertEquals(15, $section->getConfig('padding.left'));
    }

    /** @test */
    public function it_can_check_section_types()
    {
        $headerSection = TemplateSection::factory()->create(['section_type' => 'header']);
        $billtoSection = TemplateSection::factory()->create(['section_type' => 'billto']);
        $tableSection = TemplateSection::factory()->create(['section_type' => 'table']);
        $inwordsSection = TemplateSection::factory()->create(['section_type' => 'inwords']);
        $footerSection = TemplateSection::factory()->create(['section_type' => 'footer']);
        $customSection = TemplateSection::factory()->create(['section_type' => 'custom']);

        $this->assertTrue($headerSection->isHeader());
        $this->assertFalse($headerSection->isBillTo());

        $this->assertTrue($billtoSection->isBillTo());
        $this->assertFalse($billtoSection->isTable());

        $this->assertTrue($tableSection->isTable());
        $this->assertFalse($tableSection->isInWords());

        $this->assertTrue($inwordsSection->isInWords());
        $this->assertFalse($inwordsSection->isFooter());

        $this->assertTrue($footerSection->isFooter());
        $this->assertFalse($footerSection->isCustom());

        $this->assertTrue($customSection->isCustom());
        $this->assertFalse($customSection->isHeader());
    }

    /** @test */
    public function it_casts_section_config_to_array()
    {
        $config = [
            'height' => 100,
            'padding' => ['top' => 10, 'bottom' => 10],
            'background_color' => '#ffffff'
        ];

        $section = TemplateSection::factory()->create(['section_config' => $config]);

        $this->assertIsArray($section->section_config);
        $this->assertEquals($config, $section->section_config);
    }

    /** @test */
    public function it_casts_is_active_to_boolean()
    {
        $activeSection = TemplateSection::factory()->create(['is_active' => 1]);
        $inactiveSection = TemplateSection::factory()->create(['is_active' => 0]);

        $this->assertIsBool($activeSection->is_active);
        $this->assertIsBool($inactiveSection->is_active);
        $this->assertTrue($activeSection->is_active);
        $this->assertFalse($inactiveSection->is_active);
    }

    /** @test */
    public function it_casts_sort_order_to_integer()
    {
        $section = TemplateSection::factory()->create(['sort_order' => '5']);

        $this->assertIsInt($section->sort_order);
        $this->assertEquals(5, $section->sort_order);
    }
}
