<?php

/**
 * Fix Template Components Script
 * Run via: php artisan tinker
 */

echo "=== Fixing Template Components ===\n";

try {
    // Check existing components
    $components = App\Models\TemplateComponent::all();
    echo "Found " . $components->count() . " components\n";
    
    foreach ($components as $component) {
        echo "\nChecking component: {$component->name}\n";
        
        $needsUpdate = false;
        $updates = [];
        
        // Check template_html
        if (empty($component->template_html)) {
            echo "   ❌ Missing template_html\n";
            $needsUpdate = true;
            
            // Generate basic template based on type
            switch ($component->type) {
                case 'kop':
                    $updates['template_html'] = '<div class="kop-section text-{{config.alignment}} text-{{config.font_size}}pt"><h2>{{company.name}}</h2><p>{{company.address}}</p></div>';
                    break;
                case 'table':
                    $updates['template_html'] = '<table class="invoice-table {{config.show_borders ? \'bordered\' : \'\'}}"><thead><tr class="{{config.header_style}}"><th>Description</th><th>Quantity</th><th>Price</th><th>Total</th></tr></thead><tbody>{{#each items}}<tr><td>{{description}}</td><td>{{quantity}}</td><td>{{price}}</td><td>{{total}}</td></tr>{{/each}}</tbody></table>';
                    break;
                case 'bankinfo':
                    $updates['template_html'] = '<div class="bank-info {{config.layout}}"><strong>Bank Details:</strong><br>Bank: {{bank.name}}<br>Account: {{bank.account_number}}</div>';
                    break;
                case 'billto':
                    $updates['template_html'] = '<div class="billto-section"><strong>{{config.title_text}}</strong><br>{{client.name}}<br>{{client.address}}</div>';
                    break;
                case 'inwords':
                    $updates['template_html'] = '<div class="amount-words {{config.style}}">{{config.prefix}}{{totals.total_words}}</div>';
                    break;
                default:
                    $updates['template_html'] = '<div class="component-{{type}}">{{component.name}}</div>';
            }
        } else {
            echo "   ✅ Has template_html\n";
        }
        
        // Check preview_html
        if (empty($component->preview_html)) {
            echo "   ❌ Missing preview_html\n";
            $needsUpdate = true;
            
            // Generate basic preview based on type
            switch ($component->type) {
                case 'kop':
                    $updates['preview_html'] = '<div class="text-center"><h2>Company Name</h2><p>Company Address</p></div>';
                    break;
                case 'table':
                    $updates['preview_html'] = '<table border="1"><tr><th>Description</th><th>Qty</th><th>Price</th><th>Total</th></tr><tr><td>Sample Item</td><td>1</td><td>$100</td><td>$100</td></tr></table>';
                    break;
                case 'bankinfo':
                    $updates['preview_html'] = '<div><strong>Bank Details:</strong><br>Bank: Sample Bank<br>Account: **********</div>';
                    break;
                case 'billto':
                    $updates['preview_html'] = '<div><strong>Bill To:</strong><br>Client Name<br>Client Address</div>';
                    break;
                case 'inwords':
                    $updates['preview_html'] = '<div><em>Amount in words: One Thousand Dollars</em></div>';
                    break;
                default:
                    $updates['preview_html'] = '<div>Preview for ' . $component->name . '</div>';
            }
        } else {
            echo "   ✅ Has preview_html\n";
        }
        
        // Check config_schema
        if (empty($component->config_schema)) {
            echo "   ❌ Missing config_schema\n";
            $needsUpdate = true;
            
            // Generate basic config schema based on type
            switch ($component->type) {
                case 'kop':
                    $updates['config_schema'] = json_encode([
                        'alignment' => ['type' => 'select', 'options' => ['left', 'center', 'right'], 'default' => 'center'],
                        'font_size' => ['type' => 'select', 'options' => [12, 14, 16, 18], 'default' => 14]
                    ]);
                    break;
                case 'table':
                    $updates['config_schema'] = json_encode([
                        'show_borders' => ['type' => 'checkbox', 'default' => true],
                        'header_style' => ['type' => 'select', 'options' => ['normal', 'bold', 'colored'], 'default' => 'bold']
                    ]);
                    break;
                case 'bankinfo':
                    $updates['config_schema'] = json_encode([
                        'layout' => ['type' => 'select', 'options' => ['simple', 'boxed', 'table'], 'default' => 'simple']
                    ]);
                    break;
                case 'billto':
                    $updates['config_schema'] = json_encode([
                        'show_title' => ['type' => 'checkbox', 'default' => true],
                        'title_text' => ['type' => 'text', 'default' => 'Bill To:']
                    ]);
                    break;
                case 'inwords':
                    $updates['config_schema'] = json_encode([
                        'prefix' => ['type' => 'text', 'default' => 'Amount in words: '],
                        'style' => ['type' => 'select', 'options' => ['italic', 'normal', 'bold'], 'default' => 'italic']
                    ]);
                    break;
                default:
                    $updates['config_schema'] = json_encode([]);
            }
        } else {
            echo "   ✅ Has config_schema\n";
        }
        
        // Check default_config
        if (empty($component->default_config)) {
            echo "   ❌ Missing default_config\n";
            $needsUpdate = true;
            
            // Generate basic default config based on type
            switch ($component->type) {
                case 'kop':
                    $updates['default_config'] = json_encode(['alignment' => 'center', 'font_size' => 14]);
                    break;
                case 'table':
                    $updates['default_config'] = json_encode(['show_borders' => true, 'header_style' => 'bold']);
                    break;
                case 'bankinfo':
                    $updates['default_config'] = json_encode(['layout' => 'simple']);
                    break;
                case 'billto':
                    $updates['default_config'] = json_encode(['show_title' => true, 'title_text' => 'Bill To:']);
                    break;
                case 'inwords':
                    $updates['default_config'] = json_encode(['prefix' => 'Amount in words: ', 'style' => 'italic']);
                    break;
                default:
                    $updates['default_config'] = json_encode([]);
            }
        } else {
            echo "   ✅ Has default_config\n";
        }
        
        // Update component if needed
        if ($needsUpdate) {
            $component->update($updates);
            echo "   ✅ Component updated\n";
        } else {
            echo "   ✅ Component is complete\n";
        }
    }
    
    // Create missing basic components if none exist
    if ($components->count() === 0) {
        echo "\nNo components found, creating basic set...\n";
        
        $basicComponents = [
            [
                'name' => 'Company Header',
                'type' => 'kop',
                'description' => 'Company logo and information',
                'preview_html' => '<div class="text-center"><h2>Company Name</h2><p>Company Address</p></div>',
                'template_html' => '<div class="kop-section text-{{config.alignment}} text-{{config.font_size}}pt"><h2>{{company.name}}</h2><p>{{company.address}}</p></div>',
                'config_schema' => json_encode([
                    'alignment' => ['type' => 'select', 'options' => ['left', 'center', 'right'], 'default' => 'center'],
                    'font_size' => ['type' => 'select', 'options' => [12, 14, 16, 18], 'default' => 14]
                ]),
                'default_config' => json_encode(['alignment' => 'center', 'font_size' => 14])
            ],
            [
                'name' => 'Invoice Items Table',
                'type' => 'table',
                'description' => 'Table of invoice items',
                'preview_html' => '<table border="1"><tr><th>Description</th><th>Qty</th><th>Price</th><th>Total</th></tr><tr><td>Sample Item</td><td>1</td><td>$100</td><td>$100</td></tr></table>',
                'template_html' => '<table class="invoice-table {{config.show_borders ? \'bordered\' : \'\'}}"><thead><tr class="{{config.header_style}}"><th>Description</th><th>Quantity</th><th>Price</th><th>Total</th></tr></thead><tbody>{{#each items}}<tr><td>{{description}}</td><td>{{quantity}}</td><td>{{price}}</td><td>{{total}}</td></tr>{{/each}}</tbody></table>',
                'config_schema' => json_encode([
                    'show_borders' => ['type' => 'checkbox', 'default' => true],
                    'header_style' => ['type' => 'select', 'options' => ['normal', 'bold', 'colored'], 'default' => 'bold']
                ]),
                'default_config' => json_encode(['show_borders' => true, 'header_style' => 'bold'])
            ],
            [
                'name' => 'Bank Information',
                'type' => 'bankinfo',
                'description' => 'Payment and bank details',
                'preview_html' => '<div><strong>Bank Details:</strong><br>Bank: Sample Bank<br>Account: **********</div>',
                'template_html' => '<div class="bank-info {{config.layout}}"><strong>Bank Details:</strong><br>Bank: {{bank.name}}<br>Account: {{bank.account_number}}</div>',
                'config_schema' => json_encode([
                    'layout' => ['type' => 'select', 'options' => ['simple', 'boxed', 'table'], 'default' => 'simple']
                ]),
                'default_config' => json_encode(['layout' => 'simple'])
            ]
        ];
        
        foreach ($basicComponents as $componentData) {
            App\Models\TemplateComponent::create($componentData);
            echo "   ✅ Created: {$componentData['name']}\n";
        }
    }
    
    echo "\n=== Fix Complete ===\n";
    echo "✅ All template components are now valid\n";
    echo "✅ Preview should work without errors\n";
    
    // Final verification
    $finalCount = App\Models\TemplateComponent::count();
    echo "\nFinal component count: {$finalCount}\n";
    
    $invalidComponents = App\Models\TemplateComponent::whereNull('template_html')->orWhere('template_html', '')->count();
    if ($invalidComponents === 0) {
        echo "✅ All components have valid template_html\n";
    } else {
        echo "❌ {$invalidComponents} components still have invalid template_html\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
