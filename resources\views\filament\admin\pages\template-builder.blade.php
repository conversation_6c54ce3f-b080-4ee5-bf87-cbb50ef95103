<x-filament-panels::page>
    <div
        x-data="templateBuilder()"
        x-init="init()"
        class="template-builder-container"
    >
        <!-- Builder Controls -->
        <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center justify-between">
                <!-- Page Size Controls -->
                <div class="flex items-center space-x-4">
                    <label class="text-sm font-medium text-gray-700">Page Size:</label>
                    <div class="flex space-x-2">
                        <button
                            @click="changePageSize('legal')"
                            :class="pageSize === 'legal' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700'"
                            class="px-3 py-1 rounded text-sm font-medium transition-colors"
                        >
                            Legal (8.5" x 14")
                        </button>
                        <button
                            @click="changePageSize('f4')"
                            :class="pageSize === 'f4' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700'"
                            class="px-3 py-1 rounded text-sm font-medium transition-colors"
                        >
                            F4/Folio (210mm x 330mm)
                        </button>
                    </div>
                </div>

                <!-- Preview Mode Controls -->
                <div class="flex items-center space-x-4">
                    <label class="text-sm font-medium text-gray-700">Preview:</label>
                    <div class="flex space-x-2">
                        <button
                            @click="changePreviewMode('desktop')"
                            :class="previewMode === 'desktop' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700'"
                            class="px-3 py-1 rounded text-sm font-medium transition-colors"
                        >
                            Desktop
                        </button>
                        <button
                            @click="changePreviewMode('print')"
                            :class="previewMode === 'print' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700'"
                            class="px-3 py-1 rounded text-sm font-medium transition-colors"
                        >
                            Print Preview
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Builder Interface -->
        <div class="flex gap-6 min-h-[600px]">
            <!-- Component Palette Sidebar -->
            <div class="w-80 flex-shrink-0">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Components</h3>
                        <p class="text-sm text-gray-600">Drag components to build your template</p>
                    </div>

                    <div class="p-4 space-y-4 overflow-y-auto" style="max-height: calc(100vh - 300px);">
                        <template x-for="category in availableComponents" :key="category.type">
                            <div class="component-category">
                                <h4
                                    class="text-sm font-medium text-gray-700 mb-2 cursor-pointer flex items-center justify-between"
                                    @click="toggleCategory(category.type)"
                                >
                                    <span x-text="category.label"></span>
                                    <svg class="w-4 h-4 transform transition-transform" :class="{'rotate-180': expandedCategories.includes(category.type)}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </h4>

                                <div x-show="expandedCategories.includes(category.type)" x-transition class="space-y-2">
                                    <template x-for="component in category.components" :key="component.id">
                                        <div
                                            class="component-item p-3 border border-gray-200 rounded cursor-move hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 select-none"
                                            draggable="true"
                                            @dragstart="startDrag($event, component)"
                                            @dragend="endDrag($event)"
                                        >
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                                </svg>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900" x-text="component.name"></div>
                                                    <div class="text-xs text-gray-600" x-text="component.description"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>

            <!-- Template Canvas -->
            <div class="flex-1 min-w-0">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Template Canvas</h3>
                                <p class="text-sm text-gray-600">Design your invoice template</p>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex items-center space-x-3">
                                <button
                                    @click="$wire.resetTemplate()"
                                    class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                    title="Reset Template"
                                >
                                    <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Reset
                                </button>

                                <button
                                    @click="$wire.previewTemplate()"
                                    class="px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                    title="Preview Template"
                                >
                                    <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    Preview
                                </button>

                                <button
                                    @click="$wire.saveTemplate()"
                                    class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                    title="Save Template"
                                >
                                    <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                                    </svg>
                                    Save
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-canvas p-4" :class="getCanvasClass()">
                        <!-- Header Section -->
                        <div
                            class="template-section border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4 min-h-32 transition-all duration-200"
                            :class="dragOverSection === 'header' ? 'border-blue-500 bg-blue-50' : ''"
                            data-section="header"
                            @drop="dropComponent($event, 'header')"
                            @dragover.prevent="dragOverSection = 'header'"
                            @dragenter.prevent="dragOverSection = 'header'"
                            @dragleave="dragOverSection = null"
                        >
                            <div class="section-label text-xs font-medium text-gray-500 mb-3 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                HEADER SECTION
                            </div>
                            <div class="section-components space-y-2">
                                <template x-for="component in selectedComponents.header" :key="component.id">
                                    <div class="selected-component bg-blue-50 border border-blue-200 rounded-lg p-3 relative group hover:shadow-sm transition-all duration-200">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center flex-1">
                                                <svg class="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <div class="text-sm font-medium text-blue-900 truncate" x-text="component.name"></div>
                                                    <div class="text-xs text-blue-600 mt-1" x-show="component.config">
                                                        <span x-show="component.config.alignment" x-text="'Align: ' + component.config.alignment"></span>
                                                        <span x-show="component.config.font_size" x-text="' • Size: ' + component.config.font_size + 'pt'"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-1 ml-2">
                                                <button
                                                    @click="editComponent(component)"
                                                    class="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors"
                                                    title="Edit Component"
                                                >
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                    </svg>
                                                </button>
                                                <button
                                                    @click="removeComponent(component.id, 'header')"
                                                    class="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded transition-colors"
                                                    title="Remove Component"
                                                >
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <div x-show="selectedComponents.header.length === 0" class="text-center text-gray-400 py-8 border-2 border-dashed border-gray-200 rounded-lg">
                                    <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <p class="text-sm">Drop header components here</p>
                                </div>
                            </div>
                        </div>

                        <!-- Body Section -->
                        <div
                            class="template-section border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4 min-h-48 transition-all duration-200"
                            :class="dragOverSection === 'body' ? 'border-green-500 bg-green-50' : ''"
                            data-section="body"
                            @drop="dropComponent($event, 'body')"
                            @dragover.prevent="dragOverSection = 'body'"
                            @dragenter.prevent="dragOverSection = 'body'"
                            @dragleave="dragOverSection = null"
                        >
                            <div class="section-label text-xs font-medium text-gray-500 mb-3 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                BODY SECTION
                            </div>
                            <div class="section-components space-y-2">
                                <template x-for="component in selectedComponents.body" :key="component.id">
                                    <div class="selected-component bg-green-50 border border-green-200 rounded-lg p-3 relative group hover:shadow-sm transition-all duration-200">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <span class="text-sm font-medium text-green-900" x-text="component.name"></span>
                                            </div>
                                            <div class="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-1">
                                                <button
                                                    @click="editComponent(component)"
                                                    class="p-1 text-green-600 hover:text-green-800 hover:bg-green-100 rounded transition-colors"
                                                    title="Edit Component"
                                                >
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                    </svg>
                                                </button>
                                                <button
                                                    @click="removeComponent(component.id, 'body')"
                                                    class="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded transition-colors"
                                                    title="Remove Component"
                                                >
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <div x-show="selectedComponents.body.length === 0" class="text-center text-gray-400 py-12 border-2 border-dashed border-gray-200 rounded-lg">
                                    <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <p class="text-sm">Drop body components here</p>
                                    <p class="text-xs text-gray-300 mt-1">Tables, item lists, descriptions</p>
                                </div>
                            </div>
                        </div>

                        <!-- Footer Section -->
                        <div
                            class="template-section border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-32 transition-all duration-200"
                            :class="dragOverSection === 'footer' ? 'border-purple-500 bg-purple-50' : ''"
                            data-section="footer"
                            @drop="dropComponent($event, 'footer')"
                            @dragover.prevent="dragOverSection = 'footer'"
                            @dragenter.prevent="dragOverSection = 'footer'"
                            @dragleave="dragOverSection = null"
                        >
                            <div class="section-label text-xs font-medium text-gray-500 mb-3 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                </svg>
                                FOOTER SECTION
                            </div>
                            <div class="section-components space-y-2">
                                <template x-for="component in selectedComponents.footer" :key="component.id">
                                    <div class="selected-component bg-purple-50 border border-purple-200 rounded-lg p-3 relative group hover:shadow-sm transition-all duration-200">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <span class="text-sm font-medium text-purple-900" x-text="component.name"></span>
                                            </div>
                                            <div class="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-1">
                                                <button
                                                    @click="editComponent(component)"
                                                    class="p-1 text-purple-600 hover:text-purple-800 hover:bg-purple-100 rounded transition-colors"
                                                    title="Edit Component"
                                                >
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                    </svg>
                                                </button>
                                                <button
                                                    @click="removeComponent(component.id, 'footer')"
                                                    class="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded transition-colors"
                                                    title="Remove Component"
                                                >
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <div x-show="selectedComponents.footer.length === 0" class="text-center text-gray-400 py-8 border-2 border-dashed border-gray-200 rounded-lg">
                                    <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <p class="text-sm">Drop footer components here</p>
                                    <p class="text-xs text-gray-300 mt-1">Bank info, signatures, notes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Properties Panel -->
            <div class="w-80 flex-shrink-0">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Properties</h3>
                        <p class="text-sm text-gray-600">Configure selected component</p>
                    </div>

                    <div class="p-4 overflow-y-auto" style="max-height: calc(100vh - 300px);">
                        <div x-show="selectedComponent" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Component Name</label>
                                <input
                                    type="text"
                                    :value="getSelectedComponentName()"
                                    @input="selectedComponent.name = $event.target.value"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter component name"
                                >
                            </div>

                            <!-- Component Type Display -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Component Type</label>
                                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm text-gray-600">
                                    <span x-text="getSelectedComponentType()"></span>
                                </div>
                            </div>

                            <!-- Dynamic properties based on component type -->
                            <div x-show="isComponentType('kop')" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Text Alignment</label>
                                    <select
                                        :value="getConfigValue('alignment', 'center')"
                                        @change="setConfigValue('alignment', $event.target.value)"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="left">Left</option>
                                        <option value="center">Center</option>
                                        <option value="right">Right</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                                    <select
                                        :value="getConfigValue('font_size', '14')"
                                        @change="setConfigValue('font_size', $event.target.value)"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="12">12pt (Normal)</option>
                                        <option value="14">14pt (Large)</option>
                                        <option value="16">16pt (Extra Large)</option>
                                        <option value="18">18pt (Title)</option>
                                    </select>
                                </div>
                            </div>

                            <div x-show="isComponentType('table')" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Show Borders</label>
                                    <label class="flex items-center">
                                        <input
                                            type="checkbox"
                                            :checked="getConfigValue('show_borders', true)"
                                            @change="setConfigValue('show_borders', $event.target.checked)"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        >
                                        <span class="ml-2 text-sm text-gray-600">Display table borders</span>
                                    </label>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Header Style</label>
                                    <select
                                        :value="getConfigValue('header_style', 'bold')"
                                        @change="setConfigValue('header_style', $event.target.value)"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="normal">Normal</option>
                                        <option value="bold">Bold</option>
                                        <option value="colored">Colored Background</option>
                                    </select>
                                </div>
                            </div>

                            <div x-show="isComponentType('bankinfo')" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Layout Style</label>
                                    <select
                                        :value="getConfigValue('layout', 'simple')"
                                        @change="setConfigValue('layout', $event.target.value)"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="simple">Simple List</option>
                                        <option value="boxed">Boxed Style</option>
                                        <option value="table">Table Format</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Common properties for all components -->
                            <div class="border-t pt-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-3">Spacing & Style</h4>

                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Margin Top</label>
                                        <select
                                            :value="getConfigValue('margin_top', '10')"
                                            @change="setConfigValue('margin_top', $event.target.value)"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="0">None</option>
                                            <option value="10">Small (10px)</option>
                                            <option value="20">Medium (20px)</option>
                                            <option value="30">Large (30px)</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Margin Bottom</label>
                                        <select
                                            :value="getConfigValue('margin_bottom', '10')"
                                            @change="setConfigValue('margin_bottom', $event.target.value)"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="0">None</option>
                                            <option value="10">Small (10px)</option>
                                            <option value="20">Medium (20px)</option>
                                            <option value="30">Large (30px)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="flex space-x-2 pt-4">
                                <button
                                    @click="updateSelectedComponent()"
                                    class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                                >
                                    Update
                                </button>
                                <button
                                    @click="selectedComponent = null"
                                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors"
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>

                        <div x-show="!selectedComponent" class="text-center text-gray-400 py-12">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                            </svg>
                            <h3 class="text-sm font-medium text-gray-700 mb-1">No Component Selected</h3>
                            <p class="text-xs text-gray-500">Click on a component in the canvas to edit its properties</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function templateBuilder() {
            return {
                // Data from backend
                template: @js($this->template?->toArray()),
                templateData: @js($this->templateData),
                availableComponents: @js($this->availableComponents),
                selectedComponents: @js($this->selectedComponents),
                previewMode: @js($this->previewMode),
                pageSize: @js($this->pageSize),

                // UI state
                expandedCategories: ['kop', 'billto', 'table'],
                selectedComponent: null,
                draggedComponent: null,
                dragOverSection: null,

                init() {
                    console.log('Template Builder initialized');
                    console.log('Available components:', this.availableComponents);
                    console.log('Selected components:', this.selectedComponents);
                    console.log('Livewire component:', this.$wire);

                    // Test if Livewire is working
                    if (this.$wire) {
                        console.log('✅ Livewire is available');
                    } else {
                        console.error('❌ Livewire is not available');
                    }
                },

                // Safe access helper methods
                getSelectedComponentName() {
                    return this.selectedComponent && this.selectedComponent.name ? this.selectedComponent.name : '';
                },

                getSelectedComponentType() {
                    return this.selectedComponent && this.selectedComponent.type ? this.selectedComponent.type : 'Unknown';
                },

                getConfigValue(key, defaultValue = '') {
                    if (!this.selectedComponent || !this.selectedComponent.config) {
                        return defaultValue;
                    }
                    return this.selectedComponent.config[key] !== undefined ? this.selectedComponent.config[key] : defaultValue;
                },

                setConfigValue(key, value) {
                    if (!this.selectedComponent) return;
                    if (!this.selectedComponent.config) {
                        this.selectedComponent.config = {};
                    }
                    this.selectedComponent.config[key] = value;
                },

                isComponentType(type) {
                    return this.selectedComponent && this.selectedComponent.type === type;
                },

                toggleCategory(type) {
                    if (this.expandedCategories.includes(type)) {
                        this.expandedCategories = this.expandedCategories.filter(cat => cat !== type);
                    } else {
                        this.expandedCategories.push(type);
                    }
                },

                startDrag(event, component) {
                    this.draggedComponent = component;
                    event.dataTransfer.effectAllowed = 'copy';
                    event.dataTransfer.setData('text/plain', ''); // For Firefox compatibility

                    // Add visual feedback
                    event.target.style.opacity = '0.5';
                },

                endDrag(event) {
                    // Reset visual feedback
                    event.target.style.opacity = '1';
                    this.dragOverSection = null;
                },

                dropComponent(event, section) {
                    event.preventDefault();
                    this.dragOverSection = null;

                    console.log('Drop event triggered', { draggedComponent: this.draggedComponent, section: section });

                    if (this.draggedComponent) {
                        console.log('Adding component to section:', this.draggedComponent.id, section);

                        // Call Livewire method
                        this.$wire.addComponent(this.draggedComponent.id, section).then(() => {
                            console.log('Component added successfully');
                            // Add visual feedback
                            this.showSuccessFeedback(section);
                        }).catch((error) => {
                            console.error('Error adding component:', error);
                        });

                        this.draggedComponent = null;
                    } else {
                        console.log('No dragged component found');
                    }
                },

                showSuccessFeedback(section) {
                    // Add success class temporarily
                    const sectionElement = document.querySelector(`[data-section="${section}"]`);
                    if (sectionElement) {
                        sectionElement.classList.add('success-feedback');
                        setTimeout(() => {
                            sectionElement.classList.remove('success-feedback');
                        }, 600);
                    }
                },

                removeComponent(componentId, section) {
                    this.$wire.removeComponent(componentId, section);
                },

                editComponent(component) {
                    // Deep clone the component to avoid reference issues
                    this.selectedComponent = JSON.parse(JSON.stringify(component));

                    // Ensure config object exists with defaults
                    if (!this.selectedComponent.config) {
                        this.selectedComponent.config = {};
                    }

                    // Set default values based on component type
                    const defaults = {
                        kop: {
                            alignment: 'center',
                            font_size: '14',
                            margin_top: '10',
                            margin_bottom: '10'
                        },
                        table: {
                            show_borders: true,
                            header_style: 'bold',
                            margin_top: '10',
                            margin_bottom: '10'
                        },
                        bankinfo: {
                            layout: 'simple',
                            margin_top: '10',
                            margin_bottom: '10'
                        }
                    };

                    const typeDefaults = defaults[this.selectedComponent.type] || {};

                    // Merge defaults with existing config
                    this.selectedComponent.config = {
                        ...typeDefaults,
                        ...this.selectedComponent.config
                    };

                    console.log('Editing component:', this.selectedComponent);
                },

                updateSelectedComponent() {
                    if (this.selectedComponent) {
                        // Find which section this component belongs to
                        let section = null;
                        for (const [sectionName, components] of Object.entries(this.selectedComponents)) {
                            if (components.find(comp => comp.id === this.selectedComponent.id)) {
                                section = sectionName;
                                break;
                            }
                        }

                        if (section) {
                            this.$wire.updateComponentConfig(
                                this.selectedComponent.id,
                                section,
                                this.selectedComponent.config || {}
                            );
                        }
                    }
                },

                changePageSize(size) {
                    this.pageSize = size;
                    this.$wire.changePageSize(size);
                },

                changePreviewMode(mode) {
                    this.previewMode = mode;
                    this.$wire.changePreviewMode(mode);
                },

                getCanvasClass() {
                    let classes = 'template-canvas-content ';

                    if (this.pageSize === 'legal') {
                        classes += 'aspect-[8.5/14] max-w-md mx-auto ';
                    } else {
                        classes += 'aspect-[210/330] max-w-sm mx-auto ';
                    }

                    if (this.previewMode === 'print') {
                        classes += 'bg-white shadow-lg ';
                    } else {
                        classes += 'bg-gray-50 ';
                    }

                    return classes;
                }
            }
        }
    </script>
    @endpush

    @push('styles')
    <style>
        .template-builder-container {
            min-height: 80vh;
        }

        /* Drag & Drop Styles */
        .component-item {
            transition: all 0.2s ease;
            user-select: none;
        }

        .component-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px -1px rgba(0, 0, 0, 0.15);
        }

        .component-item:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }

        /* Template Section Styles */
        .template-section {
            transition: all 0.3s ease;
            position: relative;
        }

        .template-section.drag-over {
            border-color: #3b82f6 !important;
            background-color: #eff6ff !important;
            transform: scale(1.02);
        }

        /* Selected Component Styles */
        .selected-component {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .selected-component:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px -1px rgba(0, 0, 0, 0.1);
        }

        /* Canvas Styles */
        .template-canvas-content {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        /* Drag Feedback */
        .dragging {
            opacity: 0.5 !important;
            transform: rotate(5deg);
        }

        /* Drop Zone Styles */
        .drop-zone-active {
            border-color: #10b981 !important;
            background-color: #ecfdf5 !important;
            border-style: solid !important;
        }

        .drop-zone-active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 40%, rgba(16, 185, 129, 0.1) 50%, transparent 60%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Responsive adjustments */
        @media (max-width: 1280px) {
            .template-builder-container .flex {
                flex-direction: column;
            }

            .template-builder-container .w-80 {
                width: 100%;
                max-width: none;
            }
        }

        /* Scrollbar styling */
        .overflow-y-auto::-webkit-scrollbar {
            width: 6px;
        }

        .overflow-y-auto::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .overflow-y-auto::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .overflow-y-auto::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Focus styles for accessibility */
        .component-item:focus,
        .selected-component:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* Loading state */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Success feedback */
        .success-feedback {
            border-color: #10b981 !important;
            background-color: #ecfdf5 !important;
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
    @endpush
</x-filament-panels::page>
