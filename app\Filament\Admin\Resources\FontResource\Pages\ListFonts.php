<?php

namespace App\Filament\Admin\Resources\FontResource\Pages;

use App\Filament\Admin\Resources\FontResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFonts extends ListRecords
{
    protected static string $resource = FontResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Add Font'),
        ];
    }

    public function getTitle(): string
    {
        return 'Font Management';
    }
}
