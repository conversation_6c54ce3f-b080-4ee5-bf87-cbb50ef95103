<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drag & Drop Debug Helper</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-container { max-width: 800px; margin: 0 auto; }
        .component-item { 
            padding: 10px; 
            margin: 5px; 
            background: #f0f0f0; 
            border: 1px solid #ccc; 
            cursor: move;
            user-select: none;
        }
        .drop-zone { 
            min-height: 100px; 
            border: 2px dashed #ccc; 
            padding: 20px; 
            margin: 10px 0;
            background: #fafafa;
        }
        .drop-zone.drag-over { 
            border-color: #007bff; 
            background: #e7f3ff; 
        }
        .log { 
            background: #000; 
            color: #0f0; 
            padding: 10px; 
            height: 200px; 
            overflow-y: auto; 
            font-family: monospace;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="debug-container" x-data="debugDragDrop()">
        <h1>Drag & Drop Debug Helper</h1>
        <p>Use this to test drag & drop functionality independently</p>
        
        <h3>Components (Drag these):</h3>
        <div class="components">
            <div 
                class="component-item" 
                draggable="true"
                @dragstart="startDrag($event, {id: 1, name: 'Test Component 1'})"
                @dragend="endDrag($event)"
            >
                📄 Test Component 1
            </div>
            <div 
                class="component-item" 
                draggable="true"
                @dragstart="startDrag($event, {id: 2, name: 'Test Component 2'})"
                @dragend="endDrag($event)"
            >
                📊 Test Component 2
            </div>
            <div 
                class="component-item" 
                draggable="true"
                @dragstart="startDrag($event, {id: 3, name: 'Test Component 3'})"
                @dragend="endDrag($event)"
            >
                🏦 Test Component 3
            </div>
        </div>
        
        <h3>Drop Zones:</h3>
        <div 
            class="drop-zone"
            :class="dragOverSection === 'header' ? 'drag-over' : ''"
            @drop="dropComponent($event, 'header')"
            @dragover.prevent="dragOverSection = 'header'"
            @dragenter.prevent="dragOverSection = 'header'"
            @dragleave="dragOverSection = null"
        >
            <strong>Header Section</strong>
            <div x-show="droppedComponents.header.length > 0">
                <template x-for="comp in droppedComponents.header" :key="comp.id">
                    <div x-text="'✓ ' + comp.name" style="color: green; margin: 5px 0;"></div>
                </template>
            </div>
            <div x-show="droppedComponents.header.length === 0" style="color: #999;">
                Drop components here
            </div>
        </div>
        
        <div 
            class="drop-zone"
            :class="dragOverSection === 'body' ? 'drag-over' : ''"
            @drop="dropComponent($event, 'body')"
            @dragover.prevent="dragOverSection = 'body'"
            @dragenter.prevent="dragOverSection = 'body'"
            @dragleave="dragOverSection = null"
        >
            <strong>Body Section</strong>
            <div x-show="droppedComponents.body.length > 0">
                <template x-for="comp in droppedComponents.body" :key="comp.id">
                    <div x-text="'✓ ' + comp.name" style="color: green; margin: 5px 0;"></div>
                </template>
            </div>
            <div x-show="droppedComponents.body.length === 0" style="color: #999;">
                Drop components here
            </div>
        </div>
        
        <div 
            class="drop-zone"
            :class="dragOverSection === 'footer' ? 'drag-over' : ''"
            @drop="dropComponent($event, 'footer')"
            @dragover.prevent="dragOverSection = 'footer'"
            @dragenter.prevent="dragOverSection = 'footer'"
            @dragleave="dragOverSection = null"
        >
            <strong>Footer Section</strong>
            <div x-show="droppedComponents.footer.length > 0">
                <template x-for="comp in droppedComponents.footer" :key="comp.id">
                    <div x-text="'✓ ' + comp.name" style="color: green; margin: 5px 0;"></div>
                </template>
            </div>
            <div x-show="droppedComponents.footer.length === 0" style="color: #999;">
                Drop components here
            </div>
        </div>
        
        <h3>Debug Log:</h3>
        <div class="log" x-ref="log">
            <template x-for="entry in debugLog" :key="entry.id">
                <div x-text="entry.timestamp + ': ' + entry.message"></div>
            </template>
        </div>
        
        <button @click="clearLog()" style="margin-top: 10px; padding: 5px 10px;">Clear Log</button>
    </div>

    <script>
        function debugDragDrop() {
            return {
                draggedComponent: null,
                dragOverSection: null,
                droppedComponents: {
                    header: [],
                    body: [],
                    footer: []
                },
                debugLog: [],
                
                init() {
                    this.log('Debug helper initialized');
                },
                
                log(message) {
                    this.debugLog.push({
                        id: Date.now(),
                        timestamp: new Date().toLocaleTimeString(),
                        message: message
                    });
                    
                    // Auto scroll to bottom
                    this.$nextTick(() => {
                        this.$refs.log.scrollTop = this.$refs.log.scrollHeight;
                    });
                },
                
                startDrag(event, component) {
                    this.draggedComponent = component;
                    event.dataTransfer.effectAllowed = 'copy';
                    event.dataTransfer.setData('text/plain', '');
                    event.target.style.opacity = '0.5';
                    
                    this.log(`Started dragging: ${component.name} (ID: ${component.id})`);
                },
                
                endDrag(event) {
                    event.target.style.opacity = '1';
                    this.dragOverSection = null;
                    this.log('Drag ended');
                },
                
                dropComponent(event, section) {
                    event.preventDefault();
                    this.dragOverSection = null;
                    
                    this.log(`Drop event triggered on section: ${section}`);
                    
                    if (this.draggedComponent) {
                        this.log(`Adding component ${this.draggedComponent.name} to ${section}`);
                        
                        // Add to dropped components
                        this.droppedComponents[section].push({
                            ...this.draggedComponent,
                            droppedAt: new Date().toLocaleTimeString()
                        });
                        
                        this.log(`✅ Component successfully added to ${section}`);
                        this.draggedComponent = null;
                    } else {
                        this.log('❌ No dragged component found');
                    }
                },
                
                clearLog() {
                    this.debugLog = [];
                    this.log('Log cleared');
                }
            }
        }
    </script>
</body>
</html>
