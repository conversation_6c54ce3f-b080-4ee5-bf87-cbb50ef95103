<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceTemplateCell extends Model
{
    use HasFactory;

    protected $fillable = [
        'section_id',
        'row_index',
        'col_index',
        'rowspan',
        'colspan',
        'content_type',
        'field_path',
        'static_content',
        'field_config',
        'text_align',
        'vertical_align',
        'font_size',
        'font_weight',
        'font_color',
        'background_color',
        'padding',
        'margin',
        'border',
        'image_width',
        'image_height',
        'image_fit',
        'is_active',
    ];

    protected $casts = [
        'field_config' => 'array',
        'padding' => 'array',
        'margin' => 'array',
        'border' => 'array',
        'is_active' => 'boolean',
        'row_index' => 'integer',
        'col_index' => 'integer',
        'rowspan' => 'integer',
        'colspan' => 'integer',
        'font_size' => 'integer',
    ];

    protected $attributes = [
        'rowspan' => 1,
        'colspan' => 1,
        'content_type' => 'text',
        'text_align' => 'left',
        'vertical_align' => 'top',
        'font_size' => 12,
        'font_weight' => 'normal',
        'font_color' => '#000000',
        'image_fit' => 'contain',
        'is_active' => true,
    ];

    // Relationships
    public function section(): BelongsTo
    {
        return $this->belongsTo(InvoiceTemplateSection::class, 'section_id');
    }

    // Helper methods
    public function getContent(array $data = []): string
    {
        switch ($this->content_type) {
            case 'field':
                return $this->getFieldValue($data);
            case 'image':
                return $this->getImageValue($data);
            case 'text':
            default:
                return $this->static_content ?? '';
        }
    }

    private function getFieldValue(array $data): string
    {
        if (!$this->field_path) {
            return $this->static_content ?? '';
        }

        $value = data_get($data, $this->field_path, '');
        
        // Apply field configuration
        $config = $this->field_config ?? [];
        $prefix = $config['prefix'] ?? '';
        $suffix = $config['suffix'] ?? '';
        $format = $config['format'] ?? null;
        
        // Format based on field type
        if ($format) {
            switch ($format) {
                case 'currency':
                    $value = 'Rp ' . number_format((float)$value, 0, ',', '.');
                    break;
                case 'date':
                    $value = date('d/m/Y', strtotime($value));
                    break;
                case 'uppercase':
                    $value = strtoupper($value);
                    break;
                case 'lowercase':
                    $value = strtolower($value);
                    break;
            }
        }
        
        return $prefix . $value . $suffix;
    }

    private function getImageValue(array $data): string
    {
        $imageUrl = '';
        
        if ($this->field_path) {
            $imageUrl = data_get($data, $this->field_path, '');
        } elseif ($this->static_content) {
            $imageUrl = $this->static_content;
        }
        
        if (empty($imageUrl)) {
            return '';
        }
        
        // Process image URL
        if (!str_starts_with($imageUrl, 'http')) {
            $imageUrl = url('storage/' . ltrim($imageUrl, '/'));
        }
        
        $width = $this->image_width ?? 'auto';
        $height = $this->image_height ?? 'auto';
        $fit = $this->image_fit ?? 'contain';
        $alt = $this->field_config['alt_text'] ?? 'Image';
        
        return sprintf(
            '<img src="%s" alt="%s" style="width: %s; height: %s; object-fit: %s;">',
            $imageUrl, $alt, $width, $height, $fit
        );
    }

    public function buildCellStyles(): array
    {
        $styles = [];
        
        // Text alignment
        if ($this->text_align && $this->text_align !== 'left') {
            $styles[] = 'text-align: ' . $this->text_align;
        }
        
        // Vertical alignment
        if ($this->vertical_align && $this->vertical_align !== 'top') {
            $styles[] = 'vertical-align: ' . $this->vertical_align;
        }
        
        // Font styling
        if ($this->font_size && $this->font_size !== 12) {
            $styles[] = 'font-size: ' . $this->font_size . 'pt';
        }
        
        if ($this->font_weight && $this->font_weight !== 'normal') {
            $styles[] = 'font-weight: ' . $this->font_weight;
        }
        
        if ($this->font_color && $this->font_color !== '#000000') {
            $styles[] = 'color: ' . $this->font_color;
        }
        
        // Background
        if ($this->background_color) {
            $styles[] = 'background-color: ' . $this->background_color;
        }
        
        // Padding
        if ($this->padding) {
            $p = $this->padding;
            $styles[] = sprintf('padding: %dpx %dpx %dpx %dpx',
                $p['top'] ?? 0, $p['right'] ?? 0, $p['bottom'] ?? 0, $p['left'] ?? 0);
        }
        
        // Margin
        if ($this->margin) {
            $m = $this->margin;
            $styles[] = sprintf('margin: %dpx %dpx %dpx %dpx',
                $m['top'] ?? 0, $m['right'] ?? 0, $m['bottom'] ?? 0, $m['left'] ?? 0);
        }
        
        // Border
        if ($this->border) {
            $b = $this->border;
            if (isset($b['width']) && $b['width'] > 0) {
                $styles[] = sprintf('border: %dpx %s %s',
                    $b['width'], $b['style'] ?? 'solid', $b['color'] ?? '#000000');
            }
        }
        
        return $styles;
    }

    public function renderHtml(array $data = []): string
    {
        $html = '<td';
        
        // Add rowspan and colspan
        if ($this->rowspan > 1) {
            $html .= ' rowspan="' . $this->rowspan . '"';
        }
        
        if ($this->colspan > 1) {
            $html .= ' colspan="' . $this->colspan . '"';
        }
        
        // Add styling
        $styles = $this->buildCellStyles();
        if (!empty($styles)) {
            $html .= ' style="' . implode('; ', $styles) . '"';
        }
        
        // Add CSS classes
        $classes = ['invoice-cell', 'content-' . $this->content_type];
        $html .= ' class="' . implode(' ', $classes) . '"';
        
        $html .= '>';
        
        // Add content
        $content = $this->getContent($data);
        $html .= $content;
        
        $html .= '</td>';
        
        return $html;
    }

    public function updateContent(string $type, $value, array $config = []): void
    {
        $updateData = [
            'content_type' => $type,
        ];
        
        switch ($type) {
            case 'field':
                $updateData['field_path'] = $value;
                $updateData['static_content'] = null;
                break;
            case 'text':
                $updateData['static_content'] = $value;
                $updateData['field_path'] = null;
                break;
            case 'image':
                if (str_starts_with($value, 'http') || str_starts_with($value, '/')) {
                    $updateData['static_content'] = $value;
                    $updateData['field_path'] = null;
                } else {
                    $updateData['field_path'] = $value;
                    $updateData['static_content'] = null;
                }
                break;
        }
        
        if (!empty($config)) {
            $updateData['field_config'] = array_merge($this->field_config ?? [], $config);
        }
        
        $this->update($updateData);
    }
}
