<?php

namespace App\Filament\Admin\Resources\FontResource\Pages;

use App\Filament\Admin\Resources\FontResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFont extends ViewRecord
{
    protected static string $resource = FontResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function getTitle(): string
    {
        return 'View Font: ' . $this->record->name;
    }
}
