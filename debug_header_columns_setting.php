<?php

/**
 * Debug Header Columns Setting Issue
 * Run via: php artisan tinker
 */

echo "=== Debug Header Columns Setting ===\n";

try {
    // 1. Create simple test template
    echo "1. Creating simple test template...\n";
    
    $testTemplate = App\Models\TemplateLayout::updateOrCreate(
        ['name' => 'Debug Columns Test'],
        [
            'description' => 'Simple template for debugging column settings',
            'company_id' => null,
            'version' => 'v2',
            'created_by' => 1,
        ]
    );
    
    // Clear existing sections
    App\Models\LayoutSection::where('template_layout_id', $testTemplate->id)->delete();
    
    // Create ONE simple header section
    $headerSection = App\Models\LayoutSection::create([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'header',
        'section_name' => 'Debug Header',
        'column_count' => 1,
        'column_layout' => 'equal',
        'column_widths' => null,
        'order' => 0,
        'is_active' => true,
        'min_height' => 100,
        'background_color' => null,
    ]);
    
    echo "   ✅ Template: {$testTemplate->name} (ID: {$testTemplate->id})\n";
    echo "   ✅ Header Section: {$headerSection->id}\n";
    echo "   📊 Initial column_count: {$headerSection->column_count}\n";
    
    // 2. Test direct database update
    echo "\n2. Testing direct database update...\n";
    
    echo "   🔧 Updating column_count from 1 to 3...\n";
    $updateResult = $headerSection->update([
        'column_count' => 3,
        'column_layout' => 'equal',
        'column_widths' => null
    ]);
    
    echo "   📊 Update result: " . ($updateResult ? 'SUCCESS' : 'FAILED') . "\n";
    
    $headerSection->refresh();
    echo "   📊 After update column_count: {$headerSection->column_count}\n";
    echo "   📊 After update column_layout: {$headerSection->column_layout}\n";
    
    if ($headerSection->column_count === 3) {
        echo "   ✅ Direct database update WORKS\n";
    } else {
        echo "   ❌ Direct database update FAILED\n";
        
        // Check what's in database
        $dbRecord = DB::table('layout_sections')->where('id', $headerSection->id)->first();
        echo "   🔍 Database record:\n";
        echo "      - column_count: {$dbRecord->column_count}\n";
        echo "      - column_layout: {$dbRecord->column_layout}\n";
        echo "      - column_widths: {$dbRecord->column_widths}\n";
    }
    
    // 3. Test Livewire component properties
    echo "\n3. Testing Livewire component properties...\n";
    
    // Simulate what happens in Livewire
    $sectionSettings = [
        'section_name' => 'Debug Header Updated',
        'column_count' => 2,
        'column_layout' => 'custom',
        'column_widths' => [30, 70],
        'background_color' => '#f0f0f0',
        'min_height' => 120,
    ];
    
    echo "   📝 Simulating Livewire sectionSettings:\n";
    foreach ($sectionSettings as $key => $value) {
        $displayValue = is_array($value) ? json_encode($value) : ($value ?? 'null');
        echo "      - {$key}: {$displayValue}\n";
    }
    
    // Prepare update data like in Livewire
    $updateData = [
        'section_name' => $sectionSettings['section_name'] ?? $headerSection->section_name,
        'column_count' => (int) ($sectionSettings['column_count'] ?? $headerSection->column_count),
        'column_layout' => $sectionSettings['column_layout'] ?? $headerSection->column_layout,
        'column_widths' => $sectionSettings['column_widths'] ?? $headerSection->column_widths,
        'background_color' => $sectionSettings['background_color'] ?? $headerSection->background_color,
        'min_height' => (int) ($sectionSettings['min_height'] ?? $headerSection->min_height),
    ];
    
    echo "\n   📝 Prepared updateData:\n";
    foreach ($updateData as $key => $value) {
        $displayValue = is_array($value) ? json_encode($value) : ($value ?? 'null');
        echo "      - {$key}: {$displayValue}\n";
    }
    
    $livewireResult = $headerSection->update($updateData);
    echo "\n   📊 Livewire-style update result: " . ($livewireResult ? 'SUCCESS' : 'FAILED') . "\n";
    
    $headerSection->refresh();
    echo "   📊 After Livewire update:\n";
    echo "      - section_name: {$headerSection->section_name}\n";
    echo "      - column_count: {$headerSection->column_count}\n";
    echo "      - column_layout: {$headerSection->column_layout}\n";
    echo "      - column_widths: " . json_encode($headerSection->column_widths) . "\n";
    echo "      - background_color: " . ($headerSection->background_color ?? 'null') . "\n";
    echo "      - min_height: {$headerSection->min_height}\n";
    
    // 4. Test model fillable and casts
    echo "\n4. Testing model configuration...\n";
    
    $fillable = $headerSection->getFillable();
    echo "   📋 Fillable attributes:\n";
    foreach ($fillable as $attr) {
        echo "      - {$attr}\n";
    }
    
    $casts = $headerSection->getCasts();
    echo "\n   📋 Model casts:\n";
    foreach ($casts as $field => $cast) {
        echo "      - {$field}: {$cast}\n";
    }
    
    // Check if required fields are fillable
    $requiredFields = ['section_name', 'column_count', 'column_layout', 'column_widths', 'background_color', 'min_height'];
    $missingFields = array_diff($requiredFields, $fillable);
    
    if (empty($missingFields)) {
        echo "   ✅ All required fields are fillable\n";
    } else {
        echo "   ❌ Missing fillable fields: " . implode(', ', $missingFields) . "\n";
    }
    
    // 5. Test different column counts
    echo "\n5. Testing different column counts...\n";
    
    $testCounts = [1, 2, 3, 4];
    foreach ($testCounts as $count) {
        echo "   🔧 Testing column_count: {$count}\n";
        
        $result = $headerSection->update(['column_count' => $count]);
        $headerSection->refresh();
        
        $success = $headerSection->column_count == $count;
        echo "      📊 Result: " . ($success ? 'SUCCESS' : 'FAILED') . " (actual: {$headerSection->column_count})\n";
        
        if (!$success) {
            echo "      ❌ Column count {$count} failed to save\n";
            break;
        }
    }
    
    // 6. Test column widths
    echo "\n6. Testing column widths...\n";
    
    $testWidths = [
        [50, 50],
        [25, 75],
        [33, 33, 34],
        [25, 25, 25, 25]
    ];
    
    foreach ($testWidths as $index => $widths) {
        $columnCount = count($widths);
        echo "   🔧 Testing {$columnCount} columns with widths: " . json_encode($widths) . "\n";
        
        $result = $headerSection->update([
            'column_count' => $columnCount,
            'column_layout' => 'custom',
            'column_widths' => $widths
        ]);
        
        $headerSection->refresh();
        
        $countSuccess = $headerSection->column_count == $columnCount;
        $widthsSuccess = json_encode($headerSection->column_widths) === json_encode($widths);
        
        echo "      📊 Column count: " . ($countSuccess ? 'SUCCESS' : 'FAILED') . " (actual: {$headerSection->column_count})\n";
        echo "      📊 Column widths: " . ($widthsSuccess ? 'SUCCESS' : 'FAILED') . " (actual: " . json_encode($headerSection->column_widths) . ")\n";
        
        if (!$countSuccess || !$widthsSuccess) {
            echo "      ❌ Test failed for widths: " . json_encode($widths) . "\n";
        }
    }
    
    // 7. Check database constraints
    echo "\n7. Checking database constraints...\n";
    
    try {
        $tableInfo = DB::select("DESCRIBE layout_sections");
        echo "   📊 Table structure:\n";
        foreach ($tableInfo as $column) {
            if (in_array($column->Field, ['column_count', 'column_layout', 'column_widths'])) {
                echo "      - {$column->Field}: {$column->Type} (Null: {$column->Null}, Default: {$column->Default})\n";
            }
        }
    } catch (\Exception $e) {
        echo "   ❌ Could not get table info: {$e->getMessage()}\n";
    }
    
    // 8. Test Visual Builder URL
    echo "\n8. Visual Builder URL for testing...\n";
    
    $builderUrl = "/admin/visual-template-builder?template={$testTemplate->id}";
    echo "   🎨 URL: {$builderUrl}\n";
    
    // 9. Summary and next steps
    echo "\n=== Debug Summary ===\n";
    
    $finalSection = App\Models\LayoutSection::find($headerSection->id);
    echo "📊 Final section state:\n";
    echo "   - ID: {$finalSection->id}\n";
    echo "   - section_name: {$finalSection->section_name}\n";
    echo "   - column_count: {$finalSection->column_count}\n";
    echo "   - column_layout: {$finalSection->column_layout}\n";
    echo "   - column_widths: " . json_encode($finalSection->column_widths) . "\n";
    echo "   - background_color: " . ($finalSection->background_color ?? 'null') . "\n";
    echo "   - min_height: {$finalSection->min_height}\n";
    
    echo "\n🔍 If columns setting still doesn't work in Visual Builder:\n";
    echo "1. Check browser console for JavaScript errors\n";
    echo "2. Check Laravel logs: tail -f storage/logs/laravel.log\n";
    echo "3. Verify wire:model bindings in the form\n";
    echo "4. Test updateSectionSettings method directly\n";
    echo "5. Check if modal is properly submitting data\n";
    
    echo "\n📋 Manual test steps:\n";
    echo "1. Open: {$builderUrl}\n";
    echo "2. Click ⚙️ on header section\n";
    echo "3. Change column count from current to different number\n";
    echo "4. Click 'Save Section'\n";
    echo "5. Check if canvas updates\n";
    echo "6. Refresh page and verify changes persist\n";
    
    echo "\n✅ Database operations work correctly\n";
    echo "🔍 Issue likely in frontend/Livewire interaction\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
