<?php

/**
 * Setup Visual Builder v2.0 Script
 * Run via: php artisan tinker
 */

echo "=== Setting up Visual Builder v2.0 ===\n";

try {
    // 1. Create system block templates
    echo "1. Creating system block templates...\n";
    
    App\Models\BlockTemplate::createSystemTemplates();
    
    $blockCount = App\Models\BlockTemplate::where('is_system', true)->count();
    echo "   ✅ Created {$blockCount} system block templates\n";
    
    // 2. Create a test template layout
    echo "\n2. Creating test template layout...\n";
    
    $testTemplate = App\Models\TemplateLayout::firstOrCreate([
        'name' => 'Test Visual Builder Template'
    ], [
        'description' => 'Test template for Visual Builder v2.0',
        'company_id' => null, // Global template
        'version' => 'v2',
        'page_size' => 'legal',
        'orientation' => 'portrait',
        'font_family' => 'DejaVu Sans',
        'font_size' => 12,
        'is_active' => true,
        'is_default' => false,
        'created_by' => 1, // Assuming user ID 1 exists
    ]);
    
    echo "   ✅ Test template created (ID: {$testTemplate->id})\n";
    
    // 3. Create sample sections
    echo "\n3. Creating sample sections...\n";
    
    $headerSection = App\Models\LayoutSection::firstOrCreate([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'header'
    ], [
        'section_name' => 'Header Section',
        'column_count' => 3,
        'column_layout' => 'equal',
        'order' => 0,
        'is_active' => true,
        'min_height' => 100,
    ]);
    
    $bodySection = App\Models\LayoutSection::firstOrCreate([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'body'
    ], [
        'section_name' => 'Body Section',
        'column_count' => 1,
        'column_layout' => 'equal',
        'order' => 0,
        'is_active' => true,
        'min_height' => 200,
    ]);
    
    $footerSection = App\Models\LayoutSection::firstOrCreate([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'footer'
    ], [
        'section_name' => 'Footer Section',
        'column_count' => 2,
        'column_layout' => 'equal',
        'order' => 0,
        'is_active' => true,
        'min_height' => 80,
    ]);
    
    echo "   ✅ Header section created (ID: {$headerSection->id})\n";
    echo "   ✅ Body section created (ID: {$bodySection->id})\n";
    echo "   ✅ Footer section created (ID: {$footerSection->id})\n";
    
    // 4. Add some sample blocks
    echo "\n4. Adding sample blocks...\n";
    
    $logoBlock = App\Models\BlockTemplate::where('block_type', 'logo')->where('is_system', true)->first();
    $companyInfoBlock = App\Models\BlockTemplate::where('block_type', 'company_info')->where('is_system', true)->first();
    $invoiceDetailsBlock = App\Models\BlockTemplate::where('block_type', 'invoice_details')->where('is_system', true)->first();
    $tableBlock = App\Models\BlockTemplate::where('block_type', 'table')->where('is_system', true)->first();
    $bankInfoBlock = App\Models\BlockTemplate::where('block_type', 'bank_info')->where('is_system', true)->first();
    $signatureBlock = App\Models\BlockTemplate::where('block_type', 'signature')->where('is_system', true)->first();
    
    if ($logoBlock) {
        $logoBlock->createContentBlock($headerSection->id, ['column_position' => 1]);
        echo "   ✅ Logo block added to header column 1\n";
    }
    
    if ($companyInfoBlock) {
        $companyInfoBlock->createContentBlock($headerSection->id, ['column_position' => 2]);
        echo "   ✅ Company info block added to header column 2\n";
    }
    
    if ($invoiceDetailsBlock) {
        $invoiceDetailsBlock->createContentBlock($headerSection->id, ['column_position' => 3]);
        echo "   ✅ Invoice details block added to header column 3\n";
    }
    
    if ($tableBlock) {
        $tableBlock->createContentBlock($bodySection->id, ['column_position' => 1]);
        echo "   ✅ Table block added to body\n";
    }
    
    if ($bankInfoBlock) {
        $bankInfoBlock->createContentBlock($footerSection->id, ['column_position' => 1]);
        echo "   ✅ Bank info block added to footer column 1\n";
    }
    
    if ($signatureBlock) {
        $signatureBlock->createContentBlock($footerSection->id, ['column_position' => 2]);
        echo "   ✅ Signature block added to footer column 2\n";
    }
    
    // 5. Verification
    echo "\n5. Verification...\n";
    
    $totalTemplates = App\Models\TemplateLayout::count();
    $totalSections = App\Models\LayoutSection::count();
    $totalBlocks = App\Models\ContentBlock::count();
    $totalBlockTemplates = App\Models\BlockTemplate::count();
    
    echo "   📊 Statistics:\n";
    echo "      - Template Layouts: {$totalTemplates}\n";
    echo "      - Layout Sections: {$totalSections}\n";
    echo "      - Content Blocks: {$totalBlocks}\n";
    echo "      - Block Templates: {$totalBlockTemplates}\n";
    
    // 6. URLs for testing
    echo "\n6. Testing URLs:\n";
    echo "   🎨 Visual Builder: /admin/visual-template-builder?template={$testTemplate->id}\n";
    echo "   📋 Template Management: /admin/template-layouts\n";
    echo "   👁️ Preview: /visual-template/preview/{$testTemplate->id}\n";
    
    echo "\n=== Setup Complete ===\n";
    echo "🎯 Visual Builder v2.0 is ready for testing!\n";
    echo "🚀 Try drag & drop functionality\n";
    echo "⚙️ Test section and block settings\n";
    echo "👁️ Check preview functionality\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
