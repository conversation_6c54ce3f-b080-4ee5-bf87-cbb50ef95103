<?php

/**
 * Fix Block Templates Content
 * Run via: php artisan tinker
 */

echo "=== Fixing Block Templates Content ===\n";

try {
    // 1. Clear existing system blocks
    echo "1. Clearing existing system blocks...\n";

    App\Models\BlockTemplate::where('is_system', true)->delete();
    echo "   ✅ Existing system blocks cleared\n";

    // 2. Create comprehensive block templates with proper content
    echo "\n2. Creating comprehensive block templates...\n";

    $blockTemplates = [
        // HEADER BLOCKS - INDIVIDUAL FIELDS
        [
            'name' => 'Company Logo',
            'description' => 'Company logo image',
            'block_type' => 'logo',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.logo',
                'alt_text' => 'Company Logo',
                'width' => '120px',
                'height' => 'auto',
                'default_image' => '/images/default-logo.png'
            ],
            'field_mappings' => [
                'field' => 'company.logo',
                'format' => 'image'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'margin' => ['top' => 0, 'right' => 10, 'bottom' => 0, 'left' => 0]
            ],
            'preview_html' => '<div class="w-20 h-16 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500 rounded">[LOGO]</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['logo', 'company', 'header', 'image'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Name',
            'description' => 'Company name text field',
            'block_type' => 'company_name',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.name',
                'prefix' => '',
                'suffix' => '',
                'default_text' => 'PT. Sample Company'
            ],
            'field_mappings' => [
                'field' => 'company.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 18,
                'font_weight' => 'bold',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="text-lg font-bold text-gray-800">PT. Sample Company</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'name', 'header', 'text'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Address',
            'description' => 'Company address text field',
            'block_type' => 'company_address',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.address',
                'prefix' => '',
                'suffix' => '',
                'default_text' => 'Jl. Sample Street No. 123, Jakarta 12345'
            ],
            'field_mappings' => [
                'field' => 'company.address',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Jl. Sample Street No. 123, Jakarta 12345</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'address', 'header', 'text'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Phone',
            'description' => 'Company phone number field',
            'block_type' => 'company_phone',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.phone',
                'prefix' => 'Phone: ',
                'suffix' => '',
                'default_text' => '+62 21 1234 5678'
            ],
            'field_mappings' => [
                'field' => 'company.phone',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Phone: +62 21 1234 5678</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'phone', 'header', 'contact'],
            'usage_count' => 0
        ],
        [
            'name' => 'Company Email',
            'description' => 'Company email address field',
            'block_type' => 'company_email',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'company.email',
                'prefix' => 'Email: ',
                'suffix' => '',
                'default_text' => '<EMAIL>'
            ],
            'field_mappings' => [
                'field' => 'company.email',
                'format' => 'email'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-sm text-gray-600">Email: <span class="text-blue-600"><EMAIL></span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'email', 'header', 'contact'],
            'usage_count' => 0
        ],

        // COMBINED BLOCKS
        [
            'name' => 'Company Info Combined',
            'description' => 'Combined company name, address, phone, email in one block',
            'block_type' => 'company_info_combined',
            'category' => 'header',
            'template_data' => [
                'fields' => [
                    [
                        'path' => 'company.name',
                        'format' => 'text',
                        'prefix' => '',
                        'suffix' => '',
                        'style' => 'font-weight: bold; font-size: 18px; color: #000;'
                    ],
                    [
                        'path' => 'company.address',
                        'format' => 'text',
                        'prefix' => '',
                        'suffix' => '',
                        'style' => 'font-size: 12px; color: #666; margin-top: 4px;'
                    ],
                    [
                        'path' => 'company.phone',
                        'format' => 'text',
                        'prefix' => 'Phone: ',
                        'suffix' => '',
                        'style' => 'font-size: 12px; color: #666; margin-top: 2px;'
                    ],
                    [
                        'path' => 'company.email',
                        'format' => 'email',
                        'prefix' => 'Email: ',
                        'suffix' => '',
                        'style' => 'font-size: 12px; color: #666; margin-top: 2px;'
                    ]
                ],
                'layout' => 'vertical',
                'spacing' => 'compact'
            ],
            'field_mappings' => [
                'fields' => ['company.name', 'company.address', 'company.phone', 'company.email'],
                'format' => 'multi_field'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'line_height' => 1.4
            ],
            'preview_html' => '<div class="space-y-1"><div class="font-bold text-lg text-gray-800">PT. Sample Company</div><div class="text-sm text-gray-600">Jl. Sample Street No. 123, Jakarta</div><div class="text-sm text-gray-600">Phone: +62 21 1234 5678</div><div class="text-sm text-gray-600">Email: <span class="text-blue-600"><EMAIL></span></div></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['company', 'info', 'header', 'combined'],
            'usage_count' => 0
        ],

        // INVOICE FIELDS
        [
            'name' => 'Invoice Number',
            'description' => 'Invoice number field',
            'block_type' => 'invoice_number',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'invoice.number',
                'prefix' => 'Invoice #: ',
                'suffix' => '',
                'default_text' => 'INV-2024-001'
            ],
            'field_mappings' => [
                'field' => 'invoice.number',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 14,
                'font_weight' => 'bold',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="text-right font-bold text-gray-800">Invoice #: INV-2024-001</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'number', 'header'],
            'usage_count' => 0
        ],
        [
            'name' => 'Invoice Date',
            'description' => 'Invoice date field',
            'block_type' => 'invoice_date',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'invoice.date',
                'prefix' => 'Date: ',
                'suffix' => '',
                'default_text' => '01/01/2024'
            ],
            'field_mappings' => [
                'field' => 'invoice.date',
                'format' => 'date'
            ],
            'default_styling' => [
                'alignment' => 'right',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#666666'
            ],
            'preview_html' => '<div class="text-right text-sm text-gray-600">Date: 01/01/2024</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['invoice', 'date', 'header'],
            'usage_count' => 0
        ],

        // CLIENT FIELDS
        [
            'name' => 'Client Name',
            'description' => 'Client company name field',
            'block_type' => 'client_name',
            'category' => 'header',
            'template_data' => [
                'field_path' => 'client.name',
                'prefix' => 'Bill To: ',
                'suffix' => '',
                'default_text' => 'Sample Client Corp'
            ],
            'field_mappings' => [
                'field' => 'client.name',
                'format' => 'text'
            ],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 14,
                'font_weight' => 'bold',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="font-bold text-gray-800">Bill To: Sample Client Corp</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['client', 'name', 'header', 'billto'],
            'usage_count' => 0
        ],

        // UTILITY BLOCKS
        [
            'name' => 'Free Text Block',
            'description' => 'Fully customizable text content with HTML support',
            'block_type' => 'free_text',
            'category' => 'general',
            'template_data' => [
                'text' => 'Enter your custom text here. You can use <strong>HTML</strong> formatting.',
                'allow_html' => true,
                'editable' => true,
                'placeholder' => 'Click to edit this text...'
            ],
            'field_mappings' => [],
            'default_styling' => [
                'alignment' => 'left',
                'font_size' => 12,
                'font_weight' => 'normal',
                'text_color' => '#000000'
            ],
            'preview_html' => '<div class="border-2 border-dashed border-blue-300 bg-blue-50 p-3 text-blue-700 text-sm rounded"><span class="font-medium">✏️ Free Text Block</span><br><span class="text-xs">Click to edit custom content</span></div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['text', 'custom', 'editable', 'free', 'html'],
            'usage_count' => 0
        ],
        [
            'name' => 'Spacer',
            'description' => 'Empty space for layout spacing',
            'block_type' => 'spacer',
            'category' => 'general',
            'template_data' => [
                'height' => '20px',
                'show_border' => false,
                'adjustable' => true
            ],
            'field_mappings' => [],
            'default_styling' => [
                'min_height' => 20,
                'max_height' => 100
            ],
            'preview_html' => '<div class="h-5 border-b border-dashed border-gray-300 flex items-center justify-center text-xs text-gray-400 bg-gray-50 rounded">--- spacer (20px) ---</div>',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['spacer', 'layout', 'spacing'],
            'usage_count' => 0
        ],
        [
            'name' => 'Horizontal Line',
            'description' => 'Horizontal divider line',
            'block_type' => 'horizontal_line',
            'category' => 'general',
            'template_data' => [
                'thickness' => '1px',
                'color' => '#cccccc',
                'style' => 'solid',
                'width' => '100%'
            ],
            'field_mappings' => [],
            'default_styling' => [
                'margin' => ['top' => 10, 'bottom' => 10]
            ],
            'preview_html' => '<hr class="border-gray-300 my-2">',
            'is_system' => true,
            'is_active' => true,
            'tags' => ['line', 'divider', 'separator', 'hr'],
            'usage_count' => 0
        ]
    ];

    $createdBlocks = [];
    foreach ($blockTemplates as $blockData) {
        $block = App\Models\BlockTemplate::create($blockData);
        $createdBlocks[] = $block;
        echo "   ✅ Created: {$block->name} ({$block->block_type})\n";
    }

    // 3. Verification
    echo "\n3. Verification...\n";

    $totalBlocks = App\Models\BlockTemplate::where('is_system', true)->count();
    $categories = App\Models\BlockTemplate::where('is_system', true)->distinct('category')->pluck('category');

    echo "   📊 Total blocks created: {$totalBlocks}\n";
    echo "   📦 Categories: " . $categories->implode(', ') . "\n";

    foreach ($categories as $category) {
        $count = App\Models\BlockTemplate::where('is_system', true)->where('category', $category)->count();
        echo "      - {$category}: {$count} blocks\n";
    }

    // 4. Test content rendering
    echo "\n4. Testing content rendering...\n";

    $testData = [
        'company' => [
            'logo' => '/images/company-logo.png',
            'name' => 'PT. Test Company Indonesia',
            'address' => 'Jl. Sudirman No. 123, Jakarta Pusat 10220',
            'phone' => '+62 21 5555 1234',
            'email' => '<EMAIL>'
        ],
        'invoice' => [
            'number' => 'INV-2024-0001',
            'date' => '2024-01-15',
            'due_date' => '2024-02-15'
        ],
        'client' => [
            'name' => 'PT. Client Corporation',
            'address' => 'Jl. Client Street No. 456, Surabaya 60123'
        ]
    ];

    foreach ($createdBlocks as $block) {
        if (in_array($block->block_type, ['logo', 'company_name', 'free_text'])) {
            echo "   🧪 Testing {$block->name}:\n";

            // Create temporary content block for testing
            $tempSection = App\Models\LayoutSection::first();
            if ($tempSection) {
                $contentBlock = $block->createContentBlock($tempSection->id, [
                    'column_position' => 1,
                    'order' => 0
                ]);
                
                $renderedHtml = $contentBlock->renderHtml($testData);
                $cleanText = strip_tags($renderedHtml);
                echo "      📄 Rendered: " . Str::limit($cleanText, 50) . "\n";

                // Clean up
                $contentBlock->delete();
            }
        }
    }

    // 5. Create test template with proper content
    echo "\n5. Creating test template with content...\n";

    $testTemplate = App\Models\TemplateLayout::updateOrCreate(
        ['name' => 'Content Test Template'],
        [
            'description' => 'Template with proper block content for testing',
            'company_id' => null,
            'version' => 'v2',
            'created_by' => 1,
        ]
    );

    // Clear existing sections
    App\Models\LayoutSection::where('template_layout_id', $testTemplate->id)->delete();

    // Create header section with 3 columns
    $headerSection = App\Models\LayoutSection::create([
        'template_layout_id' => $testTemplate->id,
        'section_type' => 'header',
        'section_name' => 'Header Section',
        'column_count' => 3,
        'column_layout' => 'custom',
        'column_widths' => [25, 50, 25],
        'order' => 0,
        'is_active' => true,
        'min_height' => 120,
    ]);

    // Add blocks to header
    $logoBlock = App\Models\BlockTemplate::where('block_type', 'logo')->first();
    $companyInfoBlock = App\Models\BlockTemplate::where('block_type', 'company_info_combined')->first();
    $invoiceNumberBlock = App\Models\BlockTemplate::where('block_type', 'invoice_number')->first();

    if ($logoBlock) {
        $logoBlock->createContentBlock($headerSection->id, ['column_position' => 1]);
        echo "   ✅ Logo added to column 1\n";
    }

    if ($companyInfoBlock) {
        $companyInfoBlock->createContentBlock($headerSection->id, ['column_position' => 2]);
        echo "   ✅ Company info added to column 2\n";
    }

    if ($invoiceNumberBlock) {
        $invoiceNumberBlock->createContentBlock($headerSection->id, ['column_position' => 3]);
        echo "   ✅ Invoice number added to column 3\n";
    }

    echo "\n=== Block Templates Content Fix Complete ===\n";
    echo "✅ Created {$totalBlocks} block templates with proper content\n";
    echo "✅ Individual field blocks: Logo, Name, Address, Phone, Email\n";
    echo "✅ Combined blocks: Company Info Combined\n";
    echo "✅ Utility blocks: Free Text, Spacer, Horizontal Line\n";
    echo "✅ Test template created with sample layout\n";

    $builderUrl = "/admin/visual-template-builder?template={$testTemplate->id}";
    echo "\n🚀 Test the Visual Builder: {$builderUrl}\n";

    echo "\n📋 What to test:\n";
    echo "1. Section settings (change column count, widths)\n";
    echo "2. Drag & drop blocks from palette\n";
    echo "3. Block settings (fonts, colors, alignment)\n";
    echo "4. Content rendering with real data\n";
    echo "5. Free text block editing\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
