<x-filament-panels::page>
    <div class="visual-template-builder" x-data="visualBuilder()" x-init="init()">

        {{-- Header Controls --}}
        <div class="builder-header bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    {{-- Template Info --}}
                    @if($template)
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="font-medium text-gray-900">{{ $template->name }}</span>
                            <span class="text-sm text-gray-500">v2.0</span>
                        </div>
                    @else
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                            <span class="font-medium text-gray-500">No Template Selected</span>
                        </div>
                    @endif
                </div>

                <div class="flex items-center space-x-4">
                    {{-- Preview Mode Toggle --}}
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Preview:</label>
                        <select wire:model.live="previewMode" class="text-sm border-gray-300 rounded-md">
                            <option value="desktop">Desktop</option>
                            <option value="tablet">Tablet</option>
                            <option value="mobile">Mobile</option>
                        </select>
                    </div>

                    {{-- Page Size Toggle --}}
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Page:</label>
                        <select wire:model.live="pageSize" class="text-sm border-gray-300 rounded-md">
                            <option value="legal">Legal</option>
                            <option value="f4">F4/Folio</option>
                        </select>
                    </div>

                    {{-- Auto-save indicator --}}
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-xs text-gray-500">Auto-saving</span>
                    </div>
                </div>
            </div>
        </div>

        {{-- Main Builder Interface - True 2 Column Side by Side Layout --}}
        <div class="builder-interface flex gap-4 h-[calc(100vh-200px)]">

            {{-- Left Column: Block Palette (Compact Width) --}}
            <div class="blocks-column w-64 flex-shrink-0">
                <div class="block-palette bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
                    <div class="p-4 border-b border-gray-200 flex-shrink-0">
                        <h3 class="font-semibold text-gray-900">Content Blocks</h3>
                        <p class="text-sm text-gray-500 mt-1">Drag blocks to canvas →</p>
                    </div>

                    <div class="flex-1 overflow-y-auto p-3">
                        <div class="space-y-3">
                            @foreach($availableBlocks as $category)
                                <div class="block-category">
                                    <h4 class="font-medium text-gray-700 mb-2 sticky top-0 bg-white py-1 border-b border-gray-100 text-xs uppercase tracking-wide">
                                        {{ $category['label'] }}
                                    </h4>
                                    <div class="space-y-1">
                                        @foreach($category['blocks'] as $block)
                                            <div class="block-item p-2 border border-gray-200 rounded cursor-move hover:border-blue-300 hover:bg-blue-50 hover:shadow-sm transition-all duration-200"
                                                 draggable="true"
                                                 x-data="{ blockId: {{ $block['id'] }} }"
                                                 @dragstart="startDrag($event, blockId)">
                                                <div class="flex items-center space-x-2">
                                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded flex items-center justify-center flex-shrink-0">
                                                        <span class="text-xs font-bold text-blue-700">{{ strtoupper(substr($block['block_type'], 0, 2)) }}</span>
                                                    </div>
                                                    <div class="flex-1 min-w-0">
                                                        <p class="text-xs font-medium text-gray-900 truncate">{{ $block['name'] }}</p>
                                                        <p class="text-xs text-gray-500 truncate">{{ Str::limit($block['description'], 25) }}</p>
                                                    </div>
                                                    <div class="text-gray-400 flex-shrink-0">
                                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            {{-- Right Column: Canvas Area (Flexible Width) --}}
            <div class="canvas-column flex-1 min-w-0">
                <div class="canvas-container bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden flex flex-col">
                    <div class="canvas-header p-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900">Template Canvas</h3>
                            <div class="flex items-center space-x-2">
                                <button type="button"
                                        class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                                        wire:click="addSection('header')">
                                    + Header
                                </button>
                                <button type="button"
                                        class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
                                        wire:click="addSection('body')">
                                    + Body
                                </button>
                                <button type="button"
                                        class="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors"
                                        wire:click="addSection('footer')">
                                    + Footer
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="canvas-content p-4 overflow-y-auto flex-1">
                        @if($template && $template->sections->count() > 0)
                            {{-- Render existing sections --}}
                            @foreach($template->sections->groupBy('section_type') as $sectionType => $sections)
                                <div class="section-group mb-6">
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
                                        {{ ucfirst($sectionType) }} Sections
                                    </h4>

                                    @foreach($sections as $section)
                                        <div class="template-section border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4 hover:border-blue-400 transition-colors"
                                             x-data="{
                                                sectionId: {{ $section->id }},
                                                isDragOver: false
                                             }"
                                             @dragover.prevent="isDragOver = true"
                                             @dragleave="isDragOver = false"
                                             @drop="dropBlock($event, sectionId); isDragOver = false"
                                             :class="{ 'border-blue-500 bg-blue-50': isDragOver }">

                                            <div class="section-header flex items-center justify-between mb-3">
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-sm font-medium text-gray-700">
                                                        {{ $section->section_name ?: ucfirst($section->section_type) . ' Section' }}
                                                    </span>
                                                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                                        {{ $section->column_count }} {{ $section->column_count === 1 ? 'column' : 'columns' }}
                                                    </span>
                                                    <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                                        {{ $section->blocks->count() }} blocks
                                                    </span>
                                                </div>
                                                <div class="flex items-center space-x-1">
                                                    <button type="button"
                                                            class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                                            @click="openSectionSettings({{ $section->id }})"
                                                            title="Section Settings">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        </svg>
                                                    </button>
                                                    <button type="button"
                                                            class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                                                            wire:click="deleteSection({{ $section->id }})"
                                                            onclick="return confirm('Are you sure you want to delete this section?')"
                                                            title="Delete Section">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>

                                            {{-- Section columns --}}
                                            <div class="section-columns grid gap-4"
                                                 style="grid-template-columns: repeat({{ $section->column_count }}, 1fr);">
                                                @for($col = 1; $col <= $section->column_count; $col++)
                                                    <div class="section-column border border-gray-200 rounded p-3 min-h-[120px] bg-gray-50"
                                                         x-data="{ columnId: {{ $col }} }"
                                                         @dragover.prevent
                                                         @drop="dropBlockToColumn($event, sectionId, columnId)">
                                                        <div class="text-xs text-gray-500 mb-2 font-medium">Column {{ $col }}</div>

                                                        {{-- Render blocks in this column --}}
                                                        @foreach($section->blocks->where('column_position', $col) as $block)
                                                            <div class="content-block bg-white border border-gray-200 rounded p-3 mb-2 shadow-sm hover:shadow-md transition-shadow"
                                                                 x-data="{ blockId: {{ $block->id }} }">
                                                                <div class="flex items-center justify-between mb-2">
                                                                    <span class="text-xs font-medium text-gray-700">{{ $block->block_name ?: ucfirst($block->block_type) }}</span>
                                                                    <div class="flex items-center space-x-1">
                                                                        <button type="button"
                                                                                class="p-1 text-gray-400 hover:text-blue-600 rounded"
                                                                                @click="openBlockSettings(blockId)"
                                                                                title="Block Settings">
                                                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                            </svg>
                                                                        </button>
                                                                        <button type="button"
                                                                                class="p-1 text-gray-400 hover:text-red-600 rounded"
                                                                                wire:click="deleteBlock({{ $block->id }})"
                                                                                onclick="return confirm('Remove this block?')"
                                                                                title="Remove Block">
                                                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                                            </svg>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <div class="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                                                    <div class="truncate">{{ $block->block_type }} block</div>
                                                                </div>
                                                            </div>
                                                        @endforeach

                                                        {{-- Drop zone --}}
                                                        <div class="drop-zone border-2 border-dashed border-gray-300 rounded p-4 text-center text-gray-500 text-xs hover:border-blue-400 hover:text-blue-600 hover:bg-blue-50 transition-all"
                                                             x-show="!isDragOver || $el.parentElement.children.length === 2">
                                                            <svg class="w-6 h-6 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                            </svg>
                                                            Drop blocks here
                                                        </div>
                                                    </div>
                                                @endfor
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endforeach
                        @else
                            {{-- Empty state --}}
                            <div class="empty-canvas text-center py-12">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Template</h3>
                                <p class="text-gray-500 mb-4">Add sections to begin creating your invoice template</p>
                                <div class="flex justify-center space-x-2">
                                    <button type="button"
                                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                            wire:click="addSection('header')">
                                        Add Header Section
                                    </button>
                                    <button type="button"
                                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                                            wire:click="addSection('body')">
                                        Add Body Section
                                    </button>
                                    <button type="button"
                                            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                                            wire:click="addSection('footer')">
                                        Add Footer Section
                                    </button>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

        </div>

        {{-- Properties Modal (Slide-in from right) --}}
        <div x-show="showPropertiesModal"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-50 overflow-hidden"
             style="display: none;">

            {{-- Backdrop --}}
            <div class="absolute inset-0 bg-black bg-opacity-50" @click="closePropertiesModal()"></div>

            {{-- Modal Panel --}}
            <div class="absolute right-0 top-0 h-full w-96 bg-white shadow-xl"
                 x-show="showPropertiesModal"
                 x-transition:enter="transition ease-out duration-300 transform"
                 x-transition:enter-start="translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transition ease-in duration-200 transform"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="translate-x-full">

                {{-- Modal Header --}}
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <h3 class="text-lg font-semibold text-gray-900" x-text="propertiesTitle">Properties</h3>
                            {{-- Auto-save indicator --}}
                            <div class="flex items-center space-x-1 text-xs text-gray-500">
                                <svg wire:loading.remove wire:target="autoSaveSectionSettings" class="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <svg wire:loading wire:target="autoSaveSectionSettings" class="w-3 h-3 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span wire:loading.remove wire:target="autoSaveSectionSettings">Auto-saved</span>
                                <span wire:loading wire:target="autoSaveSectionSettings">Saving...</span>
                            </div>
                        </div>
                        <button type="button"
                                class="p-2 text-gray-400 hover:text-gray-600 rounded"
                                @click="closePropertiesModal()">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                {{-- Modal Content --}}
                <div class="p-4 overflow-y-auto h-full">
                    <div x-show="propertiesType === 'section'">
                        <h4 class="font-medium text-gray-900 mb-3">Section Settings</h4>
                        <form wire:submit.prevent="updateSectionSettings">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Section Name</label>
                                    <input type="text"
                                           wire:model.live="sectionSettings.section_name"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           placeholder="Custom section name">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Columns</label>
                                    <select wire:model.live="sectionSettings.column_count"
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="1">1 Column</option>
                                        <option value="2">2 Columns</option>
                                        <option value="3">3 Columns</option>
                                        <option value="4">4 Columns</option>
                                    </select>
                                </div>
                                <div x-show="$wire.sectionSettings.column_count > 1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Column Layout</label>
                                    <select wire:model.live="sectionSettings.column_layout"
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="equal">Equal Width</option>
                                        <option value="custom">Custom Width</option>
                                    </select>
                                </div>
                                <div x-show="$wire.sectionSettings.column_layout === 'custom' && $wire.sectionSettings.column_count > 1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Column Widths (%)</label>
                                    <div class="text-xs text-gray-500 mb-2">Enter percentages for each column (must total 100%)</div>
                                    <template x-for="i in parseInt($wire.sectionSettings.column_count)" :key="i">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <span class="text-sm w-16" x-text="'Column ' + i + ':'"></span>
                                            <input type="number"
                                                   x-model="$wire.sectionSettings.column_widths[i-1]"
                                                   class="flex-1 border border-gray-300 rounded-md px-2 py-1 text-sm"
                                                   min="5" max="95" placeholder="25">
                                            <span class="text-sm text-gray-500">%</span>
                                        </div>
                                    </template>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
                                    <input type="color"
                                           wire:model.live="sectionSettings.background_color"
                                           class="w-full h-10 border border-gray-300 rounded-md">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Minimum Height (px)</label>
                                    <input type="number"
                                           wire:model.live="sectionSettings.min_height"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           min="50" max="500" placeholder="120">
                                </div>
                            </div>
                        </form>
                    </div>

                    <div x-show="propertiesType === 'block'">
                        <h4 class="font-medium text-gray-900 mb-3">Block Settings</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Block Name</label>
                                <input type="text"
                                       wire:model="blockSettings.block_name"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       placeholder="Custom block name">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Alignment</label>
                                <select wire:model="blockSettings.alignment"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="left">Left</option>
                                    <option value="center">Center</option>
                                    <option value="right">Right</option>
                                    <option value="justify">Justify</option>
                                </select>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
                                    <input type="number"
                                           wire:model="blockSettings.font_size"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           min="8" max="24" placeholder="12">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Font Weight</label>
                                    <select wire:model="blockSettings.font_weight"
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="normal">Normal</option>
                                        <option value="bold">Bold</option>
                                        <option value="lighter">Light</option>
                                    </select>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Font Color</label>
                                <div class="flex items-center space-x-2">
                                    <input type="color"
                                           wire:model="blockSettings.font_color"
                                           class="w-12 h-10 border border-gray-300 rounded-md">
                                    <input type="text"
                                           wire:model="blockSettings.font_color"
                                           class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm"
                                           placeholder="#000000">
                                </div>
                            </div>
                            <div x-show="propertiesType === 'block' && ['custom_text'].includes(selectedBlockType)">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Custom Text</label>
                                <textarea wire:model="blockSettings.custom_text"
                                          class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                          rows="3"
                                          placeholder="Enter custom text content"></textarea>
                            </div>
                            <div x-show="propertiesType === 'block' && ['spacer'].includes(selectedBlockType)">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Spacer Height (px)</label>
                                <input type="number"
                                       wire:model="blockSettings.spacer_height"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                       min="5" max="100" placeholder="20">
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Modal Footer --}}
                <div class="p-4 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <div class="text-xs text-gray-500">
                            <span x-show="propertiesType === 'section'">Changes are saved automatically</span>
                            <span x-show="propertiesType === 'block'">Block settings</span>
                        </div>
                        <div class="flex space-x-2">
                            {{-- Manual save button (optional, for blocks or manual trigger) --}}
                            <button type="button"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                                    wire:click="updateBlockSettings"
                                    x-show="propertiesType === 'block'">
                                Save Block
                            </button>
                            <button type="button"
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm"
                                    @click="closePropertiesModal()">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Alpine.js Component --}}
    <script>
        function visualBuilder() {
            return {
                draggedBlockId: null,
                showPropertiesModal: false,
                propertiesType: null,
                propertiesTitle: 'Properties',
                selectedElementId: null,

                startDrag(event, blockId) {
                    this.draggedBlockId = blockId;
                    event.dataTransfer.effectAllowed = 'move';
                    event.dataTransfer.setData('text/plain', blockId);

                    // Add visual feedback
                    event.target.style.opacity = '0.5';
                    console.log('Started dragging block:', blockId);
                },

                dropBlock(event, sectionId) {
                    event.preventDefault();

                    if (this.draggedBlockId) {
                        console.log('Dropping block', this.draggedBlockId, 'to section', sectionId);

                        // Call Livewire method to add block to section
                        @this.call('addBlockToSection', this.draggedBlockId, sectionId, 1)
                            .then(() => {
                                console.log('Block added successfully');
                                this.draggedBlockId = null;
                            })
                            .catch((error) => {
                                console.error('Error adding block:', error);
                                this.draggedBlockId = null;
                            });
                    }
                },

                dropBlockToColumn(event, sectionId, columnId) {
                    event.preventDefault();
                    event.stopPropagation();

                    if (this.draggedBlockId) {
                        console.log('Dropping block', this.draggedBlockId, 'to section', sectionId, 'column', columnId);

                        // Call Livewire method to add block to specific column
                        @this.call('addBlockToSection', this.draggedBlockId, sectionId, columnId)
                            .then(() => {
                                console.log('Block added to column successfully');
                                this.draggedBlockId = null;
                            })
                            .catch((error) => {
                                console.error('Error adding block to column:', error);
                                this.draggedBlockId = null;
                            });
                    }
                },

                openSectionSettings(sectionId) {
                    console.log('🔧 Opening section settings for:', sectionId);
                    this.selectedElementId = sectionId;
                    this.propertiesType = 'section';
                    this.propertiesTitle = 'Section Settings';

                    console.log('🎯 Properties type set to:', this.propertiesType);
                    console.log('🎯 Should show save button:', this.propertiesType === 'section');

                    // Load section settings from server
                    @this.set('selectedElementId', sectionId);
                    @this.call('loadSectionSettings', sectionId)
                        .then(() => {
                            console.log('✅ Section settings loaded');
                            console.log('📊 Current sectionSettings:', @this.sectionSettings);
                            console.log('🎯 Properties type after load:', this.propertiesType);
                        })
                        .catch((error) => {
                            console.error('❌ Error loading section settings:', error);
                        });

                    this.showPropertiesModal = true;
                },

                openBlockSettings(blockId) {
                    this.selectedElementId = blockId;
                    this.propertiesType = 'block';
                    this.propertiesTitle = 'Block Settings';
                    this.showPropertiesModal = true;
                    console.log('Opening block settings for:', blockId);
                },

                closePropertiesModal() {
                    this.showPropertiesModal = false;
                    this.propertiesType = null;
                    this.selectedElementId = null;
                    this.propertiesTitle = 'Properties';
                },

                init() {
                    console.log('🚀 Visual Builder v2.0 initialized');

                    // Listen for close modal event
                    window.addEventListener('close-properties-modal', () => {
                        this.closePropertiesModal();
                    });

                    // Debug Livewire events
                    this.$wire.on('section-updated', (data) => {
                        console.log('✅ Section auto-saved:', data);
                        if (data.auto_saved) {
                            // Show subtle success feedback
                            this.showAutoSaveSuccess();
                        }
                    });

                    this.$wire.on('section-update-failed', (data) => {
                        console.error('❌ Section auto-save failed:', data);
                        if (data.auto_save) {
                            // Show subtle error feedback
                            this.showAutoSaveError(data.error);
                        }
                    });
                },

                showAutoSaveSuccess() {
                    // Subtle success feedback - could add a brief green flash or checkmark
                    console.log('💚 Auto-save successful');
                },

                showAutoSaveError(error) {
                    // Subtle error feedback - could add a brief red flash
                    console.error('💔 Auto-save failed:', error);
                },

                saveSectionSettings() {
                    console.log('💾 Manual save triggered...');
                    console.log('📊 Current sectionSettings:', @this.sectionSettings);
                    console.log('🎯 Selected element ID:', this.selectedElementId);

                    @this.call('updateSectionSettings')
                        .then(() => {
                            console.log('✅ Section settings saved successfully');
                        })
                        .catch((error) => {
                            console.error('❌ Error saving section settings:', error);
                        });
                },

                saveProperties() {
                    console.log('Saving properties for:', this.propertiesType, this.selectedElementId);
                    // Here you would collect form data and call Livewire method
                    // @this.call('updateElementProperties', this.selectedElementId, formData);
                    this.closePropertiesModal();
                },

                addSection(sectionType) {
                    console.log('Adding section:', sectionType);
                    @this.call('addSection', sectionType);
                }
            }
        }

        // Global drag event handlers
        document.addEventListener('dragend', function(event) {
            // Reset opacity after drag
            event.target.style.opacity = '';
        });

        document.addEventListener('dragover', function(event) {
            event.preventDefault();
        });
    </script>
</x-filament-panels::page>
